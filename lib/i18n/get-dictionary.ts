/**
 * 字典加载工具
 * 用于动态加载不同语言的翻译字典
 */

import { Locale, defaultLocale, locales } from './config';
import { Dictionary } from './types';

/**
 * 字典加载器映射
 * 为每种支持的语言提供一个加载函数
 */
const dictionaries: Record<Locale, () => Promise<Dictionary>> = {
  en: () => import('./dictionaries/en.json').then((module) => module.default as Dictionary),
  zh: () => import('./dictionaries/zh.json').then((module) => module.default as Dictionary),
  tw: () => import('./dictionaries/tw.json').then((module) => module.default as Dictionary),
  ko: () => import('./dictionaries/ko.json').then((module) => module.default as Dictionary),
  ja: () => import('./dictionaries/ja.json').then((module) => module.default as Dictionary),
  pt: () => import('./dictionaries/pt.json').then((module) => module.default as Dictionary),
  es: () => import('./dictionaries/es.json').then((module) => module.default as Dictionary),
  de: () => import('./dictionaries/de.json').then((module) => module.default as Dictionary),
  fr: () => import('./dictionaries/fr.json').then((module) => module.default as Dictionary),
  vi: () => import('./dictionaries/vi.json').then((module) => module.default as Dictionary),
};

/**
 * 验证语言代码是否受支持
 * @param locale 语言代码
 * @returns 是否受支持
 */
export function isValidLocale(locale: string): locale is Locale {
  return locales.includes(locale as Locale);
}

/**
 * 获取指定语言的翻译字典
 * @param locale 语言代码
 * @returns 翻译字典
 */
export async function getDictionary(locale: Locale = defaultLocale): Promise<Dictionary> {
  try {
    // 确保语言代码有效
    const validLocale = isValidLocale(locale) ? locale : defaultLocale;

    // 加载字典
    return await dictionaries[validLocale]();
  } catch (error) {
    console.error(`Failed to load dictionary for locale: ${locale}`, error);

    // 如果不是默认语言，则尝试加载默认语言的字典
    if (locale !== defaultLocale) {
      try {
        return await dictionaries[defaultLocale]();
      } catch (fallbackError) {
        console.error(`Failed to load fallback dictionary for locale: ${defaultLocale}`, fallbackError);
      }
    }

    // 如果所有尝试都失败，返回一个空字典
    return {} as Dictionary;
  }
}