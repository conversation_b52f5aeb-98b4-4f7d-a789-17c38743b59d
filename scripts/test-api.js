#!/usr/bin/env node

/**
 * API 测试脚本
 * 用于测试工具提交和管理 API 功能
 */

const https = require('https');
const http = require('http');

// 配置
const config = {
  baseUrl: process.env.API_BASE_URL || 'http://localhost:8787',
  adminApiKey: process.env.ADMIN_API_KEY || 'test-admin-key',
};

// HTTP 请求工具函数
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const protocol = options.protocol === 'https:' ? https : http;
    
    const req = protocol.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          const result = {
            statusCode: res.statusCode,
            headers: res.headers,
            body: body ? JSON.parse(body) : null
          };
          resolve(result);
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: body
          });
        }
      });
    });

    req.on('error', reject);

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

// 解析 URL
function parseUrl(url) {
  const urlObj = new URL(url);
  return {
    protocol: urlObj.protocol,
    hostname: urlObj.hostname,
    port: urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80),
    path: urlObj.pathname + urlObj.search
  };
}

// 测试工具提交
async function testSubmitTool() {
  console.log('🧪 Testing tool submission...');
  
  const testData = {
    name: 'Test AI Tool',
    description: 'This is a test AI tool for automated testing purposes. It demonstrates the submission functionality.',
    website: 'https://example.com',
    category: 'productivity',
    email: '<EMAIL>',
    additionalInfo: 'This is additional information for testing.',
    locale: 'en',
    submittedAt: new Date().toISOString()
  };

  try {
    const url = parseUrl(`${config.baseUrl}/api/submit-tool`);
    const options = {
      ...url,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    };

    const response = await makeRequest(options, testData);
    
    if (response.statusCode === 200 && response.body.success) {
      console.log('✅ Tool submission successful');
      console.log('   Submission ID:', response.body.submissionId);
      return response.body.submissionId;
    } else {
      console.log('❌ Tool submission failed');
      console.log('   Status:', response.statusCode);
      console.log('   Response:', response.body);
      return null;
    }
  } catch (error) {
    console.log('❌ Tool submission error:', error.message);
    return null;
  }
}

// 测试获取提交列表
async function testGetSubmissions() {
  console.log('🧪 Testing get submissions...');
  
  try {
    const url = parseUrl(`${config.baseUrl}/api/admin/submissions`);
    const options = {
      ...url,
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${config.adminApiKey}`,
      }
    };

    const response = await makeRequest(options);
    
    if (response.statusCode === 200 && response.body.success) {
      console.log('✅ Get submissions successful');
      console.log('   Found submissions:', response.body.data.length);
      return response.body.data;
    } else {
      console.log('❌ Get submissions failed');
      console.log('   Status:', response.statusCode);
      console.log('   Response:', response.body);
      return null;
    }
  } catch (error) {
    console.log('❌ Get submissions error:', error.message);
    return null;
  }
}

// 测试获取统计信息
async function testGetStats() {
  console.log('🧪 Testing get statistics...');
  
  try {
    const url = parseUrl(`${config.baseUrl}/api/admin/submissions?action=stats`);
    const options = {
      ...url,
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${config.adminApiKey}`,
      }
    };

    const response = await makeRequest(options);
    
    if (response.statusCode === 200 && response.body.success) {
      console.log('✅ Get statistics successful');
      console.log('   Stats:', response.body.data);
      return response.body.data;
    } else {
      console.log('❌ Get statistics failed');
      console.log('   Status:', response.statusCode);
      console.log('   Response:', response.body);
      return null;
    }
  } catch (error) {
    console.log('❌ Get statistics error:', error.message);
    return null;
  }
}

// 测试更新提交状态
async function testUpdateSubmission(submissionId) {
  if (!submissionId) {
    console.log('⏭️  Skipping update test (no submission ID)');
    return;
  }

  console.log('🧪 Testing update submission status...');
  
  const updateData = {
    id: submissionId,
    status: 'approved',
    reviewedBy: 'test-admin',
    reviewNotes: 'Approved during automated testing'
  };

  try {
    const url = parseUrl(`${config.baseUrl}/api/admin/submissions`);
    const options = {
      ...url,
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${config.adminApiKey}`,
        'Content-Type': 'application/json',
      }
    };

    const response = await makeRequest(options, updateData);
    
    if (response.statusCode === 200 && response.body.success) {
      console.log('✅ Update submission successful');
      return true;
    } else {
      console.log('❌ Update submission failed');
      console.log('   Status:', response.statusCode);
      console.log('   Response:', response.body);
      return false;
    }
  } catch (error) {
    console.log('❌ Update submission error:', error.message);
    return false;
  }
}

// 主测试函数
async function runTests() {
  console.log('🚀 Starting API tests...');
  console.log('📍 Base URL:', config.baseUrl);
  console.log('');

  // 测试工具提交
  const submissionId = await testSubmitTool();
  console.log('');

  // 测试获取提交列表
  await testGetSubmissions();
  console.log('');

  // 测试获取统计信息
  await testGetStats();
  console.log('');

  // 测试更新提交状态
  await testUpdateSubmission(submissionId);
  console.log('');

  console.log('🏁 Tests completed!');
}

// 运行测试
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  testSubmitTool,
  testGetSubmissions,
  testGetStats,
  testUpdateSubmission,
  runTests
};
