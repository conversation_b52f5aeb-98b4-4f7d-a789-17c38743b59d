'use client';

import Link from 'next/link';
import { useState } from 'react';
import { Tool } from '@/lib/types';
import { Locale } from '@/lib/i18n/config';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Star, CheckCircle } from 'lucide-react';

interface ToolContentProps {
  tool: Tool;
  dictionary: any;
  locale: Locale;
}

export default function ToolContent({ tool, dictionary, locale }: ToolContentProps) {
  return (
    <div className="my-12">
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="w-full sm:w-auto grid grid-cols-3 sm:inline-flex gap-x-2">
          <TabsTrigger value="overview" className="text-sm">{dictionary.tool?.overview || 'Overview'}</TabsTrigger>
          <TabsTrigger value="features" className="text-sm">{dictionary.tool?.features || 'Features'}</TabsTrigger>
          <TabsTrigger value="reviews" className="text-sm">{dictionary.tool.reviews}</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="md:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle>{dictionary.tool?.about || 'About'} {tool.name}</CardTitle>
                  <CardDescription>
                    {dictionary.tool?.aboutDescription || 'Everything you need to know about this tool'}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium mb-3">{dictionary.tool?.description || 'Description'}</h3>
                    <p className="text-muted-foreground">
                      {tool.what_is}
                    </p>

                    <p className="text-muted-foreground mt-4">
                      {tool.how_to_use}
                    </p>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium mb-3">{dictionary.tool?.tags || 'Tags'}</h3>
                    <div className="flex flex-wrap gap-2">
                      {(tool.tags || []).map((tag) => (
                        <Badge key={tag} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div>
              <Card className="mb-6">
                <CardHeader>
                  <CardTitle>{dictionary.tool?.features || 'Features'}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex flex-wrap gap-2">
                    {(tool.features || []).map((feature) => (
                      <Badge key={feature} variant="secondary" className="text-xs">
                        {feature}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>{dictionary.categories?.title || 'Categories'}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex flex-wrap gap-2">
                    {(tool.groups || []).map((group) => (
                      <Link key={group.id} className="text-xs hover:underline" href={`/${locale}/category/${group.slug}`}>
                          {group.name}
                      </Link>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="features" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>{dictionary.tool?.keyFeatures || 'Key Features'}</CardTitle>
              <CardDescription>
                {dictionary.tool?.featuresDescription?.replace('{toolName}', tool.name) || `What makes ${tool.name} stand out from the competition`}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {(tool.features || []).map((feature, index) => (
                  <div key={index} className="flex items-start gap-3">
                    <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <div>
                      <h3 className="font-medium">{feature}</h3>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reviews" className="mt-6">
          <ReviewsTab tool={tool} dictionary={dictionary} />
        </TabsContent>
      </Tabs>
    </div>
  );
}

function ReviewsTab({ tool, dictionary }: { tool: Tool; dictionary: any }) {
  // This would normally fetch reviews from an API
  const [reviews] = useState([
    {
      id: '1',
      userName: 'Alex Johnson',
      userImage: '/photos/pexels-photo-614810.webp',
      rating: 5,
      title: 'Game changer for my workflow',
      content: 'This tool has completely transformed how I work. The AI capabilities are incredibly accurate and the interface is intuitive. Highly recommend!',
      date: new Date(2025, 5, 15),
      likes: 24,
    },
    {
      id: '2',
      userName: 'Sarah Miller',
      userImage: '/photos/pexels-photo-774909.jpeg',
      rating: 4,
      title: 'Great tool with minor issues',
      content: 'Overall a fantastic tool that has helped me tremendously. There are some small bugs here and there, but the development team is responsive and constantly improving the product.',
      date: new Date(2025, 3, 28),
      likes: 17,
    },
    {
      id: '3',
      userName: 'Michael Chen',
      userImage: '/photos/pexels-photo-220453.webp',
      rating: 5,
      title: 'Exceeded my expectations',
      content: 'I was skeptical at first, but this tool exceeded all my expectations. The quality of the output is incredible, and it saves me hours of work each week.',
      date: new Date(2024, 12, 22),
      likes: 32,
    },
  ]);

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>{dictionary.tool.reviews}</CardTitle>
          <CardDescription>
            {dictionary.tool?.reviewsDescription?.replace('{toolName}', tool.name) || `See what others are saying about ${tool.name}`}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {reviews.map((review) => (
              <div key={review.id} className="pb-6 border-b last:border-0">
                <div className="flex items-start justify-between mb-2">
                  <div className="flex items-center">
                    <div className="w-10 h-10 rounded-full overflow-hidden mr-3">
                      <img
                        src={review.userImage}
                        alt={review.userName}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div>
                      <h4 className="font-medium">{review.userName}</h4>
                      <div className="flex items-center mt-1">
                        {[1, 2, 3, 4, 5].map((star) => (
                          <Star
                            key={star}
                            className={`h-4 w-4 ${
                              star <= review.rating
                                ? 'text-yellow-500 fill-yellow-500'
                                : 'text-gray-300 fill-gray-300'
                            }`}
                          />
                        ))}
                      </div>
                    </div>
                  </div>
                  <span className="text-xs text-muted-foreground">
                    {review.date.toLocaleDateString()}
                  </span>
                </div>
                <h3 className="font-semibold mt-3">{review.title}</h3>
                <p className="text-muted-foreground mt-2">{review.content}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}