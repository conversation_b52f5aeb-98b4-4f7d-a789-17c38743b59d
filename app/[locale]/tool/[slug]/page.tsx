import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { getToolBySlug, getRelatedTools, getAllTools } from '@/lib/db/tools';
import { getDictionary } from '@/lib/i18n/get-dictionary';
import { Locale, i18n } from '@/lib/i18n/config';
import { Tool } from '@/lib/types';
import ToolHeader from '@/components/tool/tool-header';
import ToolContent from '@/components/tool/tool-content';
import RelatedTools from '@/components/tool/related-tools';
import { generateToolMetadata, generateToolJsonLd } from '@/lib/seo';
import ServerJsonLd from '@/components/seo/server-json-ld';
import { ToolPageAdLayout } from '@/components/ads/client-ad-wrapper';

interface ToolPageProps {
  params: Promise<{
    locale: Locale;
    slug: string;
  }>;
}

export async function generateStaticParams() {
  // 获取所有支持的语言
  const locales = i18n.locales;
  const params = [];

  // 为每种语言获取工具并生成参数
  for (const locale of locales) {
    try {
      const tools = await getAllTools(locale);

      // 为每个工具和语言组合生成参数
      for (const tool of tools) {
        // 确保工具有有效的 slug
        if (tool && tool.slug && typeof tool.slug === 'string' && tool.slug.trim()) {
          params.push({
            locale: locale,
            slug: tool.slug
          });
        }
      }

      // 如果没有工具，记录警告但不添加默认工具
      if (tools.length === 0) {
        console.warn(`No tools found for locale: ${locale}`);
      }
    } catch (error) {
      console.error(`Error fetching tools for locale ${locale}:`, error);
      // 继续处理其他语言，不中断整个构建过程
    }
  }

  // 确保至少有一些参数，避免构建失败
  if (params.length === 0) {
    console.warn('No valid tool params generated, adding fallback params');
    // 为每种语言添加一个安全的默认参数
    for (const locale of locales) {
      params.push({
        locale: locale,
        slug: 'not-found'
      });
    }
  }

  console.log(`Generated ${params.length} static params for tool pages`);
  return params;
}

export async function generateMetadata({ params }: ToolPageProps): Promise<Metadata> {
  // 在 Next.js 15+ 中，params 是异步的，需要使用 await
  const { slug, locale } = await params;

  // 处理特殊的 slug 情况
  if (slug === 'not-found') {
    return {
      title: 'Tool Not Found | X114 AI Tool Hub',
    };
  }

  let tool;
  try {
    tool = await getToolBySlug(slug, locale);
  } catch (error) {
    console.error(`Error fetching tool metadata for slug ${slug}:`, error);
    return {
      title: 'Tool Not Found | X114 AI Tool Hub',
    };
  }

  if (!tool) {
    return {
      title: 'Tool Not Found | X114 AI Tool Hub',
    };
  }

  try {
    const dictionary = await getDictionary(locale);

    // 基于工具信息和语言生成元数据
    const metadata = generateToolMetadata(tool, locale);

    // 根据语言调整标题
    if (dictionary.seo) {
      const appName = dictionary.common.appName;
      metadata.title = `${tool.name} - AI Tool | ${appName}`;
    }

    return metadata;
  } catch (error) {
    console.error(`Error generating metadata for tool ${slug}:`, error);
    return {
      title: `${tool.name} - AI Tool | X114 AI Tool Hub`,
    };
  }
}

export default async function ToolPage({ params }: ToolPageProps) {
  // 在 Next.js 15+ 中，params 是异步的，需要使用 await
  const { locale, slug } = await params;
  const dictionary = await getDictionary(locale);

  // 处理特殊的 slug 情况
  if (slug === 'not-found') {
    notFound();
  }

  let tool;
  try {
    tool = await getToolBySlug(slug, locale);
  } catch (error) {
    console.error(`Error fetching tool with slug ${slug}:`, error);
    notFound();
  }

  // 如果找不到工具，返回 404
  if (!tool) {
    notFound();
  }

  let relatedTools: Tool[] = [];
  try {
    relatedTools = await getRelatedTools(tool, 4, locale);
  } catch (error) {
    console.error(`Error fetching related tools for ${slug}:`, error);
    // 继续渲染页面，只是没有相关工具
  }

  const jsonLdData = generateToolJsonLd(tool);

  return (
    <div className="container mx-auto px-4 py-24">
      {/* 只保留结构化数据，其他SEO信息由generateMetadata处理 */}
      <ServerJsonLd data={jsonLdData} id={`tool-${tool.slug}-jsonld`} />

      <ToolPageAdLayout
        sidebarAdSlot="7890123456"
        contentAdSlot="2345678901"
      >
        <ToolHeader tool={tool} dictionary={dictionary} locale={locale} />
        <ToolContent tool={tool} dictionary={dictionary} locale={locale} />
        <RelatedTools tools={relatedTools} dictionary={dictionary} locale={locale} />
      </ToolPageAdLayout>
    </div>
  );
}
