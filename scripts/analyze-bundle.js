/**
 * 构建分析脚本
 * 
 * 此脚本用于分析 Next.js 构建包的大小，帮助优化构建过程。
 * 使用方法：node scripts/analyze-bundle.js
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// 分析构建包
function analyzeBuild() {
  console.log('🔍 开始分析构建包...');
  
  try {
    // 设置环境变量
    process.env.ANALYZE = 'true';
    
    // 执行构建命令
    execSync('npm run build', { stdio: 'inherit' });
    
    console.log('✅ 构建分析完成！');
  } catch (error) {
    console.error('❌ 构建分析失败：', error);
    process.exit(1);
  }
}

// 检查大文件
function checkLargeFiles() {
  console.log('🔍 检查大文件...');
  
  const outputDir = path.join(__dirname, '..', 'out');
  
  if (!fs.existsSync(outputDir)) {
    console.log('❌ 输出目录不存在，请先构建项目');
    return;
  }
  
  // 递归获取所有文件
  const files = [];
  
  function getFiles(dir) {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const itemPath = path.join(dir, item);
      const stats = fs.statSync(itemPath);
      
      if (stats.isDirectory()) {
        getFiles(itemPath);
      } else {
        files.push({
          path: itemPath,
          size: stats.size,
          relativePath: path.relative(outputDir, itemPath)
        });
      }
    }
  }
  
  getFiles(outputDir);
  
  // 按大小排序
  files.sort((a, b) => b.size - a.size);
  
  // 显示前 20 个最大的文件
  console.log('\n📊 最大的 20 个文件：');
  console.log('----------------------------------------');
  console.log('大小 (KB) | 文件路径');
  console.log('----------------------------------------');
  
  files.slice(0, 20).forEach(file => {
    const sizeInKB = (file.size / 1024).toFixed(2);
    console.log(`${sizeInKB.padStart(10)} | ${file.relativePath}`);
  });
  
  // 按类型统计
  const fileTypes = {};
  
  files.forEach(file => {
    const ext = path.extname(file.path).toLowerCase() || 'unknown';
    
    if (!fileTypes[ext]) {
      fileTypes[ext] = {
        count: 0,
        size: 0
      };
    }
    
    fileTypes[ext].count++;
    fileTypes[ext].size += file.size;
  });
  
  // 按大小排序
  const sortedTypes = Object.entries(fileTypes)
    .map(([ext, data]) => ({
      ext,
      ...data,
      avgSize: data.size / data.count
    }))
    .sort((a, b) => b.size - a.size);
  
  // 显示文件类型统计
  console.log('\n📊 文件类型统计：');
  console.log('----------------------------------------');
  console.log('类型 | 数量 | 总大小 (MB) | 平均大小 (KB)');
  console.log('----------------------------------------');
  
  sortedTypes.forEach(type => {
    const totalSizeInMB = (type.size / (1024 * 1024)).toFixed(2);
    const avgSizeInKB = (type.avgSize / 1024).toFixed(2);
    
    console.log(`${type.ext.padEnd(6)} | ${String(type.count).padStart(5)} | ${totalSizeInMB.padStart(12)} | ${avgSizeInKB.padStart(15)}`);
  });
  
  // 计算总大小
  const totalSize = files.reduce((sum, file) => sum + file.size, 0);
  const totalSizeInMB = (totalSize / (1024 * 1024)).toFixed(2);
  
  console.log('\n📊 总计：');
  console.log(`总文件数：${files.length}`);
  console.log(`总大小：${totalSizeInMB} MB`);
}

// 主函数
function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--analyze')) {
    analyzeBuild();
  } else if (args.includes('--check')) {
    checkLargeFiles();
  } else {
    console.log('用法：');
    console.log('  node scripts/analyze-bundle.js --analyze  # 分析构建包');
    console.log('  node scripts/analyze-bundle.js --check    # 检查大文件');
  }
}

main();
