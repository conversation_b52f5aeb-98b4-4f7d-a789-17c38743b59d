import Link from 'next/link';
import { Calendar, Clock, Tag } from 'lucide-react';
import { Card, Card<PERSON>ontent, <PERSON><PERSON>ooter, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Article } from '@/lib/types';
import { Dictionary } from '@/lib/i18n/types';
import { Locale } from '@/lib/i18n/config';

interface ArticleCardProps {
  article: Article;
  dictionary: Dictionary;
  locale: Locale;
}

export default function ArticleCard({ article, dictionary, locale }: ArticleCardProps) {
  // 计算阅读时间（基于内容长度的简单估算）
  const readTime = Math.max(1, Math.ceil(article.content.length / 1000));

  // 格式化日期
  const formatDate = (date: Date) => {
    const localeMap: Record<string, string> = {
      'zh': 'zh-CN',
      'es': 'es-ES',
      'en': 'en-US'
    };
    return new Intl.DateTimeFormat(localeMap[locale] || 'en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    }).format(date);
  };

  return (
    <Card className="h-full flex flex-col hover:shadow-lg transition-shadow duration-300">
      <CardHeader className="pb-4">
        <Link
          href={`/${locale}/article/${article.slug}`}
          className="block group"
        >
          <h3 className="text-xl font-semibold line-clamp-2 group-hover:text-primary transition-colors">
            {article.title}
          </h3>
        </Link>
      </CardHeader>

      <CardContent className="flex-1 pb-4">
        <p className="text-muted-foreground line-clamp-3 mb-4">
          {article.summary}
        </p>

        {article.tags && article.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-4">
            {article.tags.slice(0, 3).map((tag) => (
              <Badge key={tag} variant="outline" className="text-xs">
                <Tag className="w-3 h-3 mr-1" />
                {tag}
              </Badge>
            ))}
            {article.tags.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{article.tags.length - 3}
              </Badge>
            )}
          </div>
        )}
      </CardContent>

      <CardFooter className="pt-0">
        <div className="flex items-center justify-between w-full text-sm text-muted-foreground">
          <div className="flex items-center space-x-4">
            <div className="flex items-center">
              <Calendar className="w-4 h-4 mr-1" />
              {article.publishedAt ? formatDate(article.publishedAt) : 'Unknown date'}
            </div>
            <div className="flex items-center">
              <Clock className="w-4 h-4 mr-1" />
              {readTime} {dictionary.articles?.readTime || 'min read'}
            </div>
          </div>

          <Link
            href={`/${locale}/article/${article.slug}`}
            className="text-primary hover:underline font-medium"
          >
            {dictionary.articles?.readMore || 'Read More'}
          </Link>
        </div>
      </CardFooter>
    </Card>
  );
}
