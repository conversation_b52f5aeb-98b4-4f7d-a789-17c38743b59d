import Link from 'next/link';
import { getDictionary } from '@/lib/i18n/get-dictionary';
import { Locale } from '@/lib/i18n/config';
import { Button } from '@/components/ui/button';

export default async function NotFound({
  params
}: {
  params?: Promise<{ locale: Locale }>
}) {
  // 默认使用英语
  // 在 Next.js 15+ 中，params 是异步的，需要使用 await
  const locale = params ? (await params).locale : 'en';
  const dictionary = await getDictionary(locale);

  return (
    <div className="container mx-auto px-4 py-24 flex flex-col items-center justify-center min-h-[70vh] text-center">
      <h1 className="text-9xl font-bold text-primary">404</h1>
      <h2 className="text-3xl font-semibold mt-6 mb-4">
        {dictionary.errors?.notFoundTitle || 'Page Not Found'}
      </h2>
      <p className="text-muted-foreground max-w-md mb-8">
        {dictionary.errors?.notFoundDescription ||
          "The page you're looking for doesn't exist or has been moved."}
      </p>
      <div className="flex gap-4">
        <Button asChild variant="default">
          <Link href={`/${locale}`}>
            {dictionary.errors?.backHome || 'Back to Home'}
          </Link>
        </Button>
        <Button asChild variant="outline">
          <Link href={`/${locale}/tools`}>
            {dictionary.errors?.browseTools || 'Browse Tools'}
          </Link>
        </Button>
      </div>
    </div>
  );
}