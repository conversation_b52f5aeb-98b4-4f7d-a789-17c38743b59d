import { Tool } from '@/lib/types';
import SectionHeader from '@/components/ui/section-header';
import ToolCard from '@/components/tools/tool-card';

export interface ToolsSectionProps {
  title: string;
  viewAllText?: string;
  viewAllLink?: string;
  tools: Tool[];
  locale: string;
  dictionary?: any;
  showTimeAgo?: boolean;
  showRating?: boolean;
  showViewDetails?: boolean;
  showTooltips?: boolean;
  className?: string;
  gridClassName?: string;
}

/**
 * 通用的工具展示区块组件
 * 用于展示一组工具
 */
export default function ToolsSection({
  title,
  viewAllText,
  viewAllLink,
  tools,
  locale,
  dictionary,
  showTimeAgo = false,
  showRating = true,
  showViewDetails = true,
  showTooltips = true,
  className = '',
  gridClassName = 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'
}: ToolsSectionProps) {
  if (!tools || tools.length === 0) {
    return null;
  }

  return (
    <section className={`py-10 ${className}`}>
      <SectionHeader
        title={title}
        viewAllText={viewAllText}
        viewAllLink={viewAllLink}
      />

      <div className={`grid ${gridClassName} gap-6`}>
        {tools.map((tool) => (
          <ToolCard
            key={tool.id}
            tool={tool}
            locale={locale}
            dictionary={dictionary}
            showTimeAgo={showTimeAgo}
            showRating={showRating}
            showViewDetails={showViewDetails}
            showTooltips={showTooltips}
          />
        ))}
      </div>
    </section>
  );
}
