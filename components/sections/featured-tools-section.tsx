import { getTools } from '@/lib/db/tools';
import ToolsSection from './tools-section';

/**
 * 精选工具区块组件
 * 展示精选的工具
 */
export default async function FeaturedToolsSection({ dictionary }: { dictionary: any }) {
  // 获取当前语言
  const locale = dictionary.locale || 'en';

  // 从数据库获取精选工具
  const featuredTools = await getTools({ featured: true, limit: 4 }, locale);

  return (
    <ToolsSection
      title={dictionary.home.featured.title}
      viewAllText={dictionary.home.featured.viewAll}
      viewAllLink={`/${locale}/tools?featured=true`}
      tools={featuredTools}
      locale={locale}
      dictionary={dictionary}
    />
  );
}