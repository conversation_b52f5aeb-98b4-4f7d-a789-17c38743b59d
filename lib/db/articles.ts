import { Article } from '../types';
import {
  getMongoDBArticles,
  getMongoDBArticleById,
  getMongoDBArticleBySlug
} from './mongodb-wrapper';

// 默认语言
const DEFAULT_LANGUAGE = 'en';

/**
 * 获取所有文章
 * @param language 语言，默认为英语
 * @returns 文章列表
 */
export async function getAllArticles(language: string = DEFAULT_LANGUAGE): Promise<Article[]> {
  return getMongoDBArticles(language);
}

/**
 * 根据过滤条件获取文章
 * @param filters 过滤条件
 * @param language 语言，默认为英语
 * @returns 过滤后的文章列表
 */
export async function getArticles(filters?: {
  category?: string;
  featured?: boolean;
  tags?: string[];
  sort?: 'newest' | 'popular';
  limit?: number;
  offset?: number;
}, language: string = DEFAULT_LANGUAGE): Promise<Article[]> {
  // 在静态导出环境中，使用模拟数据
  if (process.env.STATIC_EXPORT === 'true') {
    let articles = await getMongoDBArticles(language);

    // 应用过滤条件
    if (filters) {
      if (filters.category) {
        articles = articles.filter(article => article.category === filters.category);
      }

      if (filters.featured) {
        articles = articles.filter(article => article.featured);
      }

      if (filters.tags && filters.tags.length > 0) {
        articles = articles.filter(article => {
          const articleTags = article.tags || [];
          return filters.tags!.some(tag => articleTags.includes(tag));
        });
      }

      // 应用排序
      if (filters.sort) {
        switch (filters.sort) {
          case 'newest':
            articles.sort((a, b) => {
              const aTime = a.publishedAt?.getTime() || 0;
              const bTime = b.publishedAt?.getTime() || 0;
              return bTime - aTime;
            });
            break;
          case 'popular':
            // 这里可以根据阅读量或其他指标排序，暂时使用发布日期
            articles.sort((a, b) => {
              const aTime = a.publishedAt?.getTime() || 0;
              const bTime = b.publishedAt?.getTime() || 0;
              return bTime - aTime;
            });
            break;
        }
      }

      // 应用分页
      if (filters.offset !== undefined && filters.limit !== undefined) {
        articles = articles.slice(filters.offset, filters.offset + filters.limit);
      } else if (filters.limit !== undefined) {
        articles = articles.slice(0, filters.limit);
      }
    }

    return articles;
  }

  try {
    // 动态导入 MongoDB 模块
    const { getArticlesFromMongoDB } = await import('./mongodb/articles');
    const articles = await getArticlesFromMongoDB({
      ...filters,
      language
    });
    return articles;
  } catch (error) {
    console.error(`Error fetching filtered articles from MongoDB for language ${language}:`, error);
    return [];
  }
}

/**
 * 根据ID获取文章
 * @param id 文章ID
 * @param language 语言，默认为英语
 * @returns 文章对象，如果未找到则返回undefined
 */
export async function getArticleById(id: string, language: string = DEFAULT_LANGUAGE): Promise<Article | undefined> {
  return getMongoDBArticleById(id, language);
}

/**
 * 根据Slug获取文章
 * @param slug 文章Slug
 * @param language 语言，默认为英语
 * @returns 文章对象，如果未找到则返回undefined
 */
export async function getArticleBySlug(slug: string, language: string = DEFAULT_LANGUAGE): Promise<Article | undefined> {
  return getMongoDBArticleBySlug(slug, language);
}

/**
 * 获取特色文章
 * @param limit 限制数量，默认为4
 * @param language 语言，默认为英语
 * @returns 特色文章列表
 */
export async function getFeaturedArticles(limit: number = 4, language: string = DEFAULT_LANGUAGE): Promise<Article[]> {
  return getArticles({
    featured: true,
    limit
  }, language);
}

/**
 * 获取最新文章
 * @param limit 限制数量，默认为10
 * @param language 语言，默认为英语
 * @returns 最新文章列表
 */
export async function getLatestArticles(limit: number = 10, language: string = DEFAULT_LANGUAGE): Promise<Article[]> {
  return getArticles({
    sort: 'newest',
    limit
  }, language);
}

/**
 * 获取相关文章
 * @param article 文章对象
 * @param limit 限制数量，默认为4
 * @param language 语言，默认为文章的语言或英语
 * @returns 相关文章列表
 */
export async function getRelatedArticles(article: Article, limit: number = 4, language?: string): Promise<Article[]> {
  // 如果未指定语言，则使用文章的语言或默认语言
  const lang = language || article.language || DEFAULT_LANGUAGE;

  // 获取所有文章
  const allArticles = await getArticles({
    category: article.category,
    limit: limit * 2 // 获取更多文章，以便有足够的相关文章
  }, lang);

  // 过滤掉当前文章
  const filteredArticles = allArticles.filter(a => a.id !== article.id);

  // 根据标签匹配度排序
  const scoredArticles = filteredArticles.map(a => {
    // 计算标签匹配度
    const aTags = a.tags || [];
    const articleTags = article.tags || [];
    const tagMatchCount = aTags.filter(tag => articleTags.includes(tag)).length;
    return { article: a, score: tagMatchCount };
  });

  // 按匹配度排序并限制数量
  return scoredArticles
    .sort((a, b) => b.score - a.score)
    .slice(0, limit)
    .map(item => item.article);
}

/**
 * 创建新文章
 * @param article 文章对象
 * @returns 创建的文章对象
 */
export async function createArticle(article: Omit<Article, '_id'>): Promise<Article> {
  // 确保文章有语言字段
  const articleWithLanguage = {
    ...article,
    language: article.language || DEFAULT_LANGUAGE
  };

  // 在静态导出环境中，这个函数不会被调用
  // 但为了类型安全，我们返回一个模拟的文章对象
  if (process.env.STATIC_EXPORT === 'true') {
    return articleWithLanguage as Article;
  }

  try {
    // 动态导入 MongoDB 模块
    const { createArticleInMongoDB } = await import('./mongodb/articles');
    const newArticle = await createArticleInMongoDB(articleWithLanguage);
    return newArticle;
  } catch (error) {
    console.error('Error creating article in MongoDB:', error);
    throw error;
  }
}

/**
 * 更新文章
 * @param id 文章ID
 * @param article 更新的文章字段
 * @returns 更新后的文章对象，如果未找到则返回null
 */
export async function updateArticle(id: string, article: Partial<Article>): Promise<Article | null> {
  // 在静态导出环境中，这个函数不会被调用
  if (process.env.STATIC_EXPORT === 'true') {
    return null;
  }

  try {
    // 动态导入 MongoDB 模块
    const { updateArticleInMongoDB } = await import('./mongodb/articles');
    const updatedArticle = await updateArticleInMongoDB(id, article);
    return updatedArticle;
  } catch (error) {
    console.error(`Error updating article with id ${id} in MongoDB:`, error);
    throw error;
  }
}

/**
 * 删除文章
 * @param id 文章ID
 * @returns 是否成功删除
 */
export async function deleteArticle(id: string): Promise<boolean> {
  // 在静态导出环境中，这个函数不会被调用
  if (process.env.STATIC_EXPORT === 'true') {
    return false;
  }

  try {
    // 动态导入 MongoDB 模块
    const { deleteArticleFromMongoDB } = await import('./mongodb/articles');
    const result = await deleteArticleFromMongoDB(id);
    return result;
  } catch (error) {
    console.error(`Error deleting article with id ${id} from MongoDB:`, error);
    throw error;
  }
}
