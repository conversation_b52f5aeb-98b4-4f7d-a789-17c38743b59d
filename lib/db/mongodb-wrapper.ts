import { isStaticExport } from '../utils/environment';
import { Tool, Category, Article } from '../types';
import {
  getStaticTools,
  getStaticCategories,
  getStaticArticles,
  getStaticToolById,
  getStaticToolBySlug,
  getStaticCategoryById,
  getStaticCategoryBySlug,
  getStaticArticleById,
  getStaticArticleBySlug
} from './static-data';

// 在静态导出时使用静态数据，否则动态导入 MongoDB 模块
export async function getMongoDBTools(language: string = 'en'): Promise<Tool[]> {
  if (isStaticExport()) {
    return getStaticTools(language);
  }

  try {
    // 动态导入 MongoDB 模块
    const { getAllToolsFromMongoDB } = await import('./mongodb/tools');
    return await getAllToolsFromMongoDB(language);
  } catch (error) {
    console.error(`Error fetching tools from MongoDB for language ${language}:`, error);
    return [];
  }
}

export async function getMongoDBToolsFiltered(filters: any, language: string = 'en'): Promise<Tool[]> {
  if (isStaticExport()) {
    let tools = getStaticTools(language);

    // 应用过滤条件
    if (filters) {
      if (filters.category) {
        tools = tools.filter(tool =>
          (tool.groups || []).some(group => group.slug === filters.category)
        );
      }

      if (filters.featured) {
        tools = tools.filter(tool => tool.featured);
      }

      if (filters.trending) {
        tools = tools.filter(tool => tool.trending);
      }

      if (filters.isNew) {
        tools = tools.filter(tool => tool.isNew);
      }

      if (filters.minRating) {
        tools = tools.filter(tool => (tool.rating || 0) >= filters.minRating);
      }

      if (filters.tags && filters.tags.length > 0) {
        tools = tools.filter(tool =>
          filters.tags.some((tag: string) => (tool.tags || []).includes(tag))
        );
      }

      // 应用排序
      if (filters.sort) {
        switch (filters.sort) {
          case 'newest':
            tools.sort((a, b) => new Date(b.createdAt || 0).getTime() - new Date(a.createdAt || 0).getTime());
            break;
          case 'popular':
            tools.sort((a, b) => (b.reviewCount || 0) - (a.reviewCount || 0));
            break;
          case 'rating':
            tools.sort((a, b) => (b.rating || 0) - (a.rating || 0));
            break;
        }
      }

      // 应用分页
      if (filters.offset !== undefined && filters.limit !== undefined) {
        tools = tools.slice(filters.offset, filters.offset + filters.limit);
      } else if (filters.limit !== undefined) {
        tools = tools.slice(0, filters.limit);
      }
    }

    return tools;
  }

  try {
    // 动态导入 MongoDB 模块
    const { getToolsFromMongoDB } = await import('./mongodb/tools');
    return await getToolsFromMongoDB({
      ...filters,
      language
    });
  } catch (error) {
    console.error(`Error fetching filtered tools from MongoDB for language ${language}:`, error);
    return [];
  }
}

export async function getMongoDBToolById(id: string, language: string = 'en'): Promise<Tool | undefined> {
  if (isStaticExport()) {
    return getStaticToolById(id, language);
  }

  try {
    // 动态导入 MongoDB 模块
    const { getToolByIdFromMongoDB } = await import('./mongodb/tools');
    const tool = await getToolByIdFromMongoDB(id, language);
    return tool || undefined;
  } catch (error) {
    console.error(`Error fetching tool with id ${id} from MongoDB for language ${language}:`, error);
    return undefined;
  }
}

export async function getMongoDBToolBySlug(slug: string, language: string = 'en'): Promise<Tool | undefined> {
  if (isStaticExport()) {
    return getStaticToolBySlug(slug, language);
  }

  try {
    // 动态导入 MongoDB 模块
    const { getToolBySlugFromMongoDB } = await import('./mongodb/tools');
    const tool = await getToolBySlugFromMongoDB(slug, language);
    return tool || undefined;
  } catch (error) {
    console.error(`Error fetching tool with slug ${slug} from MongoDB for language ${language}:`, error);
    return undefined;
  }
}

export async function getMongoDBRelatedTools(tool: Tool, limit: number = 4, language?: string): Promise<Tool[]> {
  // 如果未指定语言，则使用工具的语言或默认语言
  const lang = language || tool.language || 'en';

  if (isStaticExport()) {
    const tools = getStaticTools(lang);

    // 过滤掉当前工具
    const filteredTools = tools.filter(t => t.id !== tool.id);

    // 根据类别和标签匹配相关工具
    const relatedTools = filteredTools
      .filter(t =>
        (t.groups || []).some(group => (tool.groups || []).includes(group)) ||
        (t.tags || []).some(tag => (tool.tags || []).includes(tag))
      )
      .slice(0, limit);

    return relatedTools;
  }

  try {
    // 动态导入 MongoDB 模块
    const { getRelatedToolsFromMongoDB } = await import('./mongodb/tools');
    const relatedTools = await getRelatedToolsFromMongoDB(tool, limit, lang);
    return relatedTools;
  } catch (error) {
    console.error(`Error fetching related tools for tool ${tool.id} from MongoDB:`, error);
    return [];
  }
}

export async function getMongoDBCategories(language: string = 'en'): Promise<Category[]> {
  if (isStaticExport()) {
    return getStaticCategories(language);
  }

  try {
    // 动态导入 MongoDB 模块
    const { getAllCategoriesFromMongoDB } = await import('./mongodb/categories');
    const categories = await getAllCategoriesFromMongoDB(language);
    return categories;
  } catch (error) {
    console.error(`Error fetching categories from MongoDB for language ${language}:`, error);
    return [];
  }
}

export async function getMongoDBCategoryById(id: string, language: string = 'en'): Promise<Category | undefined> {
  if (isStaticExport()) {
    return getStaticCategoryById(id, language);
  }

  try {
    // 动态导入 MongoDB 模块
    const { getCategoryByIdFromMongoDB } = await import('./mongodb/categories');
    const category = await getCategoryByIdFromMongoDB(id, language);
    return category || undefined;
  } catch (error) {
    console.error(`Error fetching category with id ${id} from MongoDB for language ${language}:`, error);
    return undefined;
  }
}

export async function getMongoDBCategoryBySlug(slug: string, language: string = 'en'): Promise<Category | undefined> {
  if (isStaticExport()) {
    return getStaticCategoryBySlug(slug, language);
  }

  try {
    // 动态导入 MongoDB 模块
    const { getCategoryBySlugFromMongoDB } = await import('./mongodb/categories');
    const category = await getCategoryBySlugFromMongoDB(slug, language);
    return category || undefined;
  } catch (error) {
    console.error(`Error fetching category with slug ${slug} from MongoDB for language ${language}:`, error);
    return undefined;
  }
}

export async function getMongoDBArticles(language: string = 'en'): Promise<Article[]> {
  if (isStaticExport()) {
    return getStaticArticles(language);
  }

  try {
    // 动态导入 MongoDB 模块
    const { getAllArticlesFromMongoDB } = await import('./mongodb/articles');
    const articles = await getAllArticlesFromMongoDB(language);
    return articles;
  } catch (error) {
    console.error(`Error fetching articles from MongoDB for language ${language}:`, error);
    return [];
  }
}

export async function getMongoDBArticleById(id: string, language: string = 'en'): Promise<Article | undefined> {
  if (isStaticExport()) {
    return getStaticArticleById(id, language);
  }

  try {
    // 动态导入 MongoDB 模块
    const { getArticleByIdFromMongoDB } = await import('./mongodb/articles');
    const article = await getArticleByIdFromMongoDB(id, language);
    return article || undefined;
  } catch (error) {
    console.error(`Error fetching article with id ${id} from MongoDB for language ${language}:`, error);
    return undefined;
  }
}

export async function getMongoDBArticleBySlug(slug: string, language: string = 'en'): Promise<Article | undefined> {
  if (isStaticExport()) {
    return getStaticArticleBySlug(slug, language);
  }

  try {
    // 动态导入 MongoDB 模块
    const { getArticleBySlugFromMongoDB } = await import('./mongodb/articles');
    const article = await getArticleBySlugFromMongoDB(slug, language);
    return article || undefined;
  } catch (error) {
    console.error(`Error fetching article with slug ${slug} from MongoDB for language ${language}:`, error);
    return undefined;
  }
}
