'use client';

import { useEffect, useState } from 'react';

interface AdPerformanceData {
  totalAds: number;
  loadedAds: number;
  failedAds: number;
  loadTime: number;
}

/**
 * 广告性能监控组件
 * 监控广告加载状态和性能指标
 */
export default function AdPerformanceMonitor({
  enabled = process.env.NODE_ENV === 'development',
  showDebugInfo = false,
}: {
  enabled?: boolean;
  showDebugInfo?: boolean;
}) {
  const [performance, setPerformance] = useState<AdPerformanceData>({
    totalAds: 0,
    loadedAds: 0,
    failedAds: 0,
    loadTime: 0,
  });

  useEffect(() => {
    if (!enabled || typeof window === 'undefined') return;

    const startTime = Date.now();
    let checkInterval: NodeJS.Timeout;

    const checkAdPerformance = () => {
      const adElements = document.querySelectorAll('.adsbygoogle');
      const totalAds = adElements.length;
      let loadedAds = 0;
      let failedAds = 0;

      adElements.forEach((ad) => {
        const adElement = ad as HTMLElement;
        
        // 检查广告是否已加载
        if (adElement.getAttribute('data-adsbygoogle-status') === 'done') {
          loadedAds++;
        } else if (adElement.getAttribute('data-ad-status') === 'unfilled') {
          failedAds++;
        }
      });

      const loadTime = Date.now() - startTime;

      setPerformance({
        totalAds,
        loadedAds,
        failedAds,
        loadTime,
      });

      // 如果所有广告都已处理完成，停止检查
      if (loadedAds + failedAds >= totalAds && totalAds > 0) {
        clearInterval(checkInterval);
      }
    };

    // 初始检查
    checkAdPerformance();

    // 定期检查广告状态
    checkInterval = setInterval(checkAdPerformance, 1000);

    // 监听 AdSense 事件
    const handleAdSenseEvent = (event: Event) => {
      console.log('AdSense event:', event);
      checkAdPerformance();
    };

    // 监听广告加载完成事件
    window.addEventListener('adsbygoogle-loaded', handleAdSenseEvent);
    window.addEventListener('adsbygoogle-error', handleAdSenseEvent);

    return () => {
      clearInterval(checkInterval);
      window.removeEventListener('adsbygoogle-loaded', handleAdSenseEvent);
      window.removeEventListener('adsbygoogle-error', handleAdSenseEvent);
    };
  }, [enabled]);

  // 在控制台输出性能数据
  useEffect(() => {
    if (!enabled || !showDebugInfo) return;

    console.group('📊 Ad Performance Monitor');
    console.log('Total Ads:', performance.totalAds);
    console.log('Loaded Ads:', performance.loadedAds);
    console.log('Failed Ads:', performance.failedAds);
    console.log('Load Time:', `${performance.loadTime}ms`);
    
    if (performance.totalAds > 0) {
      const successRate = (performance.loadedAds / performance.totalAds) * 100;
      console.log('Success Rate:', `${successRate.toFixed(1)}%`);
    }
    
    console.groupEnd();
  }, [performance, enabled, showDebugInfo]);

  if (!enabled || !showDebugInfo) return null;

  return (
    <div className="fixed bottom-4 left-4 z-50 bg-background/95 backdrop-blur-sm border border-border rounded-lg p-3 text-xs font-mono shadow-lg">
      <div className="font-semibold mb-2">📊 Ad Performance</div>
      <div className="space-y-1">
        <div>Total: {performance.totalAds}</div>
        <div className="text-green-600">Loaded: {performance.loadedAds}</div>
        <div className="text-red-600">Failed: {performance.failedAds}</div>
        <div>Time: {performance.loadTime}ms</div>
        {performance.totalAds > 0 && (
          <div>
            Rate: {((performance.loadedAds / performance.totalAds) * 100).toFixed(1)}%
          </div>
        )}
      </div>
    </div>
  );
}

/**
 * 广告错误处理工具
 */
export function setupAdErrorHandling() {
  if (typeof window === 'undefined') return;

  // 监听 AdSense 错误
  window.addEventListener('error', (event) => {
    if (event.filename?.includes('googlesyndication') || 
        event.message?.includes('adsbygoogle')) {
      console.warn('AdSense Error:', event.message);
      
      // 可以在这里添加错误上报逻辑
      // reportAdError(event);
    }
  });

  // 监听未处理的 Promise 拒绝
  window.addEventListener('unhandledrejection', (event) => {
    if (event.reason?.toString().includes('adsbygoogle')) {
      console.warn('AdSense Promise Rejection:', event.reason);
      event.preventDefault(); // 防止错误显示在控制台
    }
  });
}

/**
 * 广告可见性检测
 */
export function setupAdVisibilityTracking() {
  if (typeof window === 'undefined' || !('IntersectionObserver' in window)) {
    return () => {}; // 返回空函数而不是 undefined
  }

  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        const adElement = entry.target as HTMLElement;
        const adSlot = adElement.getAttribute('data-ad-slot');
        
        if (entry.isIntersecting) {
          console.log(`Ad ${adSlot} is visible`);
          // 可以在这里添加可见性跟踪逻辑
        } else {
          console.log(`Ad ${adSlot} is not visible`);
        }
      });
    },
    {
      threshold: 0.5, // 50% 可见时触发
    }
  );

  // 观察所有广告元素
  const observeAds = () => {
    const adElements = document.querySelectorAll('.adsbygoogle');
    adElements.forEach((ad) => observer.observe(ad));
  };

  // 初始观察
  observeAds();

  // 监听 DOM 变化，观察新添加的广告
  const mutationObserver = new MutationObserver(() => {
    observeAds();
  });

  mutationObserver.observe(document.body, {
    childList: true,
    subtree: true,
  });

  return () => {
    observer.disconnect();
    mutationObserver.disconnect();
  };
}
