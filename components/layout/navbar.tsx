'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Search, Menu, X, ChevronDown, Globe } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Locale, localeNames } from '@/lib/i18n/config';
import { useChangeLocale } from '@/lib/i18n/client';
import { getDictionary } from '@/lib/i18n/get-dictionary';
import LanguageSwitcher from './language-switcher';

export default function Navbar({ locale }: { locale: Locale }) {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [translations, setTranslations] = useState<any>({});
  const pathname = usePathname();
  const changeLocale = useChangeLocale();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  useEffect(() => {
    async function loadTranslations() {
      const dict = await getDictionary(locale);
      setTranslations(dict);
    }

    loadTranslations();
  }, [locale]);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const navbarClasses = `fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
    isScrolled
      ? 'bg-background/95 backdrop-blur-md border-b shadow-sm'
      : 'bg-transparent'
  }`;

  if (!translations.common) return null;

  return (
    <nav className={navbarClasses}>
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href={`/${locale}`} className="flex items-center">
            <span className="text-xl font-bold bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent">
              {translations.common.appName}
            </span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-6">
            <form
              action={`/${locale}/tools`}
              className="relative w-64"
              onSubmit={(e) => {
                const input = e.currentTarget.querySelector('input');
                if (input && !input.value.trim()) {
                  e.preventDefault();
                }
              }}
            >
              <Input
                type="text"
                name="q"
                placeholder={translations.common.searchPlaceholder}
                className="pl-10 pr-4 py-2 rounded-full bg-muted/50 border-0 focus-visible:ring-1"
              />
              <button type="submit" className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground">
                <Search className="h-4 w-4" />
              </button>
            </form>

            <Link href={`/${locale}/tools`} className="text-sm hover:text-primary transition-colors">
              {translations.navigation?.tools || 'Tools'}
            </Link>
            <Link href={`/${locale}/categories`} className="text-sm hover:text-primary transition-colors">
              {translations.navigation?.categories || 'Categories'}
            </Link>
            <Link href={`/${locale}/articles`} className="text-sm hover:text-primary transition-colors">
              {translations.articles?.title || 'Articles'}
            </Link>
            <Link href={`/${locale}/submit`} className="text-sm hover:text-primary transition-colors">
              {translations.common.submitTool}
            </Link>

            <LanguageSwitcher currentLocale={locale} />

            {/* TODO 临时隐藏登录和注册按钮 */}
            {/* <div className="flex items-center space-x-2">
              <Button variant="ghost" size="sm" asChild>
                <Link href={`/${locale}/login`}>{translations.common.login}</Link>
              </Button>
              <Button size="sm" asChild>
                <Link href={`/${locale}/signup`}>{translations.common.signup}</Link>
              </Button>
            </div> */}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <Button variant="ghost" size="icon" onClick={toggleMobileMenu}>
              {isMobileMenuOpen ? <X /> : <Menu />}
            </Button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      {isMobileMenuOpen && (
        <div className="md:hidden px-4 py-3 bg-background border-b shadow-sm">
          <form
            action={`/${locale}/search`}
            className="relative mb-4"
            onSubmit={(e) => {
              const input = e.currentTarget.querySelector('input');
              if (input && !input.value.trim()) {
                e.preventDefault();
              }
            }}
          >
            <Input
              type="text"
              name="q"
              placeholder={translations.common.searchPlaceholder}
              className="pl-10 pr-4 py-2 w-full bg-muted/50 border-0"
            />
            <button type="submit" className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground">
              <Search className="h-4 w-4" />
            </button>
          </form>

          <div className="flex flex-col space-y-4">
            <Link href={`/${locale}/tools`} className="py-2 hover:text-primary transition-colors">
              {translations.navigation?.tools || 'Tools'}
            </Link>
            <Link href={`/${locale}/categories`} className="py-2 hover:text-primary transition-colors">
              {translations.navigation?.categories || 'Categories'}
            </Link>
            <Link href={`/${locale}/articles`} className="py-2 hover:text-primary transition-colors">
              {translations.articles?.title || 'Articles'}
            </Link>
            <Link href={`/${locale}/submit`} className="py-2 hover:text-primary transition-colors">
              {translations.common.submitTool}
            </Link>

            <div className="py-2">
              <LanguageSwitcher currentLocale={locale} />
            </div>

            {/* TODO 临时隐藏登录和注册按钮 */}
            {/* <div className="flex space-x-2 pt-2 border-t">
              <Button variant="outline" size="sm" className="flex-1" asChild>
                <Link href={`/${locale}/login`}>{translations.common.login}</Link>
              </Button>
              <Button size="sm" className="flex-1" asChild>
                <Link href={`/${locale}/signup`}>{translations.common.signup}</Link>
              </Button>
            </div> */}
          </div>
        </div>
      )}
    </nav>
  );
}

