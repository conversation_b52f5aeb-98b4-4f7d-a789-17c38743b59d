
import { getDictionary } from '@/lib/i18n/get-dictionary';
import { Locale, i18n } from '@/lib/i18n/config';
import HeroSection from '@/components/sections/hero-section';
import CategorySection from '@/components/sections/category-section';
import FeaturedToolsSection from '@/components/sections/featured-tools-section';
import PopularToolsSection from '@/components/sections/popular-tools-section';
import NewToolsSection from '@/components/sections/new-tools-section';
import SubscribeSection from '@/components/sections/subscribe-section';
import { generateWebsiteJsonLd, generateOrganizationJsonLd } from '@/lib/seo';
import ServerJsonLd from '@/components/seo/server-json-ld';
import { TopBannerAd, ContentRectangleAd } from '@/components/ads/client-ad-wrapper';

// 为静态导出生成所有支持的语言路径
export function generateStaticParams() {
  return i18n.locales.map((locale) => ({ locale }));
}



export default async function Home({
  params,
}: {
  params: Promise<{ locale: Locale }>;
}) {
  // 在 Next.js 15+ 中，params 是异步的，需要使用 await
  const locale = (await params).locale;
  const dictionary = await getDictionary(locale);
  const websiteJsonLd = generateWebsiteJsonLd();
  const organizationJsonLd = generateOrganizationJsonLd();

  return (
    <div className="flex flex-col w-full">
      {/* 只保留结构化数据，其他SEO信息由layout处理 */}
      <ServerJsonLd data={websiteJsonLd} id="website-jsonld" />
      <ServerJsonLd data={organizationJsonLd} id="organization-jsonld" />
      <HeroSection dictionary={{ ...dictionary, locale }} />
      <div className="container mx-auto px-4 py-10 space-y-20">
        <CategorySection dictionary={{ ...dictionary, locale }} />

        {/* 顶部横幅广告 */}
        <TopBannerAd adSlot="1620640133" />

        <FeaturedToolsSection dictionary={{ ...dictionary, locale }} />
        <PopularToolsSection dictionary={{ ...dictionary, locale }} />

        {/* 内容区域方形广告 */}
        <ContentRectangleAd adSlot="4626949563" />

        <NewToolsSection dictionary={{ ...dictionary, locale }} />
        {/* TODO <SubscribeSection /> */}
      </div>
    </div>
  );
}