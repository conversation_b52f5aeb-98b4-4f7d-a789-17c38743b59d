/**
 * 静态导出错误处理工具
 * 提供更好的错误处理机制，用于静态导出过程中的错误处理
 */

import { isStaticExport, isDevelopment } from './environment';

/**
 * 静态导出错误类型
 */
export enum StaticExportErrorType {
  DATA_NOT_FOUND = 'DATA_NOT_FOUND',
  MODULE_NOT_LOADED = 'MODULE_NOT_LOADED',
  INVALID_DATA = 'INVALID_DATA',
  FILE_NOT_FOUND = 'FILE_NOT_FOUND',
  UNKNOWN = 'UNKNOWN'
}

/**
 * 静态导出错误类
 */
export class StaticExportError extends Error {
  type: StaticExportErrorType;
  details?: any;
  
  constructor(
    message: string,
    type: StaticExportErrorType = StaticExportErrorType.UNKNOWN,
    details?: any
  ) {
    super(message);
    this.name = 'StaticExportError';
    this.type = type;
    this.details = details;
  }
  
  /**
   * 创建数据未找到错误
   * @param dataType 数据类型
   * @param id 数据ID
   * @param language 语言
   * @returns StaticExportError 实例
   */
  static dataNotFound(dataType: string, id?: string, language?: string): StaticExportError {
    const message = id
      ? `${dataType} with ID "${id}" not found${language ? ` for language "${language}"` : ''}`
      : `${dataType} not found${language ? ` for language "${language}"` : ''}`;
    
    return new StaticExportError(message, StaticExportErrorType.DATA_NOT_FOUND, { dataType, id, language });
  }
  
  /**
   * 创建模块未加载错误
   * @param moduleName 模块名称
   * @returns StaticExportError 实例
   */
  static moduleNotLoaded(moduleName: string): StaticExportError {
    return new StaticExportError(
      `Module "${moduleName}" not loaded. Please run "node scripts/fetch-static-data.js" before building.`,
      StaticExportErrorType.MODULE_NOT_LOADED,
      { moduleName }
    );
  }
  
  /**
   * 创建无效数据错误
   * @param dataType 数据类型
   * @param details 详情
   * @returns StaticExportError 实例
   */
  static invalidData(dataType: string, details?: any): StaticExportError {
    return new StaticExportError(
      `Invalid ${dataType} data`,
      StaticExportErrorType.INVALID_DATA,
      details
    );
  }
  
  /**
   * 创建文件未找到错误
   * @param filePath 文件路径
   * @returns StaticExportError 实例
   */
  static fileNotFound(filePath: string): StaticExportError {
    return new StaticExportError(
      `File "${filePath}" not found`,
      StaticExportErrorType.FILE_NOT_FOUND,
      { filePath }
    );
  }
}

/**
 * 处理静态导出错误
 * @param error 错误对象
 * @param fallback 回退值
 * @returns 回退值
 */
export function handleStaticExportError<T>(error: unknown, fallback: T): T {
  // 如果不是静态导出，直接抛出错误
  if (!isStaticExport()) {
    throw error;
  }
  
  // 处理静态导出错误
  if (error instanceof StaticExportError) {
    // 在开发环境中打印详细错误信息
    if (isDevelopment()) {
      console.error(`[Static Export Error] ${error.message}`, {
        type: error.type,
        details: error.details
      });
    } else {
      // 在生产环境中只打印错误消息
      console.error(`[Static Export Error] ${error.message}`);
    }
  } else {
    // 处理其他错误
    console.error('[Static Export Error] Unknown error:', error);
  }
  
  // 返回回退值
  return fallback;
}

/**
 * 使用回退值处理可能的错误
 * @param fn 可能抛出错误的函数
 * @param fallback 回退值
 * @returns 函数结果或回退值
 */
export function withFallback<T>(fn: () => T, fallback: T): T {
  try {
    return fn();
  } catch (error) {
    return handleStaticExportError(error, fallback);
  }
}

/**
 * 使用回退值处理可能的异步错误
 * @param fn 可能抛出错误的异步函数
 * @param fallback 回退值
 * @returns Promise，解析为函数结果或回退值
 */
export async function withAsyncFallback<T>(fn: () => Promise<T>, fallback: T): Promise<T> {
  try {
    return await fn();
  } catch (error) {
    return handleStaticExportError(error, fallback);
  }
}
