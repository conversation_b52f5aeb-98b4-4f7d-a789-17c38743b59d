/**
 * 环境检测工具
 * 用于确定代码运行的环境，以便做出相应的行为调整
 */

// 基本环境检测
const isBrowser = typeof window !== 'undefined';
const isServer = !isBrowser;

/**
 * 检查当前是否在浏览器环境中
 */
export function isClientSide(): boolean {
  return isBrowser;
}

/**
 * 检查当前是否在服务器端渲染环境中
 */
export function isServerSide(): boolean {
  return isServer;
}

/**
 * 检查当前是否在静态导出环境中
 * 在静态导出环境中，我们不能使用依赖 Node.js API 的功能，如 MongoDB 连接
 */
export function isStaticExport(): boolean {
  // 检查是否在浏览器环境中（客户端运行时总是静态导出的结果）
  if (isBrowser) return true;

  // 服务器端检查
  // 检查是否在 Next.js 静态导出过程中
  const isNextStaticExport = process.env.NEXT_PHASE === 'phase-export';

  // 检查是否设置了静态导出环境变量
  const hasStaticExportEnv = process.env.STATIC_EXPORT === 'true';

  return isNextStaticExport || hasStaticExportEnv;
}

/**
 * 检查当前是否在构建时（build-time）
 * 在构建时，我们可以使用 Node.js API，如 MongoDB 连接
 */
export function isBuildTime(): boolean {
  // 只有在服务器端才可能是构建时
  if (isBrowser) return false;

  // 检查是否在 Next.js 构建过程中
  return process.env.NODE_ENV === 'production';
}

/**
 * 检查当前是否在开发环境中
 */
export function isDevelopment(): boolean {
  return process.env.NODE_ENV === 'development';
}

/**
 * 检查当前是否在生产环境中
 */
export function isProduction(): boolean {
  return process.env.NODE_ENV === 'production';
}

/**
 * 检查当前是否在测试环境中
 */
export function isTest(): boolean {
  return process.env.NODE_ENV === 'test';
}

/**
 * 检查当前是否可以访问 MongoDB
 * 在静态导出环境中，我们不能访问 MongoDB
 */
export function canAccessMongoDB(): boolean {
  return isServerSide() && !isStaticExport();
}

// 为了向后兼容，保留原来的 isRuntime 函数
export const isRuntime = isClientSide;
