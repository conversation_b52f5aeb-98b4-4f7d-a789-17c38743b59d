import '../globals.css';
import type { Metadata } from 'next';
import { Locale, defaultLocale, locales } from '@/lib/i18n/config';
import { ThemeProvider } from '@/components/providers/theme-provider';
import { Toaster } from '@/components/ui/sonner';
import Navbar from '@/components/layout/navbar';
import Footer from '@/components/layout/footer';
import { getDictionary } from '@/lib/i18n/get-dictionary';
import { generateStaticMetadata } from '@/lib/seo-static';

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: Locale }>;
}): Promise<Metadata> {
  const locale = (await params).locale;
  const dictionary = await getDictionary(locale);

  // 使用统一的SEO配置
  return generateStaticMetadata(
    dictionary.seo?.defaultTitle,
    dictionary.seo?.defaultDescription,
    '',
    locale
  );
}

export async function generateStaticParams() {
  return locales.map((locale) => ({ locale }));
}

export default async function LocaleLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ locale: Locale }>;
}) {
  // 在 Next.js 15+ 中，params 是异步的，需要使用 await
  const locale = (await params).locale || defaultLocale;

  return (
    <ThemeProvider>
      <Navbar locale={locale} />
      <main className="min-h-screen">{children}</main>
      <Footer locale={locale} />
      <Toaster />
    </ThemeProvider>
  );
}