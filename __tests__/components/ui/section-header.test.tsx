import React from 'react';
import { render, screen } from '@testing-library/react';
import SectionHeader from '@/components/ui/section-header';

describe('SectionHeader', () => {
  it('renders the title', () => {
    render(<SectionHeader title="Test Title" />);
    expect(screen.getByText('Test Title')).toBeInTheDocument();
  });

  it('renders the "view all" link when provided', () => {
    render(
      <SectionHeader
        title="Test Title"
        viewAllText="View All"
        viewAllLink="/view-all"
      />
    );
    
    const link = screen.getByText('View All');
    expect(link).toBeInTheDocument();
    expect(link.closest('a')).toHaveAttribute('href', '/view-all');
  });

  it('does not render the "view all" link when not provided', () => {
    render(<SectionHeader title="Test Title" />);
    expect(screen.queryByText('View All')).not.toBeInTheDocument();
  });

  it('applies custom className', () => {
    const { container } = render(
      <SectionHeader title="Test Title" className="custom-class" />
    );
    
    const headerElement = container.firstChild;
    expect(headerElement).toHaveClass('custom-class');
  });

  it('renders with default className', () => {
    const { container } = render(<SectionHeader title="Test Title" />);
    
    const headerElement = container.firstChild;
    expect(headerElement).toHaveClass('flex');
    expect(headerElement).toHaveClass('flex-wrap');
    expect(headerElement).toHaveClass('items-center');
    expect(headerElement).toHaveClass('justify-between');
    expect(headerElement).toHaveClass('mb-8');
  });

  it('renders the title with correct heading level', () => {
    render(<SectionHeader title="Test Title" />);
    
    const heading = screen.getByText('Test Title');
    expect(heading.tagName).toBe('H2');
  });

  it('renders the title with correct styling', () => {
    render(<SectionHeader title="Test Title" />);
    
    const heading = screen.getByText('Test Title');
    expect(heading).toHaveClass('text-3xl');
    expect(heading).toHaveClass('font-bold');
  });

  it('renders the "view all" link with correct styling', () => {
    render(
      <SectionHeader
        title="Test Title"
        viewAllText="View All"
        viewAllLink="/view-all"
      />
    );
    
    const link = screen.getByText('View All').closest('a');
    expect(link).toHaveClass('group');
    expect(link).toHaveClass('inline-flex');
    expect(link).toHaveClass('items-center');
    expect(link).toHaveClass('text-blue-600');
    expect(link).toHaveClass('hover:text-blue-700');
  });

  it('renders the chevron icon in the "view all" link', () => {
    render(
      <SectionHeader
        title="Test Title"
        viewAllText="View All"
        viewAllLink="/view-all"
      />
    );
    
    // Check if there's an SVG icon (ChevronRight)
    const link = screen.getByText('View All').closest('a');
    const svg = link?.querySelector('svg');
    expect(svg).toBeInTheDocument();
  });
});
