'use client';

import { useEffect } from 'react';
import { Locale } from '@/lib/i18n/config';

interface GlobalSeoProps {
  locale: Locale;
  path: string;
  title?: string;
  description?: string;
  keywords?: string[];
  ogImage?: string;
  ogType?: 'website' | 'article';
  noindex?: boolean;
  structuredData?: any[];
}

/**
 * 全局SEO管理组件
 * 用于在客户端动态添加或更新SEO标签
 * 主要用于补充静态生成时无法处理的动态SEO需求
 */
export default function GlobalSeo({
  locale,
  path,
  title,
  description,
  keywords = [],
  ogImage,
  ogType = 'website',
  noindex = false,
  structuredData = [],
}: GlobalSeoProps) {
  useEffect(() => {
    // 只在客户端执行，用于补充或修正SEO信息
    if (typeof window === 'undefined') return;

    // 更新页面标题（如果需要）
    if (title && document.title !== title) {
      document.title = title;
    }

    // 添加或更新meta描述
    if (description) {
      updateMetaTag('description', description);
    }

    // 添加或更新关键词
    if (keywords.length > 0) {
      updateMetaTag('keywords', keywords.join(', '));
    }

    // 更新robots标签
    if (noindex) {
      updateMetaTag('robots', 'noindex, nofollow');
    }

    // 添加结构化数据
    structuredData.forEach((data, index) => {
      addJsonLd(data, `global-seo-${index}`);
    });

    // 清理函数
    return () => {
      // 移除动态添加的结构化数据
      structuredData.forEach((_, index) => {
        removeJsonLd(`global-seo-${index}`);
      });
    };
  }, [locale, path, title, description, keywords, ogImage, ogType, noindex, structuredData]);

  return null; // 这个组件不渲染任何内容
}

// 辅助函数：更新或添加meta标签
function updateMetaTag(name: string, content: string) {
  let meta = document.querySelector(`meta[name="${name}"]`) as HTMLMetaElement;
  
  if (meta) {
    meta.content = content;
  } else {
    meta = document.createElement('meta');
    meta.name = name;
    meta.content = content;
    document.head.appendChild(meta);
  }
}

// 辅助函数：更新或添加property meta标签
function updatePropertyTag(property: string, content: string) {
  let meta = document.querySelector(`meta[property="${property}"]`) as HTMLMetaElement;
  
  if (meta) {
    meta.content = content;
  } else {
    meta = document.createElement('meta');
    meta.setAttribute('property', property);
    meta.content = content;
    document.head.appendChild(meta);
  }
}

// 辅助函数：添加JSON-LD结构化数据
function addJsonLd(data: any, id: string) {
  // 移除已存在的相同ID的脚本
  removeJsonLd(id);
  
  const script = document.createElement('script');
  script.type = 'application/ld+json';
  script.id = id;
  script.textContent = JSON.stringify(data);
  document.head.appendChild(script);
}

// 辅助函数：移除JSON-LD结构化数据
function removeJsonLd(id: string) {
  const existingScript = document.getElementById(id);
  if (existingScript) {
    existingScript.remove();
  }
}

// 辅助函数：添加或更新link标签
function updateLinkTag(rel: string, href: string, attributes: Record<string, string> = {}) {
  let link = document.querySelector(`link[rel="${rel}"]`) as HTMLLinkElement;
  
  if (link) {
    link.href = href;
    Object.entries(attributes).forEach(([key, value]) => {
      link.setAttribute(key, value);
    });
  } else {
    link = document.createElement('link');
    link.rel = rel;
    link.href = href;
    Object.entries(attributes).forEach(([key, value]) => {
      link.setAttribute(key, value);
    });
    document.head.appendChild(link);
  }
}

// 导出辅助函数供其他组件使用
export {
  updateMetaTag,
  updatePropertyTag,
  addJsonLd,
  removeJsonLd,
  updateLinkTag,
};
