import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { getDictionary } from '@/lib/i18n/get-dictionary';
import { Locale, i18n } from '@/lib/i18n/config';
import { getArticleBySlug, getRelatedArticles } from '@/lib/db/articles';
import ArticleContent from '@/components/articles/article-content';
import RelatedArticles from '@/components/articles/related-articles';
import { Container } from '@/components/ui/container';
import { InArticleAd, SidebarAd } from '@/components/ads/client-ad-wrapper';
import { generateStaticMetadata } from '@/lib/seo-static';

// 为静态导出配置
export const dynamic = 'force-static';
export const revalidate = 3600; // 每小时重新验证一次

// 为静态导出生成所有支持的语言路径
export async function generateStaticParams() {
  // 为了静态导出，我们需要预生成所有可能的路径
  // 这里我们从所有语言获取文章数据
  const { getAllArticles } = await import('@/lib/db/articles');
  const { i18n } = await import('@/lib/i18n/config');

  const params = [];

  for (const locale of i18n.locales) {
    try {
      const articles = await getAllArticles(locale);
      for (const article of articles) {
        params.push({
          locale,
          slug: article.slug,
        });
      }
    } catch (error) {
      console.warn(`Failed to generate static params for locale ${locale}:`, error);
    }
  }

  // 如果没有文章，返回一个空的占位符以避免构建错误
  if (params.length === 0) {
    console.warn('No articles found, skipping article page generation');
    // 返回一个不存在的路径，这样页面会在运行时返回 404
    return [{ locale: 'en', slug: '__no_articles__' }];
  }

  return params;
}

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: Locale; slug: string }>;
}): Promise<Metadata> {
  const { locale, slug } = await params;
  const article = await getArticleBySlug(slug, locale);

  if (!article) {
    return {
      title: 'Article Not Found | X114 AI Tool Hub',
      description: 'The article you are looking for could not be found.',
    };
  }

  // 使用 generateStaticMetadata 来确保包含正确的 canonical 和 alternate 链接
  const baseMetadata = generateStaticMetadata(
    `${article.title} | X114 AI Tool Hub`,
    article.summary,
    `/article/${slug}`,
    locale
  );

  // 合并文章特定的 OpenGraph 数据
  return {
    ...baseMetadata,
    keywords: article.tags || [],
    openGraph: {
      ...baseMetadata.openGraph,
      type: 'article',
      publishedTime: article.publishedAt?.toISOString() || new Date().toISOString(),
      modifiedTime: article.updatedAt?.toISOString() || new Date().toISOString(),
      authors: [article.author || 'X114 Team'],
      tags: article.tags || [],
    },
  };
}

export default async function ArticlePage({
  params,
}: {
  params: Promise<{ locale: Locale; slug: string }>;
}) {
  const { locale, slug } = await params;
  const dictionary = await getDictionary(locale);

  // 获取文章数据
  const article = await getArticleBySlug(slug, locale);

  if (!article) {
    notFound();
  }

  // 获取相关文章
  const relatedArticles = await getRelatedArticles(article, 4, locale);

  return (
    <div className="min-h-screen bg-background">
      <Container className="py-24">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* 主要内容区域 */}
          <div className="lg:col-span-3">
            <ArticleContent
              article={article}
              dictionary={dictionary}
              locale={locale}
            />

            {/* 文章内广告 */}
            <InArticleAd adSlot="2345678901" />

            {relatedArticles.length > 0 && (
              <div className="mt-16">
                <RelatedArticles
                  articles={relatedArticles}
                  dictionary={dictionary}
                  locale={locale}
                />
              </div>
            )}
          </div>

          {/* 侧边栏 */}
          <div className="lg:col-span-1">
            <SidebarAd adSlot="7890123456" />
          </div>
        </div>
      </Container>
    </div>
  );
}
