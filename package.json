{"name": "x114", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "build:analyze": "ANALYZE=true next build", "build:check": "node scripts/analyze-bundle.js --check", "start": "next start", "lint": "next lint", "export": "npm run fetch-data && STATIC_EXPORT=true next build", "export:analyze": "npm run fetch-data && STATIC_EXPORT=true ANALYZE=true next build", "verify-deployment": "node scripts/verify-deployment.js", "deploy": "npm run export && wrangler pages deploy out --project-name x114-web", "deploy:verify": "npm run deploy && npm run verify-deployment", "test-rsc-fix": "node scripts/test-rsc-fix.js", "test-route-stability": "node scripts/test-route-stability.js", "static-serve": "npx serve out", "fetch-data": "node scripts/fetch-static-data.js --force", "fetch-data:verbose": "node scripts/fetch-static-data.js --verbose", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "clean": "rm -rf .next out", "clean:all": "rm -rf .next out node_modules/.cache", "prebuild": "npm run clean", "preexport": "npm run clean"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@next/swc-wasm-nodejs": "13.5.1", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@types/node": "20.6.2", "@types/react": "18.2.22", "@types/react-dom": "18.2.7", "autoprefixer": "10.4.15", "axios": "^1.9.0", "cheerio": "^1.0.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "embla-carousel-react": "^8.6.0", "eslint": "8.49.0", "eslint-config-next": "^15.3.2", "input-otp": "^1.4.2", "lucide-react": "^0.446.0", "mongodb": "^6.16.0", "next": "^15.3.2", "next-intl": "^3.11.1", "next-themes": "^0.4.6", "postcss": "8.4.30", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.53.0", "react-resizable-panels": "^3.0.2", "sonner": "^2.0.3", "tailwind-merge": "^2.5.2", "tailwindcss": "3.3.3", "tailwindcss-animate": "^1.0.7", "typescript": "5.2.2", "vaul": "^1.1.2", "zod": "^3.23.8"}, "devDependencies": {"@cloudflare/workers-types": "^4.20250529.0", "@next/bundle-analyzer": "^15.3.2", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^29.5.14", "cssnano": "^7.0.7", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jsdom": "^26.1.0", "ts-node": "^10.9.2"}}