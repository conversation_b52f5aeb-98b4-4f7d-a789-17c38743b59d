/**
 * 广告配置和优化设置
 * 集中管理所有广告相关的配置
 */

export const AD_CONFIG = {
  // Google AdSense 配置
  adsense: {
    publisherId: 'ca-pub-7932958206248396',
    scriptUrl: 'https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js',
    crossOrigin: 'anonymous',
  },

  // 性能优化配置
  performance: {
    // 延迟加载阈值
    lazyLoadThreshold: 0.1,
    
    // 广告加载超时时间（毫秒）
    loadTimeout: 10000,
    
    // 重试次数
    maxRetries: 3,
    
    // 重试间隔（毫秒）
    retryDelay: 2000,
    
    // 是否启用预加载
    enablePreload: true,
    
    // 预加载距离（像素）
    preloadDistance: 500,
  },

  // 响应式断点
  breakpoints: {
    mobile: 768,
    tablet: 1024,
    desktop: 1200,
  },

  // 广告位配置
  slots: {
    // 首页广告位
    home: {
      banner: '1620640133',
      rectangle: '4626949563',
    },
    
    // 工具页面广告位
    tools: {
      banner: '1620640133',
      sidebar: '7890123456',
      inFeed: '2345678901',
    },
    
    // 文章页面广告位
    articles: {
      banner: '1620640133',
      sidebar: '7890123456',
      inArticle: '2345678901',
      rectangle: '4626949563',
    },
    
    // 分类页面广告位
    categories: {
      banner: '1620640133',
      sidebar: '7890123456',
      inFeed: '2345678901',
    },
    
    // 静态页面广告位
    static: {
      banner: '1620640133',
      sidebar: '7890123456',
    },
  },

  // 广告显示规则
  displayRules: {
    // 最小页面停留时间（毫秒）
    minDwellTime: 3000,
    
    // 最大广告密度（每屏广告数量）
    maxAdsPerViewport: 3,
    
    // 广告间最小距离（像素）
    minAdSpacing: 300,
    
    // 是否在移动端显示侧边栏广告
    showSidebarOnMobile: false,
    
    // 是否在打印时隐藏广告
    hideOnPrint: true,
  },

  // A/B 测试配置
  experiments: {
    // 是否启用 A/B 测试
    enabled: false,
    
    // 测试变体
    variants: {
      control: {
        weight: 50,
        config: {},
      },
      variant_a: {
        weight: 25,
        config: {
          // 更多广告位
          extraAds: true,
        },
      },
      variant_b: {
        weight: 25,
        config: {
          // 延迟加载所有广告
          lazyLoadAll: true,
        },
      },
    },
  },

  // 错误处理配置
  errorHandling: {
    // 是否启用错误上报
    enableReporting: process.env.NODE_ENV === 'production',
    
    // 错误上报端点
    reportingEndpoint: '/api/ad-errors',
    
    // 是否显示错误占位符
    showErrorPlaceholder: process.env.NODE_ENV === 'development',
    
    // 错误重试配置
    retryOnError: true,
    maxErrorRetries: 2,
  },

  // 隐私和合规配置
  privacy: {
    // 是否需要用户同意
    requireConsent: true,
    
    // 同意管理平台
    cmpProvider: 'custom', // 'google', 'iab', 'custom'
    
    // 是否启用个性化广告
    enablePersonalization: true,
    
    // GDPR 合规
    gdprCompliant: true,
    
    // CCPA 合规
    ccpaCompliant: true,
  },

  // 调试配置
  debug: {
    // 是否启用调试模式
    enabled: process.env.NODE_ENV === 'development',
    
    // 是否显示广告边框
    showAdBorders: process.env.NODE_ENV === 'development',
    
    // 是否记录广告事件
    logEvents: process.env.NODE_ENV === 'development',
    
    // 是否显示性能指标
    showPerformanceMetrics: process.env.NODE_ENV === 'development',
  },
};

/**
 * 获取当前环境的广告配置
 */
export function getAdConfig() {
  return AD_CONFIG;
}

/**
 * 获取指定页面类型的广告位配置
 */
export function getPageAdSlots(pageType: keyof typeof AD_CONFIG.slots) {
  return AD_CONFIG.slots[pageType] || {};
}

/**
 * 检查是否应该显示广告
 */
export function shouldDisplayAd(context: {
  pageType?: string;
  userAgent?: string;
  viewport?: { width: number; height: number };
  dwellTime?: number;
}): boolean {
  const config = getAdConfig();
  
  // 检查停留时间
  if (context.dwellTime && context.dwellTime < config.displayRules.minDwellTime) {
    return false;
  }
  
  // 检查移动端侧边栏广告
  if (context.viewport && context.viewport.width < config.breakpoints.mobile) {
    if (!config.displayRules.showSidebarOnMobile) {
      return false;
    }
  }
  
  return true;
}

/**
 * 获取 A/B 测试变体
 */
export function getExperimentVariant(): string {
  const adConfig = getAdConfig();

  if (!adConfig.experiments.enabled) {
    return 'control';
  }

  // 简单的权重随机选择
  const random = Math.random() * 100;
  let cumulative = 0;

  for (const [variant, variantConfig] of Object.entries(adConfig.experiments.variants)) {
    cumulative += variantConfig.weight;
    if (random <= cumulative) {
      return variant;
    }
  }

  return 'control';
}

/**
 * 上报广告错误
 */
export async function reportAdError(error: {
  type: string;
  message: string;
  adSlot?: string;
  timestamp: number;
  userAgent: string;
  url: string;
}) {
  const config = getAdConfig();
  
  if (!config.errorHandling.enableReporting) {
    return;
  }
  
  try {
    await fetch(config.errorHandling.reportingEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(error),
    });
  } catch (reportError) {
    console.warn('Failed to report ad error:', reportError);
  }
}
