import Link from 'next/link';
import { ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { getAllCategories } from '@/lib/db/categories';
import { Category } from '@/lib/types';

export default async function CategorySection({ dictionary }: { dictionary: any }) {
  // 获取当前语言
  const locale = dictionary.locale || 'en';

  // 从数据库获取所有类别
  const categories = (await getAllCategories(locale)).slice(0, 12);

  return (
    <section className="py-10">
      <div className="flex flex-wrap items-center justify-between mb-8">
        <h2 className="text-3xl font-bold">{dictionary.home.categories.title}</h2>
        <Link href={`/${locale}/categories`} className="group inline-flex items-center text-blue-600 hover:text-blue-700">
          <span>{dictionary.home.categories.viewAll}</span>
          <ChevronRight className="h-4 w-4 ml-1 transition-transform group-hover:translate-x-0.5" />
        </Link>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {categories.map((category) => (
          <CategoryCard
            key={category.id}
            category={category}
            locale={locale}
          />
        ))}
      </div>
    </section>
  );
}

function CategoryCard({
  category,
  locale
}: {
  category: Category;
  locale: string;
}) {
  return (
    <Link href={`/${locale}/category/${category.slug}`}>
      <Card className="h-[88px] p-4 transition-all duration-300 hover:shadow-md hover:scale-[1.02] group">
        <div className="flex items-center justify-between h-full">
          <div className="flex items-center flex-1 min-w-0 pr-2">
            <div className={`w-12 h-12 rounded-lg flex-shrink-0 flex items-center justify-center text-2xl group-hover:scale-110 transition-all duration-300`}
              style={{ backgroundColor: category.color }}>
              {category.icon}
            </div>
            <div className="ml-4 flex-1 min-w-0">
              <h3 className="font-medium line-clamp-2">{category.name}</h3>
              <p className="text-sm text-muted-foreground mt-0.5 truncate whitespace-nowrap">{category.description}</p>
            </div>
          </div>
          <Button variant="ghost" size="icon" className="opacity-0 group-hover:opacity-100 transition-opacity flex-shrink-0">
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </Card>
    </Link>
  );
}
