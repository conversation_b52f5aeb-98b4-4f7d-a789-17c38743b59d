import React from 'react';
import { render, screen, waitFor, act } from '@testing-library/react';
import '@testing-library/jest-dom';
import ResponsiveAd, { ResponsiveBannerAd, ResponsiveRectangleAd } from '@/components/ads/responsive-ad';

// Mock IntersectionObserver
const mockIntersectionObserver = jest.fn();
mockIntersectionObserver.mockReturnValue({
  observe: () => null,
  unobserve: () => null,
  disconnect: () => null,
});
window.IntersectionObserver = mockIntersectionObserver;

// Mock window.adsbygoogle
Object.defineProperty(window, 'adsbygoogle', {
  writable: true,
  value: [],
});

describe('ResponsiveAd', () => {
  beforeEach(() => {
    // Reset window size
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 1024,
    });
  });

  it('renders placeholder in development mode', () => {
    const originalEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'development';

    render(
      <ResponsiveAd
        adSlot="test-slot"
        size="rectangle"
      />
    );

    expect(screen.getByText(/Responsive Ad Placeholder/)).toBeInTheDocument();
    expect(screen.getByText(/test-slot/)).toBeInTheDocument();

    process.env.NODE_ENV = originalEnv;
  });

  it('renders loading placeholder when lazy loading', () => {
    const originalEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'production';

    render(
      <ResponsiveAd
        adSlot="test-slot"
        size="rectangle"
        lazy={true}
      />
    );

    expect(document.querySelector('.ad-loading')).toBeInTheDocument();

    process.env.NODE_ENV = originalEnv;
  });

  it('applies correct data attributes', () => {
    render(
      <ResponsiveAd
        adSlot="test-slot"
        size="banner"
        position="header"
      />
    );

    const adContainer = document.querySelector('[data-ad-size]');
    expect(adContainer).toHaveAttribute('data-ad-size', 'banner');
    expect(adContainer).toHaveAttribute('data-ad-position', 'header');
  });

  it('adjusts size based on screen width', async () => {
    // Mock mobile screen size
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 500,
    });

    render(
      <ResponsiveAd
        adSlot="test-slot"
        size="banner"
        mobileSize="mobile-banner"
      />
    );

    // Trigger resize event
    await act(async () => {
      window.dispatchEvent(new Event('resize'));
    });

    await waitFor(() => {
      const adContainer = document.querySelector('[data-ad-size]');
      expect(adContainer).toHaveAttribute('data-ad-size', 'mobile-banner');
    });
  });
});

describe('ResponsiveBannerAd', () => {
  it('renders with correct default props', () => {
    render(<ResponsiveBannerAd adSlot="banner-slot" />);

    const adContainer = document.querySelector('[data-ad-size]');
    expect(adContainer).toHaveAttribute('data-ad-size', 'banner');
    expect(adContainer?.parentElement).toHaveClass('w-full', 'max-w-4xl', 'mx-auto');
  });
});

describe('ResponsiveRectangleAd', () => {
  it('renders with correct default props', () => {
    render(<ResponsiveRectangleAd adSlot="rectangle-slot" />);

    const adContainer = document.querySelector('[data-ad-size]');
    expect(adContainer).toHaveAttribute('data-ad-size', 'rectangle');
    expect(adContainer?.parentElement).toHaveClass('max-w-sm', 'mx-auto');
  });
});

describe('Ad Utils', () => {
  it('generates correct ad dimensions', () => {
    const { getAdDimensions } = require('@/components/ads/core/ad-utils');
    
    expect(getAdDimensions('banner')).toEqual({ width: 728, height: 90 });
    expect(getAdDimensions('rectangle')).toEqual({ width: 300, height: 250 });
    expect(getAdDimensions('responsive')).toEqual({ width: '100%', height: 'auto' });
  });

  it('generates correct ad styles', () => {
    const { generateAdStyle } = require('@/components/ads/core/ad-utils');
    
    const style = generateAdStyle('banner');
    expect(style).toEqual({
      display: 'block',
      width: 728,
      height: 90,
      maxWidth: '100%',
    });
  });

  it('gets correct responsive ad size', () => {
    const { getResponsiveAdSize } = require('@/components/ads/core/ad-utils');
    
    // Mobile
    expect(getResponsiveAdSize('banner', 500)).toBe('mobile-banner');
    expect(getResponsiveAdSize('skyscraper', 500)).toBe('rectangle');
    
    // Tablet
    expect(getResponsiveAdSize('skyscraper', 800)).toBe('rectangle');
    
    // Desktop
    expect(getResponsiveAdSize('banner', 1200)).toBe('banner');
  });
});

describe('Ad Container Classes', () => {
  it('generates correct container classes', () => {
    const { getAdContainerClasses } = require('@/components/ads/core/ad-utils');
    
    const classes = getAdContainerClasses('sidebar', 'lg', true);
    expect(classes).toContain('ad-container');
    expect(classes).toContain('ad-position-sidebar');
    expect(classes).toContain('my-6');
    expect(classes).toContain('flex justify-center items-center');
  });
});

describe('Ad Visibility Conditions', () => {
  it('checks visibility conditions correctly', () => {
    const { shouldShowAd } = require('@/components/ads/core/ad-utils');
    
    // No conditions
    expect(shouldShowAd()).toBe(true);
    
    // Width conditions (mocked)
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 800,
    });
    
    expect(shouldShowAd({ minWidth: 600 })).toBe(true);
    expect(shouldShowAd({ minWidth: 1000 })).toBe(false);
    expect(shouldShowAd({ maxWidth: 1000 })).toBe(true);
    expect(shouldShowAd({ maxWidth: 600 })).toBe(false);
  });
});
