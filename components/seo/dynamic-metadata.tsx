'use client';

import { useMetaTag, usePageTitle } from './hooks/useHeadElement';
import { Locale } from '@/lib/i18n/config';
import { useEffect } from 'react';

interface DynamicMetadataProps {
  locale: Locale;
  title: string;
  description: string;
  keywords?: string[];
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  twitterTitle?: string;
  twitterDescription?: string;
  twitterImage?: string;
  noindex?: boolean; // 添加noindex属性，用于指示搜索引擎不要索引页面
}

/**
 * 组件用于根据当前语言动态更新页面元数据
 * 这对于客户端语言切换非常有用
 */
export default function DynamicMetadata({
  // locale 参数保留用于未来可能的本地化扩展
  locale,
  title,
  description,
  keywords,
  ogTitle,
  ogDescription,
  ogImage,
  twitterTitle,
  twitterDescription,
  twitterImage,
  noindex
}: DynamicMetadataProps) {
  // 更新页面标题
  usePageTitle(title);

  // 更新基本元数据
  useMetaTag('description', description);

  // 处理 robots 标签
  useMetaTag('robots', noindex ? 'noindex, nofollow' : 'index, follow');

  // 使用 useEffect 处理条件性的元标签
  useEffect(() => {
    // 更新关键词
    if (keywords && keywords.length > 0) {
      const keywordsContent = keywords.join(', ');
      const keywordsTag = document.querySelector('meta[name="keywords"]');
      if (keywordsTag) {
        keywordsTag.setAttribute('content', keywordsContent);
      } else {
        const meta = document.createElement('meta');
        meta.name = 'keywords';
        meta.content = keywordsContent;
        document.head.appendChild(meta);
      }
    }

    // 处理 Open Graph 图片
    if (ogImage) {
      const ogImageTag = document.querySelector('meta[property="og:image"]');
      if (ogImageTag) {
        ogImageTag.setAttribute('content', ogImage);
      } else {
        const meta = document.createElement('meta');
        meta.setAttribute('property', 'og:image');
        meta.content = ogImage;
        document.head.appendChild(meta);
      }
    }

    // 处理 Twitter 图片
    if (twitterImage) {
      const twitterImageTag = document.querySelector('meta[name="twitter:image"]');
      if (twitterImageTag) {
        twitterImageTag.setAttribute('content', twitterImage);
      } else {
        const meta = document.createElement('meta');
        meta.name = 'twitter:image';
        meta.content = twitterImage;
        document.head.appendChild(meta);
      }
    }
  }, [keywords, ogImage, twitterImage]);

  // 更新 Open Graph 元数据 (不在条件中)
  useMetaTag('og:title', ogTitle || title, true);
  useMetaTag('og:description', ogDescription || description, true);

  // 更新 Twitter 卡片元数据 (不在条件中)
  useMetaTag('twitter:title', twitterTitle || title);
  useMetaTag('twitter:description', twitterDescription || description);

  // 这个组件不渲染任何内容
  return null;
}
