# 部署说明

## 问题描述

网站访问 `https://x114.org/` 时会自动跳转到 `https://x114.org/en.txt` 并不断刷新，同时 RSC 数据流请求 `https://x114.org/en.txt?_rsc=1072r` 返回 404 错误。

**根本原因**：
1. **RSC 路径映射问题**：Next.js 15 请求 `/en/category/slug.txt`，但文件实际在 `/en/category/slug/index.txt`
2. **Cloudflare Pages 路由优先级问题**：根路径被错误路由到 `.txt` 文件
3. **静态导出结构不匹配**：RSC 请求路径与实际文件路径不一致

## 解决方案

### 1. 简化配置策略

- ✅ **最小化重定向规则**：只处理必要的根路径重定向
- ✅ **正确的 MIME 类型**：为 `.txt` 文件设置 `text/x-component`
- ✅ **移除干扰性中间件**：不干预 Next.js 的 RSC 数据流
- ✅ **优化 Next.js 配置**：移除可能干扰 RSC 的设置

### 2. 关键原则

- **保持简单**：避免过度复杂的路由处理
- **不干预 RSC**：让 Next.js 的 RSC 数据流正常工作
- **精确重定向**：只处理根路径重定向

### 3. 部署步骤

```bash
# 1. 清理并重新构建
npm run clean
npm run export

# 2. 验证构建结果
ls -la out/  # 确认 .txt 文件存在（这是正常的）

# 3. 部署到 Cloudflare Pages
npm run deploy

# 4. 验证部署结果
npm run verify-deployment
```

### 4. 手动验证

部署后，手动检查以下 URL：

- `https://x114.org/` → 应该重定向到 `https://x114.org/en/`
- `https://x114.org/en/` → 应该显示英文首页
- `https://x114.org/zh/` → 应该显示中文首页
- `https://x114.org/en.txt` → 应该返回 RSC 数据流（200 状态码）

### 5. 故障排除

如果问题仍然存在：

1. **清除 Cloudflare 缓存**：
   ```bash
   # 在 Cloudflare Dashboard 中清除缓存
   # 或使用 API
   curl -X POST "https://api.cloudflare.com/client/v4/zones/{zone_id}/purge_cache" \
        -H "Authorization: Bearer {api_token}" \
        -H "Content-Type: application/json" \
        --data '{"purge_everything":true}'
   ```

2. **检查 Cloudflare Pages 设置**：
   - 确认构建命令：`npm run export`
   - 确认输出目录：`out`
   - 确认环境变量设置正确

3. **本地测试**：
   ```bash
   npm run static-serve
   # 访问 http://localhost:3000 测试重定向
   ```

### 6. 监控和维护

- 使用 `npm run verify-deployment` 定期检查网站状态
- 监控 Cloudflare Analytics 查看重定向和错误率
- 定期检查构建日志确保没有生成不必要的 `.txt` 文件

## 技术细节

### Cloudflare Pages 重定向规则

```
/ /en/ 302
/en /en/ 301

# 根级别语言 RSC 数据流
/en.txt /en/index.txt 200
/zh.txt /zh/index.txt 200

# 页面级别 RSC 路径映射
/:locale/category/:slug.txt /:locale/category/:slug/index.txt 200
/:locale/tool/:slug.txt /:locale/tool/:slug/index.txt 200
```

### Next.js 配置关键点

```javascript
{
  output: 'export',
  trailingSlash: true,
  skipTrailingSlashRedirect: true
}
```

### RSC 数据流处理

```
/*.txt
  Content-Type: text/x-component
  Cache-Control: public, max-age=0, must-revalidate
```

### 关键问题解释

**Next.js 15 RSC 路径映射问题**：

有两种类型的 RSC 请求需要处理：

1. **根级别语言页面**：
   - Next.js 请求：`/en.txt?_rsc=xxx`
   - 实际文件：`/en/index.txt`
   - 重写规则：`/en.txt /en/index.txt 200`

2. **子页面**：
   - Next.js 请求：`/en/category/coding-development.txt?_rsc=xxx`
   - 实际文件：`/en/category/coding-development/index.txt`
   - 重写规则：`/:locale/category/:slug.txt /:locale/category/:slug/index.txt 200`

这就是为什么需要精确的路径重写规则，而不是简单的重定向。

## 联系支持

如果问题仍然存在，请提供：
- 错误截图
- 浏览器开发者工具的网络请求日志
- Cloudflare Pages 的构建日志