/**
 * 自定义错误类型
 */
export enum ErrorType {
  NOT_FOUND = 'NOT_FOUND',
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  BAD_REQUEST = 'BAD_REQUEST',
  INTERNAL_SERVER = 'INTERNAL_SERVER',
  DATABASE = 'DATABASE',
  NETWORK = 'NETWORK',
  VALIDATION = 'VALIDATION'
}

/**
 * 自定义错误类
 */
export class AppError extends Error {
  type: ErrorType;
  statusCode: number;
  details?: any;

  constructor(
    message: string,
    type: ErrorType = ErrorType.INTERNAL_SERVER,
    statusCode: number = 500,
    details?: any
  ) {
    super(message);
    this.name = 'AppError';
    this.type = type;
    this.statusCode = statusCode;
    this.details = details;
  }

  /**
   * 创建 404 Not Found 错误
   * @param message 错误消息
   * @param details 错误详情
   * @returns AppError 实例
   */
  static notFound(message: string = 'Resource not found', details?: any): AppError {
    return new AppError(message, ErrorType.NOT_FOUND, 404, details);
  }

  /**
   * 创建 401 Unauthorized 错误
   * @param message 错误消息
   * @param details 错误详情
   * @returns AppError 实例
   */
  static unauthorized(message: string = 'Unauthorized', details?: any): AppError {
    return new AppError(message, ErrorType.UNAUTHORIZED, 401, details);
  }

  /**
   * 创建 403 Forbidden 错误
   * @param message 错误消息
   * @param details 错误详情
   * @returns AppError 实例
   */
  static forbidden(message: string = 'Forbidden', details?: any): AppError {
    return new AppError(message, ErrorType.FORBIDDEN, 403, details);
  }

  /**
   * 创建 400 Bad Request 错误
   * @param message 错误消息
   * @param details 错误详情
   * @returns AppError 实例
   */
  static badRequest(message: string = 'Bad request', details?: any): AppError {
    return new AppError(message, ErrorType.BAD_REQUEST, 400, details);
  }

  /**
   * 创建 500 Internal Server 错误
   * @param message 错误消息
   * @param details 错误详情
   * @returns AppError 实例
   */
  static internal(message: string = 'Internal server error', details?: any): AppError {
    return new AppError(message, ErrorType.INTERNAL_SERVER, 500, details);
  }

  /**
   * 创建数据库错误
   * @param message 错误消息
   * @param details 错误详情
   * @returns AppError 实例
   */
  static database(message: string = 'Database error', details?: any): AppError {
    return new AppError(message, ErrorType.DATABASE, 500, details);
  }

  /**
   * 创建网络错误
   * @param message 错误消息
   * @param details 错误详情
   * @returns AppError 实例
   */
  static network(message: string = 'Network error', details?: any): AppError {
    return new AppError(message, ErrorType.NETWORK, 500, details);
  }

  /**
   * 创建验证错误
   * @param message 错误消息
   * @param details 错误详情
   * @returns AppError 实例
   */
  static validation(message: string = 'Validation error', details?: any): AppError {
    return new AppError(message, ErrorType.VALIDATION, 400, details);
  }
}

/**
 * 处理错误并返回适当的响应
 * @param error 错误对象
 * @returns 错误响应
 */
export function handleError(error: unknown): { message: string; statusCode: number; type: string } {
  console.error('Error:', error);

  // 如果是自定义错误，直接返回
  if (error instanceof AppError) {
    return {
      message: error.message,
      statusCode: error.statusCode,
      type: error.type
    };
  }

  // 如果是普通错误，转换为内部服务器错误
  const message = error instanceof Error ? error.message : 'An unknown error occurred';
  return {
    message,
    statusCode: 500,
    type: ErrorType.INTERNAL_SERVER
  };
}

/**
 * 捕获异步函数中的错误
 * @param fn 异步函数
 * @returns 包装后的异步函数
 */
export function catchAsync<T>(
  fn: (...args: any[]) => Promise<T>
): (...args: any[]) => Promise<T> {
  return async (...args: any[]) => {
    try {
      return await fn(...args);
    } catch (error) {
      throw error instanceof AppError ? error : AppError.internal(
        error instanceof Error ? error.message : 'An unknown error occurred',
        error
      );
    }
  };
}
