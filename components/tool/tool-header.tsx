'use client';

import Image from 'next/image';
import { useState } from 'react';
import { Tool } from '@/lib/types';
import { Locale } from '@/lib/i18n/config';
import { Copy, Star } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';

interface ToolHeaderProps {
  tool: Tool;
  dictionary: any;
  locale: Locale;
}

export default function ToolHeader({ tool, dictionary, locale }: ToolHeaderProps) {
  const [copied, setCopied] = useState(false);

  const handleCopyLink = async () => {
    try {
      const url = window.location.href;
      await navigator.clipboard.writeText(url);
      setCopied(true);
      toast.success(dictionary.tool.share.copySuccess);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      toast.error(dictionary.tool.share.copyError);
    }
  };

  const handleSocialShare = (platform: string) => {
    const url = encodeURIComponent(window.location.href);
    const title = encodeURIComponent(tool.name);
    const description = encodeURIComponent(tool.description);

    let shareUrl = '';

    switch (platform) {
      case 'twitter':
        shareUrl = `https://twitter.com/intent/tweet?url=${url}&text=${title} - ${description}`;
        break;
      case 'facebook':
        shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${url}`;
        break;
      case 'linkedin':
        shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${url}`;
        break;
    }

    if (shareUrl) {
      window.open(shareUrl, '_blank', 'width=600,height=400');
    }
  };

  return (
    <div className="flex flex-col lg:flex-row gap-8 mb-12">
      {/* Left side - Tool info */}
      <div className="lg:w-2/3">
        {/* Title */}
        <div className="flex items-center gap-2 mb-6">
          {tool.website_logo && (
            <Image
              src={tool.website_logo}
              alt={`${tool.name} logo`}
              width={36}
              height={36}
              className="rounded-sm"
            />
          )}
          <h1 className="text-3xl md:text-4xl font-bold text-foreground">{tool.name}</h1>
        </div>

        {/* Rating */}
        <div className="flex items-center gap-2 mb-6">
          <div className="flex">
            {[1, 2, 3, 4, 5].map((star) => (
              <Star
                key={star}
                className={`h-5 w-5 ${
                  star <= Math.round(tool.rating || 0)
                    ? 'text-yellow-500 fill-yellow-500'
                    : 'text-gray-300 fill-gray-300'
                }`}
              />
            ))}
          </div>
          <span className="font-semibold mr-2">{tool.rating || 0}</span>

          <div className="flex items-center gap-2">
            {tool.featured && dictionary.tool?.badges?.featured && (
              <Badge className="bg-gradient-to-r from-amber-500 to-orange-500 text-white font-medium px-3 py-1">{dictionary.tool.badges.featured}</Badge>
            )}
            {tool.trending && dictionary.tool?.badges?.trending && (
              <Badge className="bg-gradient-to-r from-pink-500 to-purple-500 text-white font-medium px-3 py-1">{dictionary.tool.badges.trending}</Badge>
            )}
            {tool.isNew && dictionary.tool?.badges?.new && (
              <Badge className="bg-gradient-to-r from-green-500 to-emerald-500 text-white font-medium px-3 py-1">{dictionary.tool.badges.new}</Badge>
            )}
          </div>
        </div>

        {/* Tool details */}
        <div className="space-y-4 mb-6">
          <div>
            <p className="text-foreground mt-1">{tool.description}</p>
          </div>
        </div>

        {/* Action button */}
        <Button
          size="lg"
          className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-3"
          asChild
        >
          <a href={tool.website || tool.url || '#'} target="_blank" rel="noopener noreferrer">
            {dictionary.tool.openWebsite}
          </a>
        </Button>

        {/* Social buttons */}
        <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="h-16 w-16 p-0 hover:bg-yellow-100 hover:text-yellow-600"
              onClick={handleCopyLink}
              title={copied ? dictionary.tool.share.linkCopied : dictionary.tool.share.copyLink}
            >
              {copied ? (
                <svg className="h-6 w-6 text-green-600" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M20.285 2l-11.285 11.567-5.286-5.011-3.714 3.716 9 8.728 15-15.285z"/>
                </svg>
              ) : (
                <Copy className="h-6 w-6" />
              )}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="h-16 w-16 p-0 hover:bg-blue-100 hover:text-blue-600"
              onClick={() => handleSocialShare('twitter')}
              title={dictionary.tool.share.shareToTwitter}
            >
              <svg className="h-6 w-6" viewBox="0 0 24 24" fill="currentColor">
                <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
              </svg>
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="h-16 w-16 p-0 hover:bg-blue-100 hover:text-blue-800"
              onClick={() => handleSocialShare('facebook')}
              title={dictionary.tool.share.shareToFacebook}
            >
              <svg className="h-6 w-6" viewBox="0 0 24 24" fill="currentColor">
                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
              </svg>
            </Button>
          </div>
      </div>

      {/* Right side - Tool preview */}
      <div className="lg:w-1/3">
        <div className="relative aspect-video rounded-xl overflow-hidden border shadow-lg hover:shadow-xl transition-shadow duration-300 bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-950 dark:to-purple-950">
          <Image
            src={tool.image || '/images/placeholder.jpg'}
            alt={tool.name}
            fill
            style={{ objectFit: 'cover' }}
            priority
            className="transition-transform duration-300 hover:scale-105"
          />
        </div>
      </div>
    </div>
  );
}