'use client';

import { Locale } from '@/lib/i18n/config';
import { Tool, Category, Article } from '@/lib/types';
import CanonicalLink from './canonical-link';
import DynamicMetadata from './dynamic-metadata';
import HreflangTags from './hreflang-tags';
import StructuredData from './structured-data';

// 网站基本信息
const siteUrl = 'https://x114.org';

interface PageSeoProps {
  locale: Locale;
  title: string;
  description: string;
  path: string;
  keywords?: string[];
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  twitterTitle?: string;
  twitterDescription?: string;
  twitterImage?: string;
  noindex?: boolean;
  structuredDataType?: 'website' | 'tool' | 'category' | 'article' | 'organization';
  structuredData?: Tool | Category | Article;
}

/**
 * 统一的页面 SEO 组件
 * 包含所有 SEO 相关设置
 */
export default function PageSeo({
  locale,
  title,
  description,
  path,
  keywords,
  ogTitle,
  ogDescription,
  ogImage,
  twitterTitle,
  twitterDescription,
  twitterImage,
  noindex,
  structuredDataType,
  structuredData
}: PageSeoProps) {
  // 规范链接
  const canonicalUrl = `${siteUrl}/${locale}${path}`;
  
  return (
    <>
      {/* 规范链接 */}
      <CanonicalLink url={canonicalUrl} />
      
      {/* 语言版本链接 */}
      <HreflangTags path={path} locale={locale} baseUrl={siteUrl} />
      
      {/* 页面元数据 */}
      <DynamicMetadata
        locale={locale}
        title={title}
        description={description}
        keywords={keywords}
        ogTitle={ogTitle}
        ogDescription={ogDescription}
        ogImage={ogImage}
        twitterTitle={twitterTitle}
        twitterDescription={twitterDescription}
        twitterImage={twitterImage}
        noindex={noindex}
      />
      
      {/* 结构化数据 */}
      {structuredDataType && (
        <StructuredData
          type={structuredDataType}
          data={structuredData}
          locale={locale}
        />
      )}
      
      {/* 网站和组织结构化数据 */}
      <StructuredData type="website" />
      <StructuredData type="organization" />
    </>
  );
}
