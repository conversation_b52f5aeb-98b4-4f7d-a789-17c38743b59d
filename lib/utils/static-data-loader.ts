/**
 * 静态数据加载工具
 * 提供更可靠的静态数据加载机制
 */

import { Tool, Category, Article } from '@/lib/types';
import { isStaticExport, isDevelopment } from './environment';
import { StaticExportError, withFallback } from './static-export-error';

// 数据类型
type DataType = 'tools' | 'categories' | 'articles';

// 支持的语言
type SupportedLanguage = 'en' | 'zh' | 'tw' | 'ko' | 'ja' | 'pt' | 'es' | 'de' | 'fr' | 'vi';

// 默认数据
const defaultData = {
  tools: [] as Tool[],
  categories: [] as Category[],
  articles: [] as Article[]
};

/**
 * 处理日期字符串，将其转换为 Date 对象
 * @param item 数据项
 * @returns 处理后的数据项
 */
function processDateFields<T>(item: T): T {
  if (!item) return item;

  const result = { ...item } as any;
  const dateFields = ['createdAt', 'updatedAt', 'publishedAt', 'lastModified'];

  for (const field of dateFields) {
    if (typeof result[field] === 'string') {
      try {
        result[field] = new Date(result[field]);
      } catch (error) {
        console.warn(`Failed to parse date field ${field}:`, error);
      }
    }
  }

  return result as T;
}

/**
 * 处理Article数据，转换时间戳字段为Date对象
 * @param article 文章数据
 * @returns 处理后的文章数据
 */
function processArticleFields(article: Article): Article {
  if (!article) return article;

  const result = { ...article } as any;

  // 处理时间戳字段
  if (typeof result.published === 'number') {
    result.publishedAt = new Date(result.published * 1000);
  }
  if (typeof result.created_at === 'number') {
    result.createdAt = new Date(result.created_at * 1000);
  }
  if (typeof result.updated_at === 'number') {
    result.updatedAt = new Date(result.updated_at * 1000);
  }

  // 设置默认值
  if (!result.id && result._id) {
    result.id = result._id;
  }
  if (!result.author) {
    result.author = 'X114 Team';
  }
  if (!result.tags) {
    result.tags = [];
  }
  if (!result.category) {
    result.category = 'general';
  }
  if (typeof result.featured === 'undefined') {
    result.featured = false;
  }

  return result as Article;
}

/**
 * 获取静态数据文件路径
 * @param dataType 数据类型
 * @param language 语言
 * @returns 文件路径
 */
function getStaticDataFilePath(dataType: DataType, language: SupportedLanguage): string {
  // 在服务器端，使用绝对路径
  if (typeof process !== 'undefined' && process.cwd) {
    const path = require('path');
    return path.join(process.cwd(), 'lib', 'static-data', `${dataType}-${language}.json`);
  }

  // 在客户端，使用相对路径
  return `/lib/static-data/${dataType}-${language}.json`;
}

/**
 * 加载静态数据
 * @param dataType 数据类型
 * @param language 语言
 * @returns 静态数据
 */
export function loadStaticData<T>(dataType: DataType, language: SupportedLanguage = 'en'): T[] {
  // 如果不是静态导出，返回空数组
  if (!isStaticExport()) {
    return [] as unknown as T[];
  }

  return withFallback(() => {
    // 在服务器端，使用 fs 模块读取文件
    if (typeof window === 'undefined' && typeof process !== 'undefined' && typeof process.cwd === 'function') {
      try {
        const fs = require('fs');
        const filePath = getStaticDataFilePath(dataType, language);

        // 检查文件是否存在
        if (fs.existsSync(filePath)) {
          try {
            const fileContent = fs.readFileSync(filePath, 'utf-8');
            const data = JSON.parse(fileContent);

            if (!Array.isArray(data)) {
              throw StaticExportError.invalidData(dataType, {
                message: 'Data is not an array',
                language
              });
            }

            return data as T[];
          } catch (parseError) {
            if (parseError instanceof SyntaxError) {
              throw StaticExportError.invalidData(dataType, {
                message: 'Invalid JSON',
                language,
                error: parseError
              });
            }
            throw parseError;
          }
        } else {
          throw StaticExportError.fileNotFound(filePath);
        }
      } catch (requireError) {
        // fs 模块不可用，继续到客户端逻辑
        if (isDevelopment()) {
          console.log(`[Static Data] fs module not available, using fallback for ${dataType} (${language})`);
        }
      }
    }

    // 在客户端环境中，使用内联数据
    // 注意：在静态导出过程中，我们不能使用 require 或 import 导入 JSON 文件
    // 因为这些文件在构建时可能不存在，或者路径无法被正确解析
    if (isDevelopment()) {
      console.log(`[Static Data] Using fallback data for ${dataType} (${language}) in client environment`);
    }

    // 返回空数组作为默认值
    return [] as T[];
  }, (defaultData[dataType] || []) as unknown as T[]);
}

/**
 * 获取工具列表
 * @param language 语言
 * @returns 工具列表
 */
export function getStaticTools(language: SupportedLanguage = 'en'): Tool[] {
  const tools = loadStaticData<Tool>('tools', language);
  return tools.map(tool => processDateFields(tool));
}

/**
 * 根据 ID 获取工具
 * @param id 工具 ID
 * @param language 语言
 * @returns 工具
 */
export function getStaticToolById(id: string, language: SupportedLanguage = 'en'): Tool | undefined {
  const tools = getStaticTools(language);
  const tool = tools.find(tool => tool.id === id);
  return tool ? processDateFields(tool) : undefined;
}

/**
 * 根据 Slug 获取工具
 * @param slug 工具 Slug
 * @param language 语言
 * @returns 工具
 */
export function getStaticToolBySlug(slug: string, language: SupportedLanguage = 'en'): Tool | undefined {
  const tools = getStaticTools(language);
  const tool = tools.find(tool => tool.slug === slug);
  return tool ? processDateFields(tool) : undefined;
}

/**
 * 获取类别列表
 * @param language 语言
 * @returns 类别列表
 */
export function getStaticCategories(language: SupportedLanguage = 'en'): Category[] {
  const categories = loadStaticData<Category>('categories', language);
  return categories.map(category => processDateFields(category));
}

/**
 * 根据 ID 获取类别
 * @param id 类别 ID
 * @param language 语言
 * @returns 类别
 */
export function getStaticCategoryById(id: string, language: SupportedLanguage = 'en'): Category | undefined {
  const categories = getStaticCategories(language);
  const category = categories.find(category => category.id === id);
  return category ? processDateFields(category) : undefined;
}

/**
 * 获取文章列表
 * @param language 语言
 * @returns 文章列表
 */
export function getStaticArticles(language: SupportedLanguage = 'en'): Article[] {
  const articles = loadStaticData<Article>('articles', language);
  return articles.map(article => processArticleFields(article));
}

/**
 * 根据 ID 获取文章
 * @param id 文章 ID
 * @param language 语言
 * @returns 文章
 */
export function getStaticArticleById(id: string, language: SupportedLanguage = 'en'): Article | undefined {
  const articles = getStaticArticles(language);
  const article = articles.find(article => article.id === id || article._id === id);
  return article ? processArticleFields(article) : undefined;
}

/**
 * 根据别名获取文章
 * @param slug 文章别名
 * @param language 语言
 * @returns 文章
 */
export function getStaticArticleBySlug(slug: string, language: SupportedLanguage = 'en'): Article | undefined {
  const articles = getStaticArticles(language);
  const article = articles.find(article => article.slug === slug);
  return article ? processArticleFields(article) : undefined;
}
