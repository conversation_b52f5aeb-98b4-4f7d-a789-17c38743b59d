# Cloudflare Pages 项目配置文件
# 项目名称
name = "x114-web"
compatibility_date = "2024-01-15"
compatibility_flags = ["nodejs_compat"]

# Pages 特定配置
pages_build_output_dir = "out"

# 生产环境配置
[env.production]
# 环境变量
[env.production.vars]
ENVIRONMENT = "production"

# D1 数据库绑定
[[env.production.d1_databases]]
binding = "DB"
database_name = "x114-prod"
database_id = "f1d2a683-e824-4bad-9c05-60b11f3214d4"

# 测试环境配置
[env.preview]
# 环境变量
[env.preview.vars]
ENVIRONMENT = "staging"

# D1 数据库绑定
[[env.preview.d1_databases]]
binding = "DB"
database_name = "x114-test"
database_id = "cc42a19c-b03b-40bc-9ab9-f4762ea7bb9e"

# 本地开发配置
[[d1_databases]]
binding = "DB"
database_name = "x114-local"
database_id = "local"

# KV 存储（如果需要缓存）
# [[kv_namespaces]]
# binding = "CACHE"
# id = "your-kv-namespace-id"

# 环境变量（敏感信息通过 wrangler secret 命令设置）
# 使用以下命令设置密钥：
# wrangler pages secret put RESEND_API_KEY --project-name x114-web
# wrangler pages secret put ADMIN_EMAIL --project-name x114-web
# wrangler pages secret put ADMIN_API_KEY --project-name x114-web
