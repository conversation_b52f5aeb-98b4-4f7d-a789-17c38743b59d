import { MongoClient, Db } from 'mongodb';
import 'dotenv/config';

// 从环境变量中获取 MongoDB 连接信息
const uri = process.env.MONGODB_URI || 'mongodb://localhost:27017';
const dbName = process.env.MONGODB_DB || 'x114';

// 创建一个 MongoDB 客户端实例
const client = new MongoClient(uri);
let db: Db | null = null;

// 连接到 MongoDB 并获取数据库实例
export async function connectToDatabase(): Promise<Db> {
  if (db) {
    return db;
  }

  try {
    await client.connect();
    console.log('Connected to MongoDB');
    db = client.db(dbName);
    return db;
  } catch (error) {
    console.error('Failed to connect to MongoDB:', error);
    throw error;
  }
}

// 关闭 MongoDB 连接
export async function closeConnection(): Promise<void> {
  if (client) {
    await client.close();
    console.log('MongoDB connection closed');
    db = null;
  }
}

// 获取集合名称
export const COLLECTIONS = {
  TOOLS: 'tools',
  CATEGORIES: 'categories',
  ARTICLES: 'articles',
  USERS: 'users',
  REVIEWS: 'reviews',
};
