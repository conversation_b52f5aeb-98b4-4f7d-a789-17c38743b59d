import Link from 'next/link';
import { getDictionary } from '@/lib/i18n/get-dictionary';
import { Locale } from '@/lib/i18n/config';
import { Github, Twitter, Linkedin } from 'lucide-react';
import { Button } from '@/components/ui/button';

export default async function Footer({ locale }: { locale: Locale }) {
  const dictionary = await getDictionary(locale);

  return (
    <footer className="bg-muted/30 border-t">
      <div className="container mx-auto px-4 py-16">
        <div className="grid grid-cols-1 md:grid-cols-12 gap-8 mb-12">
          <div className="md:col-span-4 space-y-4">
            <Link href={`/${locale}`} className="inline-block">
              <span className="text-2xl font-bold bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent">
                {dictionary.common.appName}
              </span>
            </Link>
            <p className="text-sm text-muted-foreground max-w-xs leading-relaxed">
              {dictionary.common.slogan}
            </p>
            <div className="flex space-x-2 pt-0">
              <Button variant="ghost" size="icon" aria-label="GitHub" className="text-muted-foreground hover:text-foreground hover:bg-transparent">
                <Github className="h-5 w-5" />
              </Button>
              <Button variant="ghost" size="icon" aria-label="Twitter" className="text-muted-foreground hover:text-foreground hover:bg-transparent">
                <Twitter className="h-5 w-5" />
              </Button>
              <Button variant="ghost" size="icon" aria-label="LinkedIn" className="text-muted-foreground hover:text-foreground hover:bg-transparent">
                <Linkedin className="h-5 w-5" />
              </Button>
            </div>
          </div>

          <div className="md:col-span-2 space-y-4">
            <h3 className="font-semibold text-sm uppercase tracking-wider">{dictionary.footer.product || 'Product'}</h3>
            <ul className="space-y-3">
              <li>
                <Link href={`/${locale}/tools`} className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                  {dictionary.footer.tools || 'Tools'}
                </Link>
              </li>
              <li>
                <Link href={`/${locale}/categories`} className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                  {dictionary.footer.categories || 'Categories'}
                </Link>
              </li>
              <li>
                <Link href={`/${locale}/articles`} className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                  {dictionary.articles?.title || 'Articles'}
                </Link>
              </li>
            </ul>
          </div>

          <div className="md:col-span-2 space-y-4">
            <h3 className="font-semibold text-sm uppercase tracking-wider">{dictionary.footer.resources || 'Resources'}</h3>
            <ul className="space-y-3">
              <li>
                <Link href={`/${locale}/submit`} className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                  {dictionary.footer.submit || 'Submit Tool'}
                </Link>
              </li>
              <li>
                <Link href={`/${locale}/search`} className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                  {dictionary.footer.search || 'Search'}
                </Link>
              </li>
            </ul>
          </div>

          <div className="md:col-span-2 space-y-4">
            <h3 className="font-semibold text-sm uppercase tracking-wider">{dictionary.footer.legal || 'Legal'}</h3>
            <ul className="space-y-3">
              <li>
                <Link href={`/${locale}/privacy`} className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                  {dictionary.footer.privacy}
                </Link>
              </li>
              <li>
                <Link href={`/${locale}/terms`} className="text-sm text-muted-foreground hover:text-foreground transition-colors">
                  {dictionary.footer.terms}
                </Link>
              </li>
            </ul>
          </div>
        </div>

        <div className="pt-8 border-t">
          <p className="text-sm text-muted-foreground text-center">
            {dictionary.footer.copyright}
          </p>
        </div>
      </div>
    </footer>
  );
}