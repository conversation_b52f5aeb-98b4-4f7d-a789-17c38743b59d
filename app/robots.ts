import { MetadataRoute } from 'next';

// 为静态导出配置
export const dynamic = 'force-static';
export const revalidate = 86400; // 每天重新验证一次

export default function robots(): MetadataRoute.Robots {
  const baseUrl = 'https://x114.org';

  return {
    rules: [
      {
        userAgent: '*',
        allow: '/',
        disallow: [
          '/private/',
          '/admin/',
          '/api/',
          '/_next/',
          '/static/',
          '/*.json$',
          '/*.txt$',
          '/search?*',
          '/login',
          '/signup',
        ],
        crawlDelay: 1,
      },
      {
        userAgent: 'Googlebot',
        allow: '/',
        disallow: [
          '/private/',
          '/admin/',
          '/api/',
          '/login',
          '/signup',
        ],
      },
      {
        userAgent: 'Bingbot',
        allow: '/',
        disallow: [
          '/private/',
          '/admin/',
          '/api/',
          '/login',
          '/signup',
        ],
        crawlDelay: 2,
      },
    ],
    sitemap: [
      `${baseUrl}/sitemap.xml`,
    ],
    host: baseUrl,
  };
}
