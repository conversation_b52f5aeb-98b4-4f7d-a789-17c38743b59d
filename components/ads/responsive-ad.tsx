'use client';

import React, { useEffect, useState, useRef } from 'react';
import { cn } from '@/lib/utils';
import { ResponsiveAdProps, AdSize } from './core/ad-types';
import { generateAdStyle, getResponsiveAdSize, generateAdPlaceholder } from './core/ad-utils';
import GoogleAd from './google-ad';

/**
 * 响应式广告组件
 * 根据屏幕尺寸自动调整广告大小和格式
 */
export default function ResponsiveAd({
  adSlot,
  size,
  mobileSize,
  tabletSize,
  desktopSize,
  adFormat = 'auto',
  fullWidthResponsive = true,
  style,
  className = '',
  position,
  lazy = false,
  testMode = false,
  ...props
}: ResponsiveAdProps) {
  const [currentSize, setCurrentSize] = useState<AdSize>(size);
  const [isVisible, setIsVisible] = useState(!lazy);
  const adRef = useRef<HTMLDivElement>(null);

  // 响应式尺寸调整
  useEffect(() => {
    const updateSize = () => {
      const width = window.innerWidth;
      let newSize = size;

      if (width < 768 && mobileSize) {
        newSize = mobileSize;
      } else if (width < 1024 && tabletSize) {
        newSize = tabletSize;
      } else if (width >= 1024 && desktopSize) {
        newSize = desktopSize;
      } else {
        newSize = getResponsiveAdSize(size, width);
      }

      setCurrentSize(newSize);
    };

    updateSize();
    window.addEventListener('resize', updateSize);
    return () => window.removeEventListener('resize', updateSize);
  }, [size, mobileSize, tabletSize, desktopSize]);

  // 延迟加载
  useEffect(() => {
    if (!lazy || isVisible) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsVisible(true);
            observer.unobserve(entry.target);
          }
        });
      },
      { threshold: 0.1 }
    );

    if (adRef.current) {
      observer.observe(adRef.current);
    }

    return () => observer.disconnect();
  }, [lazy, isVisible]);

  const adStyle = generateAdStyle(currentSize, style);

  // 测试模式或开发环境显示占位符
  if (testMode || process.env.NODE_ENV !== 'production') {
    const placeholder = generateAdPlaceholder(currentSize, adSlot);
    
    return (
      <div
        ref={adRef}
        className={cn(
          'ad-placeholder',
          'flex flex-col items-center justify-center',
          className
        )}
        style={adStyle}
        data-ad-size={currentSize}
        data-ad-position={position}
      >
        <div className="font-medium mb-1">Responsive Ad Placeholder</div>
        <div className="text-xs opacity-75 whitespace-pre-line">
          {placeholder.text}
        </div>
        <div className="text-xs mt-1 opacity-50">
          Current size: {currentSize}
        </div>
      </div>
    );
  }

  // 延迟加载时显示占位符
  if (!isVisible) {
    return (
      <div
        ref={adRef}
        className={cn('ad-loading', className)}
        style={adStyle}
        data-ad-size={currentSize}
        data-ad-position={position}
      />
    );
  }

  return (
    <div
      ref={adRef}
      className={cn('responsive-ad', className)}
      data-ad-size={currentSize}
      data-ad-position={position}
    >
      <GoogleAd
        adSlot={adSlot}
        adFormat={adFormat}
        fullWidthResponsive={fullWidthResponsive}
        style={adStyle}
        className="w-full"
        {...props}
      />
    </div>
  );
}

/**
 * 预定义的响应式广告组件
 */

/**
 * 横幅广告 - 在桌面端显示728x90，移动端显示320x50
 */
export function ResponsiveBannerAd({
  adSlot,
  className = '',
  ...props
}: Omit<ResponsiveAdProps, 'size' | 'mobileSize'>) {
  return (
    <ResponsiveAd
      adSlot={adSlot}
      size="banner"
      mobileSize="mobile-banner"
      className={cn('w-full max-w-4xl mx-auto', className)}
      {...props}
    />
  );
}

/**
 * 方形广告 - 在所有设备上保持300x250
 */
export function ResponsiveRectangleAd({
  adSlot,
  className = '',
  ...props
}: Omit<ResponsiveAdProps, 'size'>) {
  return (
    <ResponsiveAd
      adSlot={adSlot}
      size="rectangle"
      className={cn('max-w-sm mx-auto', className)}
      {...props}
    />
  );
}

/**
 * 侧边栏广告 - 在桌面端显示摩天楼，移动端和平板端显示方形
 */
export function ResponsiveSidebarAd({
  adSlot,
  className = '',
  ...props
}: Omit<ResponsiveAdProps, 'size' | 'mobileSize' | 'tabletSize'>) {
  return (
    <ResponsiveAd
      adSlot={adSlot}
      size="skyscraper"
      mobileSize="rectangle"
      tabletSize="rectangle"
      className={className}
      {...props}
    />
  );
}
