import Script from 'next/script';
import './globals.css';
import PerformanceMonitor from '@/components/monitoring/performance-monitor';
import ClientScripts from '@/components/client-scripts';

export const metadata = {
  title: 'X114 - AI Tools Directory',
  description: 'Discover and compare the best AI tools for productivity, creativity, and business',
  manifest: '/manifest.json',
  icons: {
    icon: [
      { url: '/favicon-64x64.png', sizes: '16x16', type: 'image/png' },
      { url: '/favicon-64x64.png', sizes: '32x32', type: 'image/png' },
      { url: '/favicon.ico' },
    ],
    apple: [
      { url: '/x114-logo-192x192.png', sizes: '180x180', type: 'image/png' },
      { url: '/x114-logo-192x192.png', sizes: '192x192', type: 'image/png' },
    ],
    other: [
      {
        rel: 'mask-icon',
        url: '/x114-logo-192x192.png',
        color: '#3b82f6',
      },
    ],
  },
  other: {
    'format-detection': 'telephone=no',
    'mobile-web-app-capable': 'yes',
    'apple-mobile-web-app-capable': 'yes',
    'apple-mobile-web-app-status-bar-style': 'default',
    'apple-mobile-web-app-title': 'X114',
    'application-name': 'X114',
    'msapplication-TileColor': '#3b82f6',
    'msapplication-config': '/browserconfig.xml',
    'theme-color': '#3b82f6',
    'color-scheme': 'light dark',
  },
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
  themeColor: '#3b82f6',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className="font-sans">
        {/* Google Analytics - 只在生产环境加载 */}
        {process.env.NODE_ENV === 'production' && (
            <>
              <Script
                  src="https://www.googletagmanager.com/gtag/js?id=G-PR6E12G0V0"
                  strategy="afterInteractive"
              />
              <Script id="google-analytics" strategy="afterInteractive">
                {`
                window.dataLayer = window.dataLayer || [];
                function gtag(){dataLayer.push(arguments);}
                gtag('js', new Date());
                gtag('config', 'G-PR6E12G0V0');
              `}
              </Script>
            </>
        )}

        {/* Google AdSense - 加载在所有环境以便测试 */}
        <Script 
          src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-7932958206248396"
          strategy="beforeInteractive"
          crossOrigin="anonymous"
        />

        {children}
        <ClientScripts/>
        {process.env.NODE_ENV === 'development' && (
            <PerformanceMonitor showDebugPanel={false} />
        )}
      </body>
    </html>
  );
}
