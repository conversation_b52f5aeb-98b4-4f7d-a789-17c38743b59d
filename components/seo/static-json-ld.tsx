import Script from 'next/script';

interface StaticJsonLdProps {
  data: any;
  id?: string;
}

/**
 * 静态JSON-LD组件
 * 使用Next.js的Script组件来确保在静态导出时正确包含结构化数据
 */
export default function StaticJsonLd({ data, id }: StaticJsonLdProps) {
  if (!data) return null;

  return (
    <Script
      id={id}
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(data),
      }}
      strategy="beforeInteractive"
    />
  );
}
