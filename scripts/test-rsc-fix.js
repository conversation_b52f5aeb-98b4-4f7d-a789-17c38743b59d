#!/usr/bin/env node

/**
 * 测试 RSC 修复
 * 专门验证根级别 RSC 请求是否正常工作
 */

const https = require('https');

async function testRscRequest(url) {
  return new Promise((resolve, reject) => {
    const request = https.get(url, {
      headers: {
        'Accept': 'text/x-component',
        'User-Agent': 'Mozilla/5.0 (compatible; RSC-Test/1.0)'
      }
    }, (response) => {
      let data = '';
      
      response.on('data', (chunk) => {
        data += chunk;
      });
      
      response.on('end', () => {
        resolve({
          status: response.statusCode,
          headers: response.headers,
          contentType: response.headers['content-type'],
          dataLength: data.length,
          isRscData: data.includes('$Sreact') || data.includes('I['),
          preview: data.substring(0, 100)
        });
      });
    });
    
    request.on('error', reject);
    request.setTimeout(10000, () => {
      request.destroy();
      reject(new Error(`Timeout for ${url}`));
    });
  });
}

async function testRscFix(baseUrl = 'https://x114.org') {
  console.log(`Testing RSC fix at: ${baseUrl}`);
  console.log('='.repeat(60));
  
  const testUrls = [
    `${baseUrl}/en.txt`,
    `${baseUrl}/zh.txt`,
    `${baseUrl}/en/category/coding-development.txt`,
    `${baseUrl}/en/tool/force-com.txt`,
    `${baseUrl}/en/tools.txt`
  ];
  
  for (const url of testUrls) {
    try {
      console.log(`\nTesting: ${url}`);
      const result = await testRscRequest(url);
      
      console.log(`Status: ${result.status}`);
      console.log(`Content-Type: ${result.contentType}`);
      console.log(`Data Length: ${result.dataLength} bytes`);
      console.log(`Is RSC Data: ${result.isRscData ? '✅' : '❌'}`);
      console.log(`Preview: ${result.preview}...`);
      
      if (result.status === 200 && result.isRscData) {
        console.log('✅ SUCCESS: RSC request working correctly');
      } else {
        console.log('❌ FAILED: RSC request not working');
      }
      
    } catch (error) {
      console.log(`❌ ERROR: ${error.message}`);
    }
  }
  
  console.log('\n' + '='.repeat(60));
  console.log('RSC fix test completed!');
}

// 从命令行参数获取 URL，或使用默认值
const baseUrl = process.argv[2] || 'https://x114.org';
testRscFix(baseUrl).catch(console.error);