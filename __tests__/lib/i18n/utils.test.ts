import {
  getLocaleFromPathname,
  localizeUrl,
  removeLocaleFromPath,
  mapLocaleToIntl,
  formatNumber,
  formatDate,
  formatCurrency
} from '@/lib/i18n/utils';

describe('i18n Utils', () => {
  describe('getLocaleFromPathname', () => {
    it('should extract locale from pathname', () => {
      expect(getLocaleFromPathname('/en/tools')).toBe('en');
      expect(getLocaleFromPathname('/zh/tools')).toBe('zh');
      expect(getLocaleFromPathname('/es/tools')).toBe('es');
    });

    it('should return default locale for invalid locale', () => {
      expect(getLocaleFromPathname('/fr/tools')).toBe('en');
    });

    it('should return default locale for missing locale', () => {
      expect(getLocaleFromPathname('/tools')).toBe('en');
    });
  });

  describe('localizeUrl', () => {
    it('should localize URL', () => {
      expect(localizeUrl('/tools', 'en')).toBe('/en/tools');
      expect(localizeUrl('/tools', 'zh')).toBe('/zh/tools');
      expect(localizeUrl('/tools', 'es')).toBe('/es/tools');
    });

    it('should replace existing locale', () => {
      expect(localizeUrl('/en/tools', 'zh')).toBe('/zh/tools');
      expect(localizeUrl('/zh/tools', 'es')).toBe('/es/tools');
      expect(localizeUrl('/es/tools', 'en')).toBe('/en/tools');
    });

    it('should handle paths with query parameters', () => {
      expect(localizeUrl('/tools?page=2', 'en')).toBe('/en/tools?page=2');
    });
  });

  describe('removeLocaleFromPath', () => {
    it('should remove locale from path', () => {
      expect(removeLocaleFromPath('/en/tools')).toBe('/tools');
      expect(removeLocaleFromPath('/zh/tools')).toBe('/tools');
      expect(removeLocaleFromPath('/es/tools')).toBe('/tools');
    });

    it('should return original path if no locale is present', () => {
      expect(removeLocaleFromPath('/tools')).toBe('/tools');
    });

    it('should handle paths without leading slash', () => {
      expect(removeLocaleFromPath('en/tools')).toBe('/tools');
    });

    it('should handle paths with query parameters', () => {
      expect(removeLocaleFromPath('/en/tools?page=2')).toBe('/tools?page=2');
    });
  });

  describe('mapLocaleToIntl', () => {
    it('should map locale to Intl format', () => {
      expect(mapLocaleToIntl('en')).toBe('en-US');
      expect(mapLocaleToIntl('zh')).toBe('zh-CN');
      expect(mapLocaleToIntl('es')).toBe('es-ES');
    });
  });

  describe('formatNumber', () => {
    it('should format number according to locale', () => {
      // Test with a large number to ensure formatting differences
      const number = 1234567.89;
      
      // English format: 1,234,567.89
      expect(formatNumber('en', number)).toMatch(/1,234,567.89/);
      
      // Chinese format might use different separators
      const zhFormatted = formatNumber('zh', number);
      expect(zhFormatted).toBeDefined();
      
      // Spanish format uses . as thousands separator and , as decimal
      const esFormatted = formatNumber('es', number);
      expect(esFormatted).toBeDefined();
    });

    it('should apply formatting options', () => {
      const number = 1234.56;
      const options = { maximumFractionDigits: 0 };
      
      // Should round to 1,235
      expect(formatNumber('en', number, options)).toBe('1,235');
    });
  });

  describe('formatDate', () => {
    it('should format date according to locale', () => {
      const date = new Date(2023, 0, 15); // January 15, 2023
      
      const enFormatted = formatDate('en', date);
      const zhFormatted = formatDate('zh', date);
      const esFormatted = formatDate('es', date);
      
      // Each locale should produce a different format
      expect(enFormatted).toBeDefined();
      expect(zhFormatted).toBeDefined();
      expect(esFormatted).toBeDefined();
      
      // Formats should be different
      const formats = new Set([enFormatted, zhFormatted, esFormatted]);
      expect(formats.size).toBeGreaterThan(1);
    });

    it('should apply formatting options', () => {
      const date = new Date(2023, 0, 15);
      const options = { year: 'numeric', month: 'long', day: 'numeric' } as const;
      
      const formatted = formatDate('en', date, options);
      expect(formatted).toContain('2023');
      expect(formatted).toContain('January');
      expect(formatted).toContain('15');
    });
  });

  describe('formatCurrency', () => {
    it('should format currency according to locale', () => {
      const amount = 1234.56;
      
      // USD in English
      expect(formatCurrency('en', amount)).toContain('$');
      expect(formatCurrency('en', amount)).toContain('1,234.56');
      
      // USD in other locales
      expect(formatCurrency('zh', amount)).toBeDefined();
      expect(formatCurrency('es', amount)).toBeDefined();
    });

    it('should use specified currency', () => {
      const amount = 1234.56;
      
      // EUR in English
      expect(formatCurrency('en', amount, 'EUR')).toContain('€');
      
      // JPY in English (no decimal places)
      const jpyFormatted = formatCurrency('en', amount, 'JPY');
      expect(jpyFormatted).toContain('¥');
      expect(jpyFormatted).not.toContain('.');
    });
  });
});
