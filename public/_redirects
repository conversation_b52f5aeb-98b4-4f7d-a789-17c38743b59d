# Cloudflare Pages 重定向配置
# 规则按优先级排序，从高到低

# ==========================================
# 优先级 1: 根路径处理
# ==========================================
/ /en/ 302

# ==========================================
# 优先级 2: 语言路径标准化（添加尾部斜杠）
# ==========================================
/en /en/ 301
/zh /zh/ 301
/tw /tw/ 301
/ko /ko/ 301
/ja /ja/ 301
/pt /pt/ 301
/es /es/ 301
/de /de/ 301
/fr /fr/ 301
/vi /vi/ 301

# ==========================================
# 优先级 3: RSC 数据流精确映射
# ==========================================

# 根级别语言 RSC 数据流
/en.txt /en/index.txt 200
/zh.txt /zh/index.txt 200
/tw.txt /tw/index.txt 200
/ko.txt /ko/index.txt 200
/ja.txt /ja/index.txt 200
/pt.txt /pt/index.txt 200
/es.txt /es/index.txt 200
/de.txt /de/index.txt 200
/fr.txt /fr/index.txt 200
/vi.txt /vi/index.txt 200

# 动态页面 RSC 数据流
/:locale/category/:slug.txt /:locale/category/:slug/index.txt 200
/:locale/tool/:slug.txt /:locale/tool/:slug/index.txt 200
/:locale/article/:slug.txt /:locale/article/:slug/index.txt 200

# 静态页面 RSC 数据流
/:locale/tools.txt /:locale/tools/index.txt 200
/:locale/categories.txt /:locale/categories/index.txt 200
/:locale/articles.txt /:locale/articles/index.txt 200
/:locale/submit.txt /:locale/submit/index.txt 200
/:locale/privacy.txt /:locale/privacy/index.txt 200
/:locale/terms.txt /:locale/terms/index.txt 200

# ==========================================
# 优先级 4: SPA 回退处理（确保页面可访问）
# ==========================================

# 动态页面 HTML 回退
/:locale/category/:slug /:locale/category/:slug/index.html 200
/:locale/tool/:slug /:locale/tool/:slug/index.html 200
/:locale/article/:slug /:locale/article/:slug/index.html 200

# 静态页面 HTML 回退
/:locale/tools /:locale/tools/index.html 200
/:locale/categories /:locale/categories/index.html 200
/:locale/articles /:locale/articles/index.html 200
/:locale/submit /:locale/submit/index.html 200
/:locale/privacy /:locale/privacy/index.html 200
/:locale/terms /:locale/terms/index.html 200

# ==========================================
# 优先级 5: 通用 SPA 回退（兜底处理）
# ==========================================
/:locale/* /:locale/index.html 200