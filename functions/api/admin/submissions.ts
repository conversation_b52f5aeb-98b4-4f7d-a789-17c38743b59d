/**
 * 管理员API - 查看和管理工具提交
 */

interface Env {
  DB: D1Database;
  ADMIN_API_KEY?: string;
}

// Pages Functions 上下文类型
interface PagesContext {
  request: Request;
  env: Env;
  params: any;
  waitUntil: (promise: Promise<any>) => void;
  next: () => Promise<Response>;
  data: any;
}

interface SubmissionFilter {
  status?: 'pending' | 'approved' | 'rejected';
  locale?: string;
  category?: string;
  limit?: number;
  offset?: number;
}

// 验证管理员权限
function validateAdminAccess(request: Request, env: Env): boolean {
  const authHeader = request.headers.get('Authorization');
  if (!authHeader || !env.ADMIN_API_KEY) {
    return false;
  }

  const token = authHeader.replace('Bearer ', '');
  return token === env.ADMIN_API_KEY;
}

// 获取提交列表
async function getSubmissions(env: Env, filters: SubmissionFilter = {}) {
  const {
    status,
    locale,
    category,
    limit = 50,
    offset = 0
  } = filters;

  let query = `
    SELECT
      id, name, description, website, category, email,
      additional_info, locale, submitted_at, status,
      created_at, updated_at, reviewed_by, review_notes,
      approved_at, rejected_at
    FROM tool_submissions
  `;

  const conditions = [];
  const params = [];

  if (status) {
    conditions.push('status = ?');
    params.push(status);
  }

  if (locale) {
    conditions.push('locale = ?');
    params.push(locale);
  }

  if (category) {
    conditions.push('category = ?');
    params.push(category);
  }

  if (conditions.length > 0) {
    query += ' WHERE ' + conditions.join(' AND ');
  }

  query += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
  params.push(limit, offset);

  const stmt = env.DB.prepare(query);
  const result = await stmt.bind(...params).all();

  return result.results;
}

// 更新提交状态
async function updateSubmissionStatus(
  env: Env,
  id: string,
  status: 'approved' | 'rejected',
  reviewedBy: string,
  reviewNotes?: string
) {
  const now = new Date().toISOString();
  const statusField = status === 'approved' ? 'approved_at' : 'rejected_at';

  const stmt = env.DB.prepare(`
    UPDATE tool_submissions
    SET
      status = ?,
      reviewed_by = ?,
      review_notes = ?,
      ${statusField} = ?,
      updated_at = ?
    WHERE id = ?
  `);

  const result = await stmt.bind(
    status,
    reviewedBy,
    reviewNotes || '',
    now,
    now,
    id
  ).run();

  return result.success;
}

// 获取统计信息
async function getSubmissionStats(env: Env) {
  const stmt = env.DB.prepare(`
    SELECT
      status,
      COUNT(*) as count
    FROM tool_submissions
    GROUP BY status
  `);

  const result = await stmt.all();

  const stats = {
    total: 0,
    pending: 0,
    approved: 0,
    rejected: 0
  };

  result.results.forEach((row: any) => {
    stats[row.status as keyof typeof stats] = row.count;
    stats.total += row.count;
  });

  return stats;
}

export async function onRequestGet(context: PagesContext): Promise<Response> {
  const { request, env } = context;

  // 验证管理员权限
  if (!validateAdminAccess(request, env)) {
    return new Response(
      JSON.stringify({ error: 'Unauthorized' }),
      {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }

  try {
    const url = new URL(request.url);
    const action = url.searchParams.get('action');

    if (action === 'stats') {
      // 获取统计信息
      const stats = await getSubmissionStats(env);
      return new Response(
        JSON.stringify({ success: true, data: stats }),
        {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // 获取提交列表
    const filters: SubmissionFilter = {
      status: url.searchParams.get('status') as any,
      locale: url.searchParams.get('locale') || undefined,
      category: url.searchParams.get('category') || undefined,
      limit: parseInt(url.searchParams.get('limit') || '50'),
      offset: parseInt(url.searchParams.get('offset') || '0'),
    };

    const submissions = await getSubmissions(env, filters);

    return new Response(
      JSON.stringify({
        success: true,
        data: submissions,
        pagination: {
          limit: filters.limit,
          offset: filters.offset,
          hasMore: submissions.length === filters.limit
        }
      }),
      {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      }
    );

  } catch (error) {
    console.error('Admin API error:', error);
    return new Response(
      JSON.stringify({ success: false, error: 'Internal server error' }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

export async function onRequestPut(context: PagesContext): Promise<Response> {
  const { request, env } = context;

  // 验证管理员权限
  if (!validateAdminAccess(request, env)) {
    return new Response(
      JSON.stringify({ error: 'Unauthorized' }),
      {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }

  try {
    const data = await request.json() as {
      id: string;
      status: 'approved' | 'rejected';
      reviewedBy: string;
      reviewNotes?: string;
    };
    const { id, status, reviewedBy, reviewNotes } = data;

    if (!id || !status || !reviewedBy) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields' }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    if (!['approved', 'rejected'].includes(status)) {
      return new Response(
        JSON.stringify({ error: 'Invalid status' }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    const success = await updateSubmissionStatus(env, id, status, reviewedBy, reviewNotes);

    if (success) {
      return new Response(
        JSON.stringify({ success: true, message: 'Submission updated successfully' }),
        {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    } else {
      return new Response(
        JSON.stringify({ error: 'Failed to update submission' }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

  } catch (error) {
    console.error('Admin API error:', error);
    return new Response(
      JSON.stringify({ success: false, error: 'Internal server error' }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}
