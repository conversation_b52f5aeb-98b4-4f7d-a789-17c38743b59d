import { ObjectId } from 'mongodb';
import { Tool } from '../../types';
import { connectToDatabase, COLLECTIONS } from '../mongodb';
import {
  convertToolDocument,
  convertDocumentArray,
  findDocumentById,
  findDocumentBySlug
} from './utils';

// 获取所有工具
export async function getAllToolsFromMongoDB(language?: string): Promise<Tool[]> {
  try {
    const db = await connectToDatabase();
    const collection = db.collection(COLLECTIONS.TOOLS);

    // 构建查询条件，如果指定了语言，则按语言筛选
    const query = language ? { language } : {};

    // 创建索引以提高查询性能
    await collection.createIndex({ language: 1 });
    await collection.createIndex({ id: 1 });
    await collection.createIndex({ tags: 1 });

    const tools = await collection.find(query).toArray();

    // 使用通用转换函数处理文档
    return convertDocumentArray(tools, convertToolDocument);
  } catch (error) {
    console.error('Error fetching tools from MongoDB:', error);
    throw error;
  }
}

// 根据过滤条件获取工具
export async function getToolsFromMongoDB(filters?: {
  category?: string;
  featured?: boolean;
  trending?: boolean;
  isNew?: boolean;
  minRating?: number;
  tags?: string[];
  sort?: 'newest' | 'popular' | 'rating';
  limit?: number;
  offset?: number;
  language?: string; // 新增语言筛选
}): Promise<Tool[]> {
  try {
    const db = await connectToDatabase();
    const collection = db.collection(COLLECTIONS.TOOLS);

    // 构建查询条件
    const query: any = {};

    if (filters) {
      if (filters.category) {
        query['groups.slug'] = filters.category;
      }

      if (filters.featured) {
        query.featured = true;
      }

      if (filters.trending) {
        query.trending = true;
      }

      if (filters.isNew) {
        query.isNew = true;
      }

      if (filters.minRating !== undefined) {
        query.rating = { $gte: filters.minRating };
      }

      if (filters.tags && filters.tags.length > 0) {
        query.tags = { $in: filters.tags };
      }

      // 按语言筛选
      if (filters.language) {
        query.language = filters.language;
      }
    }

    // 构建排序条件
    let sortOptions = {};

    if (filters?.sort) {
      switch (filters.sort) {
        case 'newest':
          sortOptions = { createdAt: -1 };
          break;
        case 'popular':
          sortOptions = { reviewCount: -1 };
          break;
        case 'rating':
          sortOptions = { rating: -1 };
          break;
      }
    }

    // 执行查询
    let cursor = collection.find(query).sort(sortOptions);

    // 应用分页
    if (filters?.offset !== undefined) {
      cursor = cursor.skip(filters.offset);
    }

    if (filters?.limit !== undefined) {
      cursor = cursor.limit(filters.limit);
    }

    const tools = await cursor.toArray();

    // 使用通用转换函数处理文档
    return convertDocumentArray(tools, convertToolDocument);
  } catch (error) {
    console.error('Error fetching filtered tools from MongoDB:', error);
    throw error;
  }
}

// 根据 ID 获取工具
export async function getToolByIdFromMongoDB(id: string, language?: string): Promise<Tool | null> {
  try {
    const db = await connectToDatabase();
    const collection = db.collection(COLLECTIONS.TOOLS);

    // 使用通用函数查找文档
    return await findDocumentById(collection, id, convertToolDocument, language);
  } catch (error) {
    console.error(`Error fetching tool with id ${id} from MongoDB:`, error);
    throw error;
  }
}

// 根据 Slug 获取工具
export async function getToolBySlugFromMongoDB(slug: string, language?: string): Promise<Tool | null> {
  try {
    const db = await connectToDatabase();
    const collection = db.collection(COLLECTIONS.TOOLS);

    // 使用通用函数查找文档
    return await findDocumentBySlug(collection, slug, convertToolDocument, language);
  } catch (error) {
    console.error(`Error fetching tool with slug ${slug} from MongoDB:`, error);
    throw error;
  }
}

// 获取相关工具
export async function getRelatedToolsFromMongoDB(tool: Tool, limit: number = 4, language?: string): Promise<Tool[]> {
  try {
    const db = await connectToDatabase();
    const collection = db.collection(COLLECTIONS.TOOLS);

    // 查找同类别或有相似标签的工具
    const query: any = {
      id: { $ne: tool.id },
      $or: [
        { groups: { $in: tool.groups } },
        { tags: { $in: tool.tags } }
      ]
    };

    // 如果指定了语言，则按语言筛选
    if (language) {
      query.language = language;
    } else if (tool.language) {
      // 如果没有指定语言，但工具有语言属性，则使用相同语言
      query.language = tool.language;
    }

    const tools = await collection
      .find(query)
      .sort({ rating: -1 })
      .limit(limit)
      .toArray();

    // 使用通用转换函数处理文档
    return convertDocumentArray(tools, convertToolDocument);
  } catch (error) {
    console.error(`Error fetching related tools for tool ${tool.id} from MongoDB:`, error);
    throw error;
  }
}

// 创建新工具
export async function createToolInMongoDB(tool: Omit<Tool, '_id'>): Promise<Tool> {
  try {
    const db = await connectToDatabase();
    const collection = db.collection(COLLECTIONS.TOOLS);

    const result = await collection.insertOne(tool);

    // 使用通用转换函数处理文档
    return convertToolDocument({
      ...tool,
      _id: result.insertedId
    });
  } catch (error) {
    console.error('Error creating tool in MongoDB:', error);
    throw error;
  }
}

// 更新工具
export async function updateToolInMongoDB(id: string, tool: Partial<Tool>): Promise<Tool | null> {
  try {
    const db = await connectToDatabase();
    const collection = db.collection(COLLECTIONS.TOOLS);

    // 尝试使用 id 字段更新
    let query: any = { id };

    // 如果没有找到，尝试使用 _id 更新（如果 id 是有效的 ObjectId）
    if ((await collection.countDocuments(query)) === 0 && ObjectId.isValid(id)) {
      query = { _id: new ObjectId(id) };
    }

    const result = await collection.findOneAndUpdate(
      query,
      { $set: tool },
      { returnDocument: 'after' }
    );

    // 使用通用转换函数处理文档
    return convertToolDocument(result);
  } catch (error) {
    console.error(`Error updating tool with id ${id} in MongoDB:`, error);
    throw error;
  }
}

// 删除工具
export async function deleteToolFromMongoDB(id: string): Promise<boolean> {
  try {
    const db = await connectToDatabase();
    const collection = db.collection(COLLECTIONS.TOOLS);

    // 尝试使用 id 字段删除
    let result = await collection.deleteOne({ id });

    // 如果没有删除任何文档，尝试使用 _id 删除（如果 id 是有效的 ObjectId）
    if (result.deletedCount === 0 && ObjectId.isValid(id)) {
      result = await collection.deleteOne({ _id: new ObjectId(id) });
    }

    return result.deletedCount > 0;
  } catch (error) {
    console.error(`Error deleting tool with id ${id} from MongoDB:`, error);
    throw error;
  }
}
