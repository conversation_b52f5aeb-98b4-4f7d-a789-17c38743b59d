/**
 * 检查MongoDB中的数据
 */

require('./base');
const { MongoClient } = require('mongodb');

// MongoDB 连接信息
const MONGO_DB = process.env.MONGODB_DB;
const MONGO_URI = process.env.MONGODB_URI;

// 集合名称
const COLLECTIONS = {
  TOOLS: 'tools',
  CATEGORIES: 'categories',
};

async function main() {
  let client;
  try {
    // 连接到MongoDB
    client = new MongoClient(MONGO_URI);
    await client.connect();
    console.log('Connected to MongoDB\n');

    const db = client.db(MONGO_DB);

    // 获取分类数量
    const categoryCount = await db.collection(COLLECTIONS.CATEGORIES).countDocuments();
    console.log(`Categories count: ${categoryCount}`);

    // 获取工具数量
    const toolCount = await db.collection(COLLECTIONS.TOOLS).countDocuments();
    console.log(`Tools count: ${toolCount}`);

    // 获取一些分类示例
    const categories = await db.collection(COLLECTIONS.CATEGORIES).find().limit(3).toArray();
    console.log('\nSample categories:');
    categories.forEach(category => {
      console.log(`- ${category.name} (${category.slug}): ${category.toolCount} tools`);
    });

    // 获取一些工具示例
    const tools = await db.collection(COLLECTIONS.TOOLS).find().limit(3).toArray();
    console.log('\nSample tools:');
    tools.forEach(tool => {
      console.log(`- ${tool.name}: ${tool.description.substring(0, 50)}...`);
      console.log(`  Website: ${tool.website}`);
      console.log(`  Category: ${tool.category}`);
      console.log(`  Tags: ${tool.tags.join(', ')}`);
      console.log('');
    });

  } catch (error) {
    console.error('Error:', error);
  } finally {
    // 关闭MongoDB连接
    if (client) {
      await client.close();
      console.log('MongoDB connection closed');
    }
  }
}

// 执行主函数
main();
