'use client';

import { ThemeProvider as NextThemesProvider } from 'next-themes';

// 定义 ThemeProviderProps 类型
interface ThemeProviderProps {
  children: React.ReactNode;
  [key: string]: any;
}

export function ThemeProvider({ children, ...props }: ThemeProviderProps) {
  return (
    <NextThemesProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
      storageKey="theme" // 确保使用一致的存储键名
      {...props}
    >
      {children}
    </NextThemesProvider>
  );
}