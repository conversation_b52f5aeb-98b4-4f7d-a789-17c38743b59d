'use client';

import { useEffect } from 'react';
import { Tool, Category, Article } from '@/lib/types';

// 网站信息
const siteInfo = {
  name: 'X114 AI Tool Hub',
  url: 'https://x114.org',
  logo: 'https://x114.org/x114-logo-192x192.png',
  description: 'Discover and compare the best AI tools to enhance your workflow and boost productivity.',
  sameAs: [
    'https://twitter.com/x114_ai',
    'https://github.com/x114',
    'https://linkedin.com/company/x114'
  ]
};

// 组织结构化数据
export function OrganizationStructuredData() {
  useEffect(() => {
    const script = document.createElement('script');
    script.type = 'application/ld+json';
    script.textContent = JSON.stringify({
      '@context': 'https://schema.org',
      '@type': 'Organization',
      name: siteInfo.name,
      url: siteInfo.url,
      logo: siteInfo.logo,
      description: siteInfo.description,
      sameAs: siteInfo.sameAs
    });
    document.head.appendChild(script);

    return () => {
      document.head.removeChild(script);
    };
  }, []);

  return null;
}

// 面包屑结构化数据
interface BreadcrumbItem {
  name: string;
  url: string;
}

export function BreadcrumbStructuredData({ items }: { items: BreadcrumbItem[] }) {
  useEffect(() => {
    const script = document.createElement('script');
    script.type = 'application/ld+json';
    script.textContent = JSON.stringify({
      '@context': 'https://schema.org',
      '@type': 'BreadcrumbList',
      itemListElement: items.map((item, index) => ({
        '@type': 'ListItem',
        position: index + 1,
        name: item.name,
        item: item.url
      }))
    });
    document.head.appendChild(script);

    return () => {
      document.head.removeChild(script);
    };
  }, [items]);

  return null;
}

// 工具结构化数据
export function ToolStructuredData({ tool, locale }: { tool: Tool; locale: string }) {
  useEffect(() => {
    const script = document.createElement('script');
    script.type = 'application/ld+json';

    // 构建结构化数据
    const structuredData = {
      '@context': 'https://schema.org',
      '@type': 'SoftwareApplication',
      name: tool.name,
      description: tool.description,
      applicationCategory: 'AIApplication',
      operatingSystem: 'Web',
      url: `${siteInfo.url}/${locale}/tool/${tool.id}`,
      image: tool.image || siteInfo.logo,
      offers: {
        '@type': 'Offer',
        price: undefined,
        priceCurrency: 'USD',
        availability: 'https://schema.org/OnlineOnly'
      },
      aggregateRating: tool.rating ? {
        '@type': 'AggregateRating',
        ratingValue: tool.rating,
        ratingCount: tool.reviewCount || 1,
        bestRating: '5',
        worstRating: '1'
      } : undefined,
      keywords: (tool.tags || []).join(', '),
      datePublished: tool.createdAt,
      dateModified: tool.updatedAt
    };

    script.textContent = JSON.stringify(structuredData);
    document.head.appendChild(script);

    return () => {
      document.head.removeChild(script);
    };
  }, [tool, locale]);

  return null;
}

// 文章结构化数据
export function ArticleStructuredData({ article, locale }: { article: Article; locale: string }) {
  useEffect(() => {
    const script = document.createElement('script');
    script.type = 'application/ld+json';

    // 构建结构化数据
    const structuredData = {
      '@context': 'https://schema.org',
      '@type': 'Article',
      headline: article.title,
      description: article.summary,
      image: article.coverImage,
      author: {
        '@type': 'Person',
        name: article.author
      },
      publisher: {
        '@type': 'Organization',
        name: siteInfo.name,
        logo: {
          '@type': 'ImageObject',
          url: siteInfo.logo
        }
      },
      url: `${siteInfo.url}/${locale}/article/${article.slug}`,
      datePublished: article.publishedAt,
      dateModified: article.updatedAt,
      keywords: (article.tags || []).join(', ')
    };

    script.textContent = JSON.stringify(structuredData);
    document.head.appendChild(script);

    return () => {
      document.head.removeChild(script);
    };
  }, [article, locale]);

  return null;
}

// 类别结构化数据
export function CategoryStructuredData({ category, locale }: { category: Category; locale: string }) {
  useEffect(() => {
    const script = document.createElement('script');
    script.type = 'application/ld+json';

    // 构建结构化数据
    const structuredData = {
      '@context': 'https://schema.org',
      '@type': 'ItemList',
      name: category.name,
      description: category.description,
      url: `${siteInfo.url}/${locale}/categories/${category.id}`,
      numberOfItems: category.toolCount || 0
    };

    script.textContent = JSON.stringify(structuredData);
    document.head.appendChild(script);

    return () => {
      document.head.removeChild(script);
    };
  }, [category, locale]);

  return null;
}

// 搜索结构化数据
export function SearchStructuredData({ locale }: { locale: string }) {
  useEffect(() => {
    const script = document.createElement('script');
    script.type = 'application/ld+json';

    // 构建结构化数据
    const structuredData = {
      '@context': 'https://schema.org',
      '@type': 'WebSite',
      url: siteInfo.url,
      name: siteInfo.name,
      potentialAction: {
        '@type': 'SearchAction',
        target: `${siteInfo.url}/${locale}/search/results?q={search_term_string}`,
        'query-input': 'required name=search_term_string'
      }
    };

    script.textContent = JSON.stringify(structuredData);
    document.head.appendChild(script);

    return () => {
      document.head.removeChild(script);
    };
  }, [locale]);

  return null;
}
