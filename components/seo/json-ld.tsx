'use client';

import { useHeadElement } from './hooks/useHeadElement';
import { useEffect, useState } from 'react';

interface JsonLdProps {
  data: Record<string, any> | Record<string, any>[];
}

/**
 * 组件用于添加 JSON-LD 结构化数据到页面
 * 这些数据帮助搜索引擎理解页面内容
 */
export default function JsonLd({ data }: JsonLdProps) {
  const [jsonLdString, setJsonLdString] = useState<string>('');

  // 在客户端渲染时设置 JSON-LD 字符串
  useEffect(() => {
    setJsonLdString(JSON.stringify(data));
  }, [data]);

  // 使用通用的 useHeadElement Hook 添加 JSON-LD 脚本
  useHeadElement({
    type: 'script',
    attributes: {
      type: 'application/ld+json',
      innerHTML: jsonLdString
    },
    selector: 'script[type="application/ld+json"]',
    cleanup: true
  });

  // 这个组件不渲染任何内容
  return null;
}
