'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Search } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useLocale } from '@/lib/i18n/client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

export default function HeroSection({ dictionary }: { dictionary: any }) {
  const [searchQuery, setSearchQuery] = useState('');
  const router = useRouter();
  const locale = useLocale();

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      router.push(`/${locale}/tools?q=${encodeURIComponent(searchQuery)}`);
    }
  };

  return (
    <section className="relative pt-28 overflow-hidden">
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-b from-primary/5 to-background -z-10" />

      {/* Abstract shapes */}
      <div className="absolute top-0 left-0 w-full h-full -z-10">
        <div className="absolute top-1/4 left-1/4 w-72 h-72 bg-blue-500/10 rounded-full blur-3xl" />
        <div className="absolute bottom-1/3 right-1/4 w-60 h-60 bg-purple-500/10 rounded-full blur-3xl" />
      </div>

      {/* Hero content */}
      <div className="container mx-auto px-4 text-center">
        <h1 className="text-4xl md:text-5xl lg:text-6xl font-extrabold mb-6 bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent animate-fade-in">
          {dictionary.home.hero.title}
        </h1>

        <p className="text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto mb-8">
          {dictionary.home.hero.subtitle}
        </p>

        {/* Search form */}
        <form
          onSubmit={handleSearch}
          className="max-w-3xl mx-auto mb-12 relative transition-all duration-300 transform hover:scale-[1.01]"
        >
          <Input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder={dictionary.common.searchPlaceholder}
            className="pl-5 pr-28 py-6 rounded-full text-lg shadow-lg border-0 bg-card"
          />
          <Button
            type="submit"
            className="absolute right-1.5 top-1/2 transform -translate-y-1/2 rounded-full px-5 bg-gradient-to-r from-blue-600 to-blue-800 hover:from-blue-700 hover:to-blue-900 shadow-md"
          >
            <Search className="h-5 w-5 mr-2" />
            {dictionary.search?.button || 'Search'}
          </Button>
        </form>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-16 max-w-3xl mx-auto">
          <StatCard value="100+" label={dictionary.home?.hero?.stats?.categories || 'Categories'} />
          <StatCard value="1,000+" label={dictionary.home?.hero?.stats?.aiTools || 'AI Tools'} />
          <StatCard value="10,000+" label={dictionary.home?.hero?.stats?.activeUsers || 'Active Users'} />
        </div>
      </div>
    </section>
  );
}

function CategoryPill({ href, name }: { href: string; name: string }) {
  return (
    <Link
      href={href}
      className="px-4 py-2 rounded-full bg-muted/50 hover:bg-muted transition-colors text-sm font-medium"
    >
      {name}
    </Link>
  );
}

function StatCard({ value, label }: { value: string; label: string }) {
  return (
    <div className="flex flex-col items-center p-4 rounded-lg bg-card/50 border">
      <span className="text-3xl font-bold text-primary">{value}</span>
      <span className="text-sm text-muted-foreground">{label}</span>
    </div>
  );
}