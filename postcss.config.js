/**
 * PostCSS 配置文件
 *
 * 此文件配置了 PostCSS 的插件，用于优化 CSS。
 */

module.exports = {
  plugins: {
    'tailwindcss': {},
    'autoprefixer': {},
    ...(process.env.NODE_ENV === 'production' ? {
      'cssnano': {
        preset: ['default', {
          discardComments: {
            removeAll: true,
          },
          minifyFontValues: {
            removeQuotes: false,
          },
        }],
      },
    } : {}),
  },
};
