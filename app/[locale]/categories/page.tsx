import { getDictionary } from "@/lib/i18n/get-dictionary"
import { getAllCategories } from "@/lib/db/categories"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ArrowRight } from "lucide-react"
import Link from "next/link"
import { Locale, i18n } from "@/lib/i18n/config"
import { Metadata } from "next"
import { Category } from "@/lib/types"
import { generateStaticMetadata } from '@/lib/seo-static';
import ServerJsonLd from '@/components/seo/server-json-ld';
import { TopBannerAd, ContentRectangleAd } from '@/components/ads/client-ad-wrapper';

// 颜色映射函数 - 将Tailwind类名转换为实际颜色值
function getColorFromTailwindClass(colorClass: string): string {
  const colorMap: Record<string, string> = {
    'bg-blue-500/10': '#3b82f6',
    'bg-purple-500/10': '#8b5cf6',
    'bg-green-500/10': '#10b981',
    'bg-yellow-500/10': '#f59e0b',
    'bg-red-500/10': '#ef4444',
    'bg-pink-500/10': '#ec4899',
    'bg-indigo-500/10': '#6366f1',
    'bg-cyan-500/10': '#06b6d4',
    'bg-orange-500/10': '#f97316',
    'bg-teal-500/10': '#14b8a6',
    'bg-violet-500/10': '#8b5cf6',
    'bg-emerald-500/10': '#10b981',
    'bg-rose-500/10': '#f43f5e',
    'bg-sky-500/10': '#0ea5e9',
    'bg-amber-500/10': '#f59e0b',
    'bg-lime-500/10': '#84cc16',
    'bg-slate-500/10': '#64748b',
    'bg-gray-500/10': '#6b7280',
  };

  return colorMap[colorClass] || '#6b7280';
}

// 获取背景颜色（带透明度）
function getBackgroundColor(colorClass: string): string {
  const color = getColorFromTailwindClass(colorClass);
  return `${color}15`; // 添加15的透明度
}

// CategoryCard 组件
function CategoryCard({ category, locale, toolsText }: {
  category: Category;
  locale: string;
  toolsText: string;
}) {
  const iconColor = getColorFromTailwindClass(category.color);
  const bgColor = getBackgroundColor(category.color);

  return (
    <Link href={`/${locale}/category/${category.slug}`} className="group block">
      <Card className="h-full overflow-hidden border border-border/50 bg-gradient-to-br from-background via-background to-muted/10 hover:shadow-2xl hover:shadow-primary/10 transition-all duration-500 hover:-translate-y-2 hover:border-primary/20 relative">
        {/* 背景装饰 */}
        <div
          className="absolute inset-0 opacity-0 group-hover:opacity-5 transition-opacity duration-500"
          style={{
            background: `radial-gradient(circle at 20% 20%, ${iconColor}40, transparent 50%)`
          }}
        />

        <CardHeader className="pb-4 relative z-10">
          <div className="flex items-start justify-between">
            <div
              className="w-14 h-14 rounded-xl flex items-center justify-center text-2xl shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-all duration-500 relative overflow-hidden"
              style={{
                backgroundColor: bgColor,
                color: iconColor,
                boxShadow: `0 8px 25px ${iconColor}25`
              }}
            >
              {/* 图标背景光效 */}
              <div
                className="absolute inset-0 opacity-0 group-hover:opacity-20 transition-opacity duration-500"
                style={{
                  background: `radial-gradient(circle, ${iconColor}60, transparent 70%)`
                }}
              />
              <span className="relative z-10">{category.icon}</span>
            </div>
            <ArrowRight className="w-5 h-5 text-muted-foreground opacity-0 group-hover:opacity-100 group-hover:translate-x-1 transition-all duration-500" />
          </div>

          <div className="space-y-3 mt-4">
            <CardTitle className="text-lg font-semibold group-hover:text-primary transition-colors duration-300 leading-tight">
              {category.name}
            </CardTitle>
            <CardDescription className="text-sm text-muted-foreground line-clamp-2 leading-relaxed group-hover:text-muted-foreground/80 transition-colors duration-300">
              {category.description}
            </CardDescription>
          </div>
        </CardHeader>

        <CardContent className="pt-0 relative z-10">
          <div className="flex items-center justify-between">
            <Badge
              variant="secondary"
              className="text-xs font-medium transition-all duration-300 group-hover:scale-105"
              style={{
                backgroundColor: bgColor,
                color: iconColor,
                border: `1px solid ${iconColor}30`
              }}
            >
              {category.toolCount} {toolsText}
            </Badge>
            <div className="flex items-center gap-1">
              <div className="w-1.5 h-1.5 rounded-full bg-primary/30 group-hover:bg-primary transition-colors duration-300" />
              <div className="w-1 h-1 rounded-full bg-primary/20 group-hover:bg-primary/60 transition-colors duration-300 delay-75" />
            </div>
          </div>
        </CardContent>

        {/* 底部渐变线 */}
        <div
          className="absolute bottom-0 left-0 right-0 h-0.5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
          style={{
            background: `linear-gradient(90deg, transparent, ${iconColor}, transparent)`
          }}
        />
      </Card>
    </Link>
  );
}

// 为静态导出生成所有支持的语言路径
export function generateStaticParams() {
  return i18n.locales.map((locale) => ({ locale }));
}

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: Locale }>;
}): Promise<Metadata> {
  const locale = (await params).locale;
  const dictionary = await getDictionary(locale);

  return generateStaticMetadata(
    dictionary.seo?.categoriesTitle || 'AI Tool Categories',
    dictionary.seo?.categoriesDescription || 'Browse all AI tool categories. Find the best AI tools for productivity, design, writing, marketing, and more.',
    '/categories',
    locale
  );
}

export default async function CategoriesPage({
  params
}: {
  params: Promise<{ locale: Locale }>
}) {
  // 在 Next.js 15+ 中，params 是异步的，需要使用 await
  const locale = (await params).locale;
  const dict = await getDictionary(locale)
  const categories = await getAllCategories(locale)

  // 使用默认文本，不依赖于 dict.categories
  const categoriesTitle = dict?.categories?.title || "Categories";
  const categoriesDescription = dict?.categories?.description || "Browse all AI tool categories";
  const toolsText = dict?.categories?.tools || "tools";

  // 国际化文本
  const bottomText = dict?.categories?.discoverMore || 'Discover more AI tool categories';

  // 生成分类页面的结构化数据
  const categoriesJsonLd = {
    '@context': 'https://schema.org',
    '@type': 'CollectionPage',
    name: categoriesTitle,
    description: categoriesDescription,
    url: `https://x114.org/${locale}/categories`,
    mainEntity: {
      '@type': 'ItemList',
      name: 'AI Tool Categories',
      description: 'Complete list of AI tool categories',
      numberOfItems: categories.length,
      itemListElement: categories.map((category, index) => ({
        '@type': 'ListItem',
        position: index + 1,
        item: {
          '@type': 'Thing',
          '@id': `https://x114.org/${locale}/category/${category.slug}`,
          name: category.name,
          description: category.description,
          url: `https://x114.org/${locale}/category/${category.slug}`,
        }
      }))
    },
    breadcrumb: {
      '@type': 'BreadcrumbList',
      itemListElement: [
        {
          '@type': 'ListItem',
          position: 1,
          name: 'Home',
          item: `https://x114.org/${locale}`
        },
        {
          '@type': 'ListItem',
          position: 2,
          name: 'Categories',
          item: `https://x114.org/${locale}/categories`
        }
      ]
    }
  };

  return (
    <main className="container mx-auto px-4 py-8 pt-20">
      {/* 结构化数据 */}
      <ServerJsonLd data={categoriesJsonLd} id="categories-jsonld" />
      {/* 页面头部 */}
      <div className="text-center mb-12">
        <h1 className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
          {categoriesTitle}
        </h1>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto leading-relaxed">
          {categoriesDescription}
        </p>
      </div>

      {/* 顶部横幅广告 */}
      <TopBannerAd adSlot="1620640133" />

      {/* 分类网格 */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {categories.map((category) => (
          <CategoryCard
            key={category.id}
            category={category}
            locale={locale}
            toolsText={toolsText}
          />
        ))}
      </div>

      {/* 内容区域方形广告 */}
      <ContentRectangleAd adSlot="4626949563" />

      {/* 底部装饰 */}
      <div className="mt-16 text-center">
        <div className="inline-flex items-center gap-2 text-sm text-muted-foreground">
          <div className="w-1 h-1 rounded-full bg-muted-foreground/50" />
          <span>{bottomText}</span>
          <div className="w-1 h-1 rounded-full bg-muted-foreground/50" />
        </div>
      </div>
    </main>
  )
}
