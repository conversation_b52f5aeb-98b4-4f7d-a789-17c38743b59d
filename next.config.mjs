/**
 * Next.js 配置文件
 *
 * 此文件配置了 Next.js 的构建和导出行为，包括：
 * - webpack 配置
 * - 图像优化设置
 * - 静态导出选项
 * - MongoDB 相关模块的外部化
 * - Bundle Analyzer 支持
 */

import { createRequire } from 'module';

const require = createRequire(import.meta.url);

// 导入 Bundle Analyzer 插件
const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
});

/** @type {import('next').NextConfig} */
const nextConfig = {
  // 禁用 ESLint 检查，避免静态导出时的问题
  eslint: {
    ignoreDuringBuilds: true,
  },
  // 静态导出配置
  output: 'export',

  // 为静态导出优化配置
  distDir: '.next',
  assetPrefix: '',

  // 配置 webpack，解决模块解析问题
  webpack: (config, { isServer, dev }) => {
    // 启用 webpack 缓存以提高构建性能
    config.cache = {
      type: 'filesystem',
    };

    // 为静态导出简化代码分割
    if (!dev && config.optimization) {
      config.optimization.splitChunks = {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
            priority: 10,
          },
          ui: {
            test: /[\\/]components[\\/]ui[\\/]/,
            name: 'ui',
            chunks: 'all',
            priority: 20,
          },
          radix: {
            test: /[\\/]node_modules[\\/]@radix-ui[\\/]/,
            name: 'radix',
            chunks: 'all',
            priority: 30,
          },
          lucide: {
            test: /[\\/]node_modules[\\/]lucide-react[\\/]/,
            name: 'lucide',
            chunks: 'all',
            priority: 25,
          },
        },
      };
    }

    // 如果是静态导出，处理 MongoDB 相关模块
    if (process.env.STATIC_EXPORT === 'true') {
      // 在客户端构建中，将 MongoDB 相关模块替换为空模块
      if (!isServer) {
        config.resolve.fallback = {
          ...config.resolve.fallback,
          mongodb: false,
          'mongodb-client-encryption': false,
          aws4: false,
          'mongodb-connection-string-url': false,
          'bson-ext': false,
          kerberos: false,
          'snappy': false,
          'bson': false,
          'fs': false,
          'path': false,
          'util': false,
          'crypto': false,
          'stream': false,
          'zlib': false,
          'dns': false,
          'net': false,
          'tls': false,
          'os': false,
          'http': false,
          'https': false,
          'child_process': false,
        };

        // 添加别名，解决模块解析问题
        config.resolve.alias = {
          ...config.resolve.alias,
          // 将 ../../static-data 映射到一个空模块
          '../../static-data': false,
          // 将 @/lib/static-data 映射到一个空模块
          '@/lib/static-data': false,
        };
      }
    }

    // 添加解析插件，忽略某些模块
    if (config.plugins && Array.isArray(config.plugins)) {
      // 安全地添加插件
      try {
        const webpack = require('webpack');
        if (webpack && webpack.IgnorePlugin) {
          config.plugins.push(
            new webpack.IgnorePlugin({
              resourceRegExp: /^\.\.\/\.\.\/static-data/,
              contextRegExp: /lib\/utils/,
            })
          );
        }
      } catch (error) {
        console.warn('Failed to add IgnorePlugin:', error);
      }
    }

    // 添加 MongoDB 相关模块到 externals 中，避免在客户端打包
    config.externals.push({
      mongodb: 'mongodb',
      'mongodb-client-encryption': 'mongodb-client-encryption',
      'kerberos': 'kerberos',
      'aws4': 'aws4',
      'snappy': 'snappy',
      'bson-ext': 'bson-ext',
      '@mongodb-js/zstd': '@mongodb-js/zstd',
      'mongodb-client-encryption': 'mongodb-client-encryption',
      'saslprep': 'saslprep',
      'net': 'net',
      'tls': 'tls',
      'fs/promises': 'fs/promises',
      'child_process': 'child_process',
    });

    return config;
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'images.pexels.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'via.placeholder.com',
        port: '',
        pathname: '/**',
      },
    ],
    // 静态导出必须禁用图片优化
    unoptimized: true,
  },
  trailingSlash: true, // 为每个页面添加尾部斜杠，有助于静态托管
  // 配置静态导出时的基本路径
  basePath: process.env.NEXT_PUBLIC_BASE_PATH || '',
  // 禁用严格模式以避免静态导出时的一些问题
  reactStrictMode: false,
  // 排除 service-worker.js 和其他特殊文件
  skipTrailingSlashRedirect: true,
  skipMiddlewareUrlNormalize: true,
  


  // 性能优化配置
  experimental: {
    // optimizeCss: true, // 暂时禁用，因为critters模块问题
    optimizePackageImports: [
      'lucide-react',
      '@radix-ui/react-accordion',
      '@radix-ui/react-alert-dialog',
      '@radix-ui/react-avatar',
      '@radix-ui/react-checkbox',
      '@radix-ui/react-dialog',
      '@radix-ui/react-dropdown-menu',
      '@radix-ui/react-hover-card',
      '@radix-ui/react-label',
      '@radix-ui/react-popover',
      '@radix-ui/react-progress',
      '@radix-ui/react-radio-group',
      '@radix-ui/react-scroll-area',
      '@radix-ui/react-select',
      '@radix-ui/react-separator',
      '@radix-ui/react-slider',
      '@radix-ui/react-switch',
      '@radix-ui/react-tabs',
      '@radix-ui/react-toast',
      '@radix-ui/react-toggle',
      '@radix-ui/react-tooltip',
    ],
  },

  // 压缩配置
  compress: true,

  // 注意：静态导出不支持自定义headers，这些配置仅在服务器模式下有效
  // 在静态托管时，需要在托管服务商处配置这些headers
};

// 应用 Bundle Analyzer 插件
export default withBundleAnalyzer(nextConfig);
