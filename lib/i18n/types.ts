export interface Dictionary {
  common: {
    appName: string;
    slogan: string;
    searchPlaceholder: string;
    submitTool: string;
    login: string;
    signup: string;
    logout: string;
    profile: string;
    dashboard: string;
    settings: string;
    view: string;
    website: string;
    ago: string;
  };
  seo: {
    defaultTitle: string;
    defaultDescription: string;
    toolsTitle: string;
    toolsDescription: string;
    searchTitle: string;
    searchDescription: string;
    categoriesTitle: string;
    categoriesDescription: string;
    articlesTitle: string;
    articlesDescription: string;
    loginTitle: string;
    loginDescription: string;
    signupTitle: string;
    signupDescription: string;
  };
  search: {
    title: string;
    placeholder: string;
    button: string;
    found: string;
    result: string;
    results: string;
    noResults: string;
    noResultsTitle: string;
    noResultsDescription: string;
    startSearchTitle: string;
    startSearchDescription: string;
  };
  home: {
    hero: {
      title: string;
      subtitle: string;
      cta: string;
      stats: {
        categories: string;
        aiTools: string;
        activeUsers: string;
      };
    };
    categories: {
      title: string;
      viewAll: string;
    };
    featured: {
      title: string;
      viewAll: string;
    };
    popular: {
      title: string;
      viewAll: string;
    };
    new: {
      title: string;
      viewAll: string;
    };
  };
  tools: {
    title: string;
    description: string;
    filters: string;
    categories: string;
    allCategories: string;
    sortBy: string;
    highestRated: string;
    mostPopular: string;
    newestFirst: string;
    resetFilters: string;
    noToolsFound: string;
    noToolsFoundDescription: string;
    previous: string;
    next: string;
    page: string;
    showing: string;
    totalTools: string;
    foundTools: string;
    clearFilters: string;
  };
  tool: {
    reviews: string;
    writeReview: string;
    pricing: string;
    free: string;
    paid: string;
    freemium: string;
    visitWebsite: string;
    openWebsite: string;
    share: {
      title: string;
      copyLink: string;
      linkCopied: string;
      copySuccess: string;
      copyError: string;
      shareToTwitter: string;
      shareToFacebook: string;
      shareToLinkedin: string;
    };
    save: string;
    report: string;
    similarTools: string;
    description: string;
    collectionTime: string;
    monthlyVisits: string;
    socialMedia: string;
    tags: string;
    features: string;
    about: string;
    viewDetails: string;
    overview: string;
    keyFeatures: string;
    aboutDescription: string;
    featuresDescription: string;
    reviewsDescription: string;
    viewAllTools: string;
    badges: {
      featured: string;
      trending: string;
      new: string;
    };
    meta: {
      added: string;
      lastUpdated: string;
      rating: string;
      visits: string;
    };
  };
  user: {
    savedTools: string;
    reviews: string;
    submissions: string;
    accountDetails: string;
    updateProfile: string;
    changePassword: string;
  };
  navigation: {
    tools: string;
    categories: string;
    articles: string;
    submit: string;
    search: string;
  };
  errors: {
    notFoundTitle: string;
    notFoundDescription: string;
    backHome: string;
    browseTools: string;
    serverErrorTitle: string;
    serverErrorDescription: string;
    tryAgain: string;
  };
  footer: {
    about: string;
    contact: string;
    privacy: string;
    terms: string;
    copyright: string;
    product: string;
    tools: string;
    categories: string;
    resources: string;
    submit: string;
    search: string;
    legal: string;
  };
  categories: {
    title: string;
    description: string;
    tools: string;
    viewAll: string;
    discoverMore: string;
  };
  articles: {
    title: string;
    description: string;
    readMore: string;
    readTime: string;
    publishedOn: string;
    author: string;
    tags: string;
    relatedArticles: string;
    latestArticles: string;
    featuredArticles: string;
    noArticles: string;
    noArticlesDescription: string;
    backToArticles: string;
    shareArticle: string;
    category: string;
    allCategories: string;
  };
  submit: {
    title: string;
    description: string;
    success: string;
    successDescription: string;
    error: string;
    errorDescription: string;
    form: {
      name: string;
      namePlaceholder: string;
      nameDescription: string;
      description: string;
      descriptionPlaceholder: string;
      descriptionDescription: string;
      website: string;
      websitePlaceholder: string;
      websiteDescription: string;
      category: string;
      categoryPlaceholder: string;
      categoryDescription: string;
      email: string;
      emailPlaceholder: string;
      emailDescription: string;
      additionalInfo: string;
      additionalInfoPlaceholder: string;
      additionalInfoDescription: string;
      submit: string;
      submitting: string;
    };
    beforeSubmitting: {
      title: string;
      description: string;
    };
    successPage: {
      title: string;
      description: string;
      redirecting: string;
    };
  };
  privacy: {
    title: string;
    description: string;
    lastUpdated: string;
    sections: {
      dataCollection: {
        title: string;
        content: string;
      };
      dataUsage: {
        title: string;
        content: string;
      };
      dataSecurity: {
        title: string;
        content: string;
      };
      cookies: {
        title: string;
        content: string;
      };
      thirdParty: {
        title: string;
        content: string;
      };
      userRights: {
        title: string;
        content: string;
      };
      contact: {
        title: string;
        content: string;
      };
    };
  };
  terms: {
    title: string;
    description: string;
    lastUpdated: string;
    sections: {
      acceptance: {
        title: string;
        content: string;
      };
      services: {
        title: string;
        content: string;
      };
      userConduct: {
        title: string;
        content: string;
      };
      intellectualProperty: {
        title: string;
        content: string;
      };
      disclaimer: {
        title: string;
        content: string;
      };
      limitation: {
        title: string;
        content: string;
      };
      termination: {
        title: string;
        content: string;
      };
      changes: {
        title: string;
        content: string;
      };
      contact: {
        title: string;
        content: string;
      };
    };
  };
}
