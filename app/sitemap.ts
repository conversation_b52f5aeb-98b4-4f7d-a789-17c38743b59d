import { MetadataRoute } from 'next';
import { locales } from '@/lib/i18n/config';
import { getAllTools } from '@/lib/db/tools';
import { getAllCategories } from '@/lib/db/categories';
import { getAllArticles } from '@/lib/db/articles';

// 为静态导出配置
export const dynamic = 'force-static';
export const revalidate = 86400; // 每天重新验证一次

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = 'https://x114.org';
  const sitemapEntries: MetadataRoute.Sitemap = [];

  // 基础静态页面
  const staticPages = [
    { path: '', priority: 1.0, changeFrequency: 'daily' as const },
    { path: '/tools', priority: 0.9, changeFrequency: 'daily' as const },
    { path: '/categories', priority: 0.8, changeFrequency: 'weekly' as const },
    { path: '/articles', priority: 0.8, changeFrequency: 'daily' as const },
    { path: '/submit', priority: 0.6, changeFrequency: 'monthly' as const },
    { path: '/privacy', priority: 0.3, changeFrequency: 'yearly' as const },
    { path: '/terms', priority: 0.3, changeFrequency: 'yearly' as const },
  ];

  // 为每个语言添加静态页面
  for (const locale of locales) {
    for (const page of staticPages) {
      sitemapEntries.push({
        url: `${baseUrl}/${locale}${page.path}`,
        lastModified: new Date(),
        changeFrequency: page.changeFrequency,
        priority: page.priority,
      });
    }

    try {
      // 获取并添加工具页面
      const tools = await getAllTools(locale);
      for (const tool of tools) {
        if (tool.slug) {
          sitemapEntries.push({
            url: `${baseUrl}/${locale}/tool/${tool.slug}`,
            lastModified: tool.updatedAt ? new Date(tool.updatedAt) : new Date(),
            changeFrequency: 'weekly',
            priority: tool.featured ? 0.8 : 0.7,
          });
        }
      }

      // 获取并添加类别页面
      const categories = await getAllCategories(locale);
      for (const category of categories) {
        if (category.slug) {
          sitemapEntries.push({
            url: `${baseUrl}/${locale}/category/${category.slug}`,
            lastModified: new Date(),
            changeFrequency: 'weekly',
            priority: 0.6,
          });
        }
      }

      // 获取并添加文章页面
      const articles = await getAllArticles(locale);
      for (const article of articles) {
        if (article.slug) {
          // 处理时间戳字段，确保正确转换为Date对象
          let lastModified = new Date();
          if (article.updated_at) {
            // updated_at是时间戳（数字），需要乘以1000转换为毫秒
            lastModified = new Date(typeof article.updated_at === 'number' ? article.updated_at * 1000 : article.updated_at);
          } else if (article.updatedAt) {
            lastModified = new Date(article.updatedAt);
          }

          sitemapEntries.push({
            url: `${baseUrl}/${locale}/article/${article.slug}`,
            lastModified,
            changeFrequency: 'monthly',
            priority: article.featured ? 0.7 : 0.5,
          });
        }
      }
    } catch (error) {
      console.error(`Error generating sitemap for locale ${locale}:`, error);

      // 在出错时，至少添加一些常用的工具和类别作为回退
      const fallbackTools = ['chatgpt', 'midjourney', 'copilot', 'claude', 'dalle'];
      for (const toolSlug of fallbackTools) {
        sitemapEntries.push({
          url: `${baseUrl}/${locale}/tool/${toolSlug}`,
          lastModified: new Date(),
          changeFrequency: 'weekly',
          priority: 0.7,
        });
      }

      const fallbackCategories = ['chatbots', 'image-generation', 'writing', 'coding', 'video'];
      for (const categorySlug of fallbackCategories) {
        sitemapEntries.push({
          url: `${baseUrl}/${locale}/category/${categorySlug}`,
          lastModified: new Date(),
          changeFrequency: 'weekly',
          priority: 0.6,
        });
      }
    }
  }

  return sitemapEntries;
}
