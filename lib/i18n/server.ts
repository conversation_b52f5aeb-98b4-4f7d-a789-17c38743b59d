/**
 * 服务器端国际化工具
 * 提供服务器端渲染时使用的国际化功能
 */

import { Locale, defaultLocale, localeRegions } from './config';
import { getDictionary } from './get-dictionary';
import { formatNumber, formatDate, formatCurrency, mapLocaleToIntl } from './utils';

/**
 * 获取指定语言的翻译字典
 * @param locale 语言代码
 * @returns 翻译字典
 */
export function getTranslations(locale: Locale = defaultLocale) {
  return getDictionary(locale);
}

/**
 * 导出格式化工具函数，用于服务器端渲染
 */
export { formatNumber, formatDate, formatCurrency, mapLocaleToIntl };