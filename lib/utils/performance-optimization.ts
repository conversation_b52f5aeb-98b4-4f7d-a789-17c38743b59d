/**
 * 性能优化工具函数
 *
 * 提供各种性能优化相关的工具函数
 */

// 预加载关键资源
export function preloadCriticalResources() {
  if (typeof window === 'undefined') return;

  // 预加载关键字体 - 使用Google Fonts
  const fontLink = document.createElement('link');
  fontLink.href = 'https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap';
  fontLink.rel = 'preload';
  fontLink.as = 'style';
  fontLink.onload = () => {
    fontLink.rel = 'stylesheet';
  };
  document.head.appendChild(fontLink);

  // 预加载关键CSS - 动态获取实际的CSS文件
  const cssLinks = document.querySelectorAll('link[rel="stylesheet"]');
  cssLinks.forEach((link) => {
    const href = (link as HTMLLinkElement).href;
    if (href.includes('/_next/static/css/')) {
      const preloadLink = document.createElement('link');
      preloadLink.rel = 'preload';
      preloadLink.href = href;
      preloadLink.as = 'style';
      document.head.appendChild(preloadLink);
    }
  });
}

// 懒加载图片
export function setupLazyLoading() {
  if (typeof window === 'undefined') return;

  // 使用 Intersection Observer 实现图片懒加载
  const imageObserver = new IntersectionObserver((entries, observer) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        const img = entry.target as HTMLImageElement;
        const src = img.dataset.src;

        if (src) {
          img.src = src;
          img.removeAttribute('data-src');
          img.classList.remove('lazy');
          observer.unobserve(img);
        }
      }
    });
  }, {
    rootMargin: '50px 0px',
    threshold: 0.01,
  });

  // 观察所有懒加载图片
  document.querySelectorAll('img[data-src]').forEach((img) => {
    imageObserver.observe(img);
  });
}

// 预连接到外部域名
export function setupPreconnections() {
  if (typeof window === 'undefined') return;

  const domains = [
    'https://images.pexels.com',
    'https://via.placeholder.com',
    'https://fonts.googleapis.com',
    'https://fonts.gstatic.com',
  ];

  domains.forEach((domain) => {
    const link = document.createElement('link');
    link.rel = 'preconnect';
    link.href = domain;
    link.crossOrigin = 'anonymous';
    document.head.appendChild(link);
  });
}

// 注册 Service Worker
export async function registerServiceWorker() {
  if (typeof window === 'undefined' || !('serviceWorker' in navigator)) {
    return;
  }

  try {
    const registration = await navigator.serviceWorker.register('/sw.js', {
      scope: '/',
    });

    console.log('Service Worker registered successfully:', registration);

    // 监听更新
    registration.addEventListener('updatefound', () => {
      const newWorker = registration.installing;
      if (newWorker) {
        newWorker.addEventListener('statechange', () => {
          if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
            // 新版本可用，提示用户刷新
            console.log('New version available! Please refresh the page.');
          }
        });
      }
    });

    return registration;
  } catch (error) {
    console.error('Service Worker registration failed:', error);
  }
}

// 优化关键渲染路径
export function optimizeCriticalRenderingPath() {
  if (typeof window === 'undefined') return;

  // 移除阻塞渲染的资源
  const nonCriticalStyles = document.querySelectorAll('link[rel="stylesheet"]:not([data-critical])');

  nonCriticalStyles.forEach((link) => {
    const href = (link as HTMLLinkElement).href;

    // 异步加载非关键CSS
    const asyncLink = document.createElement('link');
    asyncLink.rel = 'preload';
    asyncLink.href = href;
    asyncLink.as = 'style';
    asyncLink.onload = () => {
      asyncLink.rel = 'stylesheet';
    };

    document.head.appendChild(asyncLink);
    link.remove();
  });
}

// 内存优化
export function optimizeMemoryUsage() {
  if (typeof window === 'undefined') return;

  // 清理未使用的事件监听器
  const cleanupEventListeners = () => {
    // 这里可以添加具体的清理逻辑
    console.log('Cleaning up event listeners...');
  };

  // 页面卸载时清理
  window.addEventListener('beforeunload', cleanupEventListeners);

  // 定期清理
  setInterval(() => {
    if (document.hidden) {
      // 页面不可见时进行清理
      cleanupEventListeners();
    }
  }, 30000); // 30秒
}

// 网络优化
export function optimizeNetworkRequests() {
  if (typeof window === 'undefined') return;

  // 使用 requestIdleCallback 延迟非关键请求
  const scheduleNonCriticalRequests = (callback: () => void) => {
    if ('requestIdleCallback' in window) {
      requestIdleCallback(callback, { timeout: 5000 });
    } else {
      setTimeout(callback, 1);
    }
  };

  return { scheduleNonCriticalRequests };
}

// 初始化所有性能优化
export function initializePerformanceOptimizations() {
  if (typeof window === 'undefined') return;

  // 等待 DOM 加载完成
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setupOptimizations();
    });
  } else {
    setupOptimizations();
  }

  function setupOptimizations() {
    preloadCriticalResources();
    setupPreconnections();
    setupLazyLoading();
    registerServiceWorker();
    optimizeCriticalRenderingPath();
    optimizeMemoryUsage();
    optimizeNetworkRequests();
  }
}

// 性能监控
export function setupPerformanceMonitoring() {
  if (typeof window === 'undefined') return;

  // 监控 Core Web Vitals
  const observer = new PerformanceObserver((list) => {
    list.getEntries().forEach((entry) => {
      const value = (entry as any).value || entry.startTime;
      console.log(`${entry.name}: ${value}`);

      // 这里可以发送到分析服务
      // analytics.track('performance', {
      //   metric: entry.name,
      //   value: value,
      //   rating: (entry as any).rating
      // });
    });
  });

  // 观察 LCP, FID, CLS
  try {
    observer.observe({ entryTypes: ['largest-contentful-paint', 'first-input', 'layout-shift'] });
  } catch (error) {
    console.warn('Performance Observer not supported for Core Web Vitals:', error);
  }

  // 监控资源加载时间
  window.addEventListener('load', () => {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;

    if (navigation) {
      console.log('Performance Metrics:', {
        'DNS Lookup': navigation.domainLookupEnd - navigation.domainLookupStart,
        'TCP Connection': navigation.connectEnd - navigation.connectStart,
        'Request': navigation.responseStart - navigation.requestStart,
        'Response': navigation.responseEnd - navigation.responseStart,
        'DOM Processing': navigation.domComplete - navigation.domContentLoadedEventStart,
        'Total Load Time': navigation.loadEventEnd - navigation.fetchStart,
      });
    }
  });
}
