'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Category } from '@/lib/types';
import { Locale } from '@/lib/i18n/config';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { toast } from 'sonner';
import { AlertCircle, Loader2 } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

const formSchema = z.object({
  name: z.string().min(2, 'Tool name must be at least 2 characters'),
  description: z.string().min(20, 'Description must be at least 20 characters'),
  website: z.string().url('Please enter a valid URL'),
  category: z.string().min(1, 'Please select a category'),
  email: z.string().email('Please enter a valid email'),
  additionalInfo: z.string().optional(),
});

interface SubmissionResponse {
  success: boolean;
  message?: string;
  submissionId?: string;
  error?: string;
}

export default function ToolSubmissionForm({
  categories,
  locale,
  dictionary
}: {
  categories: Category[];
  locale: Locale;
  dictionary: any;
}) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const router = useRouter();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      description: '',
      website: '',
      category: '',
      email: '',
      additionalInfo: '',
    },
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsSubmitting(true);

    try {
      // 提交到Cloudflare Functions API
      const response = await fetch('/api/submit-tool', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...values,
          locale,
          submittedAt: new Date().toISOString(),
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json() as SubmissionResponse;

      if (result.success) {
        setIsSubmitted(true);
        toast.success(dictionary?.submit?.success || 'Tool submitted successfully!', {
          description: dictionary?.submit?.successDescription || 'We will review your submission and get back to you soon.',
        });

        setTimeout(() => {
          router.push(`/${locale}`);
        }, 3000);
      } else {
        throw new Error(result.error || 'Submission failed');
      }
    } catch (error) {
      console.error('Submission error:', error);
      toast.error(dictionary?.submit?.error || 'Failed to submit tool', {
        description: dictionary?.submit?.errorDescription || 'Please try again later or contact support.',
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  if (isSubmitted) {
    return (
      <div className="bg-card border rounded-lg p-8 text-center">
        <div className="w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg
            className="w-8 h-8 text-green-600 dark:text-green-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M5 13l4 4L19 7"
            ></path>
          </svg>
        </div>
        <h2 className="text-2xl font-bold mb-2">{dictionary?.submit?.successPage?.title || 'Submission Received!'}</h2>
        <p className="text-muted-foreground mb-6">
          {dictionary?.submit?.successPage?.description || 'Thank you for submitting your tool. Our team will review it and add it to the directory once approved.'}
        </p>
        <p className="text-sm text-muted-foreground">
          {dictionary?.submit?.successPage?.redirecting || 'Redirecting you to the homepage...'}
        </p>
      </div>
    );
  }

  return (
    <div className="bg-card border rounded-lg p-8">
      <Alert className="mb-6">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>{dictionary?.submit?.beforeSubmitting?.title || 'Before submitting'}</AlertTitle>
        <AlertDescription>
          {dictionary?.submit?.beforeSubmitting?.description || 'Make sure your tool uses AI technology and is fully functional. We do not accept concepts, pre-launch products, or tools that are still in development.'}
        </AlertDescription>
      </Alert>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{dictionary?.submit?.form?.name || 'Tool Name'}</FormLabel>
                <FormControl>
                  <Input placeholder={dictionary?.submit?.form?.namePlaceholder || 'e.g., AI Assistant Pro'} {...field} />
                </FormControl>
                <FormDescription>
                  {dictionary?.submit?.form?.nameDescription || 'The name of your AI tool as it should appear in our directory.'}
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{dictionary?.submit?.form?.description || 'Description'}</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder={dictionary?.submit?.form?.descriptionPlaceholder || 'Briefly describe what your tool does and its main benefits...'}
                    className="h-32"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  {dictionary?.submit?.form?.descriptionDescription || 'Provide a clear and concise description (50-200 words).'}
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name="website"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{dictionary?.submit?.form?.website || 'Website URL'}</FormLabel>
                  <FormControl>
                    <Input placeholder={dictionary?.submit?.form?.websitePlaceholder || 'https://yourtool.com'} {...field} />
                  </FormControl>
                  <FormDescription>
                    {dictionary?.submit?.form?.websiteDescription || 'The main URL where users can access your tool.'}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="category"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{dictionary?.submit?.form?.category || 'Category'}</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder={dictionary?.submit?.form?.categoryPlaceholder || 'Select a category'} />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.icon} {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    {dictionary?.submit?.form?.categoryDescription || 'Choose the category that best fits your tool.'}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{dictionary?.submit?.form?.email || 'Contact Email'}</FormLabel>
                <FormControl>
                  <Input placeholder={dictionary?.submit?.form?.emailPlaceholder || '<EMAIL>'} {...field} />
                </FormControl>
                <FormDescription>
                  {dictionary?.submit?.form?.emailDescription || "We'll use this to contact you about your submission."}
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="additionalInfo"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{dictionary?.submit?.form?.additionalInfo || 'Additional Information (Optional)'}</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder={dictionary?.submit?.form?.additionalInfoPlaceholder || 'Any other relevant details about your tool...'}
                    className="h-24"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  {dictionary?.submit?.form?.additionalInfoDescription || "Include any additional details you'd like us to know."}
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex justify-end">
            <Button type="submit" size="lg" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {dictionary?.submit?.form?.submitting || 'Submitting...'}
                </>
              ) : (
                dictionary?.submit?.form?.submit || 'Submit Tool'
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}