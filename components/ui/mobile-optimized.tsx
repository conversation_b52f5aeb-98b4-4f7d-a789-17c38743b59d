'use client';

import React, { useState, useEffect, useRef } from 'react';
import { ChevronDown, ChevronUp, Menu, X, Search } from 'lucide-react';
import { Button } from './button';
import { Input } from './input';
import { cn } from '@/lib/utils';

// 移动端检测 Hook
export function useIsMobile() {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkIsMobile();
    window.addEventListener('resize', checkIsMobile);
    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  return isMobile;
}

// 触摸手势 Hook
export function useSwipeGesture(
  onSwipeLeft?: () => void,
  onSwipeRight?: () => void,
  threshold: number = 50
) {
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);

  const onTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const onTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > threshold;
    const isRightSwipe = distance < -threshold;

    if (isLeftSwipe && onSwipeLeft) {
      onSwipeLeft();
    }
    if (isRightSwipe && onSwipeRight) {
      onSwipeRight();
    }
  };

  return {
    onTouchStart,
    onTouchMove,
    onTouchEnd
  };
}

// 移动端友好的下拉菜单
interface MobileDropdownProps {
  trigger: React.ReactNode;
  children: React.ReactNode;
  className?: string;
}

export function MobileDropdown({ trigger, children, className }: MobileDropdownProps) {
  const [isOpen, setIsOpen] = useState(false);
  const isMobile = useIsMobile();

  if (isMobile) {
    return (
      <div className={className}>
        <Button
          variant="ghost"
          onClick={() => setIsOpen(!isOpen)}
          className="w-full justify-between"
        >
          {trigger}
          {isOpen ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
        </Button>
        {isOpen && (
          <div className="mt-2 p-4 bg-card border rounded-lg shadow-lg">
            {children}
          </div>
        )}
      </div>
    );
  }

  // 桌面端使用常规下拉菜单
  return (
    <div className={cn('relative', className)}>
      {trigger}
      {children}
    </div>
  );
}

// 移动端抽屉组件
interface MobileDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  title?: string;
  position?: 'bottom' | 'top' | 'left' | 'right';
  className?: string;
}

export function MobileDrawer({
  isOpen,
  onClose,
  children,
  title,
  position = 'bottom',
  className
}: MobileDrawerProps) {
  const swipeGesture = useSwipeGesture(
    position === 'right' ? onClose : undefined,
    position === 'left' ? onClose : undefined
  );

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  if (!isOpen) return null;

  const positionClasses = {
    bottom: 'bottom-0 left-0 right-0 rounded-t-lg',
    top: 'top-0 left-0 right-0 rounded-b-lg',
    left: 'left-0 top-0 bottom-0 rounded-r-lg w-80',
    right: 'right-0 top-0 bottom-0 rounded-l-lg w-80'
  };

  const animationClasses = {
    bottom: 'animate-in slide-in-from-bottom',
    top: 'animate-in slide-in-from-top',
    left: 'animate-in slide-in-from-left',
    right: 'animate-in slide-in-from-right'
  };

  return (
    <>
      {/* 背景遮罩 */}
      <div
        className="fixed inset-0 bg-background/80 backdrop-blur-sm z-40"
        onClick={onClose}
      />
      
      {/* 抽屉内容 */}
      <div
        className={cn(
          'fixed bg-background border shadow-lg z-50',
          positionClasses[position],
          animationClasses[position],
          className
        )}
        {...swipeGesture}
      >
        {title && (
          <div className="flex items-center justify-between p-4 border-b">
            <h3 className="text-lg font-semibold">{title}</h3>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        )}
        <div className="p-4">
          {children}
        </div>
      </div>
    </>
  );
}

// 移动端搜索栏
interface MobileSearchProps {
  placeholder?: string;
  onSearch?: (query: string) => void;
  className?: string;
}

export function MobileSearch({ placeholder = 'Search...', onSearch, className }: MobileSearchProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [query, setQuery] = useState('');
  const inputRef = useRef<HTMLInputElement>(null);
  const isMobile = useIsMobile();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch?.(query);
  };

  const handleExpand = () => {
    setIsExpanded(true);
    setTimeout(() => inputRef.current?.focus(), 100);
  };

  const handleCollapse = () => {
    setIsExpanded(false);
    setQuery('');
  };

  if (!isMobile) {
    return (
      <form onSubmit={handleSubmit} className={cn('relative', className)}>
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          ref={inputRef}
          type="search"
          placeholder={placeholder}
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          className="pl-10"
        />
      </form>
    );
  }

  return (
    <div className={cn('relative', className)}>
      {!isExpanded ? (
        <Button
          variant="ghost"
          size="sm"
          onClick={handleExpand}
          className="h-10 w-10 p-0"
        >
          <Search className="h-4 w-4" />
        </Button>
      ) : (
        <form onSubmit={handleSubmit} className="flex items-center space-x-2">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              ref={inputRef}
              type="search"
              placeholder={placeholder}
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={handleCollapse}
            className="h-10 w-10 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </form>
      )}
    </div>
  );
}

// 移动端卡片组件
interface MobileCardProps {
  children: React.ReactNode;
  onTap?: () => void;
  className?: string;
}

export function MobileCard({ children, onTap, className }: MobileCardProps) {
  const [isPressed, setIsPressed] = useState(false);

  const handleTouchStart = () => {
    setIsPressed(true);
  };

  const handleTouchEnd = () => {
    setIsPressed(false);
    onTap?.();
  };

  return (
    <div
      className={cn(
        'bg-card border rounded-lg p-4 transition-all duration-150',
        onTap && 'cursor-pointer active:scale-95',
        isPressed && 'bg-muted',
        className
      )}
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
      onClick={onTap}
    >
      {children}
    </div>
  );
}

// 移动端底部导航
interface MobileBottomNavProps {
  items: Array<{
    icon: React.ReactNode;
    label: string;
    active?: boolean;
    onClick?: () => void;
  }>;
  className?: string;
}

export function MobileBottomNav({ items, className }: MobileBottomNavProps) {
  const isMobile = useIsMobile();

  if (!isMobile) return null;

  return (
    <nav className={cn(
      'fixed bottom-0 left-0 right-0 bg-background border-t z-40',
      'flex items-center justify-around py-2 px-4',
      className
    )}>
      {items.map((item, index) => (
        <button
          key={index}
          onClick={item.onClick}
          className={cn(
            'flex flex-col items-center space-y-1 p-2 rounded-lg transition-colors',
            'min-w-0 flex-1',
            item.active ? 'text-primary bg-primary/10' : 'text-muted-foreground'
          )}
        >
          <div className="h-6 w-6 flex items-center justify-center">
            {item.icon}
          </div>
          <span className="text-xs font-medium truncate">
            {item.label}
          </span>
        </button>
      ))}
    </nav>
  );
}

// 移动端安全区域组件
interface SafeAreaProps {
  children: React.ReactNode;
  className?: string;
}

export function SafeArea({ children, className }: SafeAreaProps) {
  return (
    <div className={cn('pb-safe-bottom pt-safe-top', className)}>
      {children}
    </div>
  );
}
