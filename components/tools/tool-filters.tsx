'use client';

import { useState, useEffect } from 'react';
import { useRouter, usePathname, useSearchParams } from 'next/navigation';
import { Category } from '@/lib/types';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { MobileDrawer } from '@/components/ui/mobile-optimized';
import { AccessibleButton } from '@/components/ui/accessibility';
import { LoadingButton } from '@/components/ui/loading-states';
import { useUserPreferences, useLoadingState } from '@/hooks/use-ux-enhancements';
import { Filter } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ToolFiltersProps {
  categories: Category[];
  selectedCategory?: string;
  selectedSort?: string;
  locale: string;
  dictionary?: any;
}

export default function ToolFilters({
  categories,
  selectedCategory,
  selectedSort,
  locale,
  dictionary
}: ToolFiltersProps) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const { isMobile, prefersReducedMotion } = useUserPreferences();
  const { isLoading, startLoading, stopLoading } = useLoadingState();

  const [mounted, setMounted] = useState(false);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  // Set mounted state to true on client side
  useEffect(() => {
    setMounted(true);
  }, []);

  // Function to update query parameters
  const updateFilters = async (name: string, value: string | null) => {
    startLoading();

    const params = new URLSearchParams(searchParams);

    if (value) {
      params.set(name, value);
    } else {
      params.delete(name);
    }

    // Reset to page 1 when filters change
    params.delete('page');

    try {
      router.push(`${pathname}?${params.toString()}`);

      // 在移动端关闭抽屉
      if (isMobile) {
        setIsDrawerOpen(false);
      }
    } finally {
      // 模拟加载延迟以提供视觉反馈
      setTimeout(stopLoading, 300);
    }
  };

  // 重置所有过滤器
  const resetFilters = async () => {
    startLoading();
    try {
      router.push(pathname);
      if (isMobile) {
        setIsDrawerOpen(false);
      }
    } finally {
      setTimeout(stopLoading, 300);
    }
  };

  // Don't render on the server to avoid hydration issues
  if (!mounted) return null;

  // 过滤器内容组件
  const FilterContent = () => (
    <div className="space-y-6">
      <Accordion type="multiple" defaultValue={['categories', 'sort']}>
        <AccordionItem value="categories">
          <AccordionTrigger className="text-sm font-medium">{dictionary?.tools?.categories || 'Categories'}</AccordionTrigger>
          <AccordionContent>
            <div className="space-y-3 pt-2">
              <AccessibleButton
                className={cn(
                  'w-full px-3 py-2 rounded-md text-sm text-left min-touch-target',
                  !selectedCategory ? 'bg-muted font-medium' : 'hover:bg-muted/50'
                )}
                onClick={() => updateFilters('category', null)}
                description={dictionary?.tools?.showAllCategories || 'Show all categories'}
              >
                {dictionary?.tools?.allCategories || 'All Categories'}
              </AccessibleButton>

              {categories.map((category) => (
                <AccessibleButton
                  key={category.id}
                  className={cn(
                    'w-full px-3 py-2 rounded-md text-sm flex items-center justify-between min-touch-target',
                    selectedCategory === category.id ? 'bg-muted font-medium' : 'hover:bg-muted/50'
                  )}
                  onClick={() => updateFilters('category', category.id)}
                  description={`Filter by ${category.name} category`}
                >
                  <div className="flex items-center">
                    <span className="mr-2" aria-hidden="true">{category.icon}</span>
                    <span>{category.name}</span>
                  </div>
                  <span className="text-xs text-muted-foreground" aria-label={`${category.toolCount} tools`}>
                    {category.toolCount}
                  </span>
                </AccessibleButton>
              ))}
            </div>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="sort">
          <AccordionTrigger className="text-sm font-medium">{dictionary?.tools?.sortBy || 'Sort By'}</AccordionTrigger>
          <AccordionContent>
            <Select
              value={selectedSort || 'rating'}
              onValueChange={(value) => updateFilters('sort', value)}
            >
              <SelectTrigger className="w-full min-touch-target">
                <SelectValue placeholder={dictionary?.tools?.sortBy || 'Sort by'} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="rating">{dictionary?.tools?.highestRated || 'Highest Rated'}</SelectItem>
                <SelectItem value="popular">{dictionary?.tools?.mostPopular || 'Most Popular'}</SelectItem>
                <SelectItem value="newest">{dictionary?.tools?.newestFirst || 'Newest First'}</SelectItem>
              </SelectContent>
            </Select>
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      <LoadingButton
        variant="outline"
        className="w-full"
        onClick={resetFilters}
        isLoading={isLoading}
        loadingText={dictionary?.tools?.resetting || 'Resetting...'}
      >
        {dictionary?.tools?.resetFilters || 'Reset Filters'}
      </LoadingButton>
    </div>
  );

  // 移动端渲染
  if (isMobile) {
    return (
      <>
        <Button
          variant="outline"
          onClick={() => setIsDrawerOpen(true)}
          className="w-full mb-4 min-touch-target"
        >
          <Filter className="mr-2 h-4 w-4" />
          {dictionary?.tools?.filters || 'Filters'}
        </Button>

        <MobileDrawer
          isOpen={isDrawerOpen}
          onClose={() => setIsDrawerOpen(false)}
          title={dictionary?.tools?.filterTools || 'Filter Tools'}
          position="bottom"
        >
          <FilterContent />
        </MobileDrawer>
      </>
    );
  }

  // 桌面端渲染
  return (
    <div className="rounded-lg border bg-card p-6 sticky top-24">
      <h2 className="font-semibold text-lg mb-6">{dictionary?.tools?.filters || 'Filters'}</h2>
      <FilterContent />
    </div>
  );
}