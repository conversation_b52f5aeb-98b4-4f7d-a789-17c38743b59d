import { Tool } from '../types';
import {
  getMongoDBTools,
  getMongoDBToolsFiltered,
  getMongoDBToolById,
  getMongoDBToolBySlug,
  getMongoDBRelatedTools
} from './mongodb-wrapper';

// 默认语言
const DEFAULT_LANGUAGE = 'en';

/**
 * 获取所有工具
 * @param language 语言，默认为英语
 * @returns 工具列表
 */
export async function getAllTools(language: string = DEFAULT_LANGUAGE): Promise<Tool[]> {
  return getMongoDBTools(language);
}

/**
 * 根据过滤条件获取工具
 * @param filters 过滤条件
 * @param language 语言，默认为英语
 * @returns 过滤后的工具列表
 */
export async function getTools(filters?: {
  category?: string;
  featured?: boolean;
  trending?: boolean;
  isNew?: boolean;
  minRating?: number;
  tags?: string[];
  sort?: 'newest' | 'popular' | 'rating';
  limit?: number;
  offset?: number;
}, language: string = DEFAULT_LANGUAGE): Promise<Tool[]> {
  return getMongoDBToolsFiltered(filters, language);
}

/**
 * 根据ID获取工具
 * @param id 工具ID
 * @param language 语言，默认为英语
 * @returns 工具对象，如果未找到则返回undefined
 */
export async function getToolById(id: string, language: string = DEFAULT_LANGUAGE): Promise<Tool | undefined> {
  return getMongoDBToolById(id, language);
}

/**
 * 根据ID获取工具
 * @param id 工具ID
 * @param language 语言，默认为英语
 * @returns 工具对象，如果未找到则返回undefined
 */
export async function getToolBySlug(slug: string, language: string = DEFAULT_LANGUAGE): Promise<Tool | undefined> {
  return getMongoDBToolBySlug(slug, language);
}

/**
 * 获取相关工具
 * @param tool 工具对象
 * @param limit 限制数量，默认为4
 * @param language 语言，默认为工具的语言或英语
 * @returns 相关工具列表
 */
export async function getRelatedTools(tool: Tool, limit: number = 4, language?: string): Promise<Tool[]> {
  return getMongoDBRelatedTools(tool, limit, language);
}

/**
 * 创建新工具
 * @param tool 工具对象
 * @returns 创建的工具对象
 */
export async function createTool(tool: Omit<Tool, '_id'>): Promise<Tool> {
  // 确保工具有语言字段
  const toolWithLanguage = {
    ...tool,
    language: tool.language || DEFAULT_LANGUAGE
  };

  // 在静态导出环境中，这个函数不会被调用
  // 但为了类型安全，我们返回一个模拟的工具对象
  if (process.env.STATIC_EXPORT === 'true') {
    return toolWithLanguage as Tool;
  }

  try {
    // 动态导入 MongoDB 模块
    const { createToolInMongoDB } = await import('./mongodb/tools');
    const newTool = await createToolInMongoDB(toolWithLanguage);
    return newTool;
  } catch (error) {
    console.error('Error creating tool in MongoDB:', error);
    throw error;
  }
}

/**
 * 更新工具
 * @param id 工具ID
 * @param tool 更新的工具字段
 * @returns 更新后的工具对象，如果未找到则返回null
 */
export async function updateTool(id: string, tool: Partial<Tool>): Promise<Tool | null> {
  // 在静态导出环境中，这个函数不会被调用
  if (process.env.STATIC_EXPORT === 'true') {
    return null;
  }

  try {
    // 动态导入 MongoDB 模块
    const { updateToolInMongoDB } = await import('./mongodb/tools');
    const updatedTool = await updateToolInMongoDB(id, tool);
    return updatedTool;
  } catch (error) {
    console.error(`Error updating tool with id ${id} in MongoDB:`, error);
    throw error;
  }
}

/**
 * 删除工具
 * @param id 工具ID
 * @returns 是否成功删除
 */
export async function deleteTool(id: string): Promise<boolean> {
  // 在静态导出环境中，这个函数不会被调用
  if (process.env.STATIC_EXPORT === 'true') {
    return false;
  }

  try {
    // 动态导入 MongoDB 模块
    const { deleteToolFromMongoDB } = await import('./mongodb/tools');
    const result = await deleteToolFromMongoDB(id);
    return result;
  } catch (error) {
    console.error(`Error deleting tool with id ${id} from MongoDB:`, error);
    throw error;
  }
}
