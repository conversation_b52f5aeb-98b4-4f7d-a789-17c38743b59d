# npm run build vs npm run export 对比分析

## 📋 概述

本文档详细分析了 X114 项目中 `npm run build` 和 `npm run export` 两个命令的区别、用途和适用场景。

## 🔧 命令定义

### `npm run build`
```json
"build": "next build"
```

### `npm run export`
```json
"preexport": "npm run clean",
"export": "npm run fetch-data && STATIC_EXPORT=true next build",
"fetch-data": "node scripts/fetch-static-data.js --force"
```

## 🎯 主要区别

### 1. 数据源差异

#### `npm run build`
- **数据源**: 直接连接 MongoDB 数据库
- **数据获取**: 运行时动态查询数据库
- **适用场景**: 服务器端渲染 (SSR) 或混合模式

#### `npm run export`
- **数据源**: 预先获取的静态 JSON 文件
- **数据获取**: 构建前通过 `fetch-static-data.js` 脚本获取数据并保存为静态文件
- **适用场景**: 完全静态网站部署 (SSG)

### 2. 构建流程差异

#### `npm run build` 流程
```
1. Next.js 构建开始
2. 页面生成时直接查询 MongoDB
3. 生成静态页面和服务器端代码
4. 输出到 .next 目录
```

#### `npm run export` 流程
```
1. 清理旧文件 (npm run clean)
2. 获取静态数据 (npm run fetch-data)
   - 连接 MongoDB
   - 按语言和类型获取数据
   - 保存为 JSON 文件到 lib/static-data/
3. 设置 STATIC_EXPORT=true 环境变量
4. Next.js 构建 (使用静态数据)
5. 生成完全静态的 HTML 文件
6. 输出到 out 目录
```

### 3. 输出差异

#### `npm run build`
- **输出目录**: `.next/`
- **文件类型**: 混合 (静态 + 服务器端代码)
- **页面数量**: 6368 页面
- **部署要求**: 需要 Node.js 服务器

#### `npm run export`
- **输出目录**: `out/`
- **文件类型**: 纯静态 HTML/CSS/JS
- **页面数量**: 6364 个 HTML 文件
- **部署要求**: 任何静态文件服务器 (CDN, GitHub Pages, Cloudflare Pages 等)

## 🌍 多语言支持

### 数据分布 (修复后)
```
EN: 596 tools, 22 categories, 9 articles
ZH: 596 tools, 22 categories, 9 articles
TW: 595 tools, 22 categories, 9 articles
KO: 599 tools, 22 categories, 9 articles
JA: 599 tools, 22 categories, 9 articles
PT: 599 tools, 22 categories, 9 articles
ES: 599 tools, 22 categories, 9 articles
DE: 599 tools, 22 categories, 9 articles
FR: 599 tools, 22 categories, 9 articles
VI: 600 tools, 22 categories, 9 articles
```

### 生成的页面结构
- 10 种语言 × 多种页面类型
- 工具详情页: ~6000 页面
- 分类页面: ~220 页面
- 文章页面: ~90 页面
- 其他页面: ~50 页面

## 🐛 问题排查记录

### 问题: 静态文件数量不完整
**原因**: 环境变量 `SUPPORTED_LANGUAGES` 中包含多余空格
```bash
# 错误配置
SUPPORTED_LANGUAGES=en, zh, tw, ko, ja, pt, es, de, fr, vi

# 正确配置
SUPPORTED_LANGUAGES=en,zh,tw,ko,ja,pt,es,de,fr,vi
```

**影响**: 导致语言代码变成 `" zh"`, `" tw"` 等，MongoDB 查询失败

**解决**: 移除空格后，所有语言数据正常获取

## ⚙️ 配置要点

### Next.js 配置 (next.config.mjs)
```javascript
const nextConfig = {
  // 静态导出配置
  output: 'export',
  
  // 静态导出时的特殊处理
  webpack: (config, { isServer, dev }) => {
    if (process.env.STATIC_EXPORT === 'true') {
      // MongoDB 模块替换为空模块
      if (!isServer) {
        config.resolve.fallback = {
          mongodb: false,
          // ... 其他 Node.js 模块
        };
      }
    }
    return config;
  },
  
  // 图片优化必须禁用
  images: {
    unoptimized: true,
  },
  
  // 添加尾部斜杠
  trailingSlash: true,
};
```

### 环境变量配置
```bash
# MongoDB 连接
MONGODB_URI=************************:port
MONGODB_DB=database_name

# 支持的语言 (注意: 不要有空格)
SUPPORTED_LANGUAGES=en,zh,tw,ko,ja,pt,es,de,fr,vi
```

## 🚀 部署建议

### 使用 `npm run build` 的场景
- 需要服务器端渲染 (SSR)
- 需要 API 路由
- 需要实时数据更新
- 部署到 Vercel, Netlify 等支持 Next.js 的平台

### 使用 `npm run export` 的场景
- 完全静态网站
- 部署到 CDN 或静态文件服务器
- GitHub Pages, Cloudflare Pages 等
- 需要最佳性能和缓存效果

## 📊 性能对比

### `npm run build`
- **构建时间**: 较快 (直接构建)
- **运行时性能**: 依赖服务器性能
- **缓存策略**: 需要配置服务器缓存
- **扩展性**: 支持动态功能

### `npm run export`
- **构建时间**: 较慢 (需要预获取数据)
- **运行时性能**: 最佳 (纯静态文件)
- **缓存策略**: CDN 级别缓存
- **扩展性**: 仅支持静态功能

## 🎯 最佳实践

1. **开发阶段**: 使用 `npm run build` 进行快速迭代
2. **生产部署**: 根据需求选择合适的构建方式
3. **静态部署**: 优先使用 `npm run export` 获得最佳性能
4. **数据更新**: 静态导出需要重新构建来更新数据

## 📝 总结

- `npm run build`: 适合动态网站和服务器端部署
- `npm run export`: 适合静态网站和 CDN 部署
- 两种方式都支持完整的多语言功能
- 选择取决于部署环境和性能需求
