import { getTools } from '@/lib/db/tools';
import ToolsSection from './tools-section';

/**
 * 热门工具区块组件
 * 展示热门的工具
 */
export default async function PopularToolsSection({ dictionary }: { dictionary: any }) {
  // 获取当前语言
  const locale = dictionary.locale || 'en';

  // 从数据库获取热门工具
  const popularTools = await getTools({ sort: 'popular', limit: 4 }, locale);

  return (
    <ToolsSection
      title={dictionary.home.popular.title}
      viewAllText={dictionary.home.popular.viewAll}
      viewAllLink={`/${locale}/tools?sort=popular`}
      tools={popularTools}
      locale={locale}
      dictionary={dictionary}
    />
  );
}