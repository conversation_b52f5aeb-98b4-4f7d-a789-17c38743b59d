import { ObjectId } from 'mongodb';
import { Tool, Category, Article } from '../../types';

/**
 * 通用的 MongoDB 文档转换函数
 * 将 MongoDB 文档转换为应用程序模型对象
 * 处理 _id 转换、日期字段处理和默认语言设置
 */
export function convertMongoDocument<T extends { id?: string; language?: string }>(
  doc: any,
  defaultLanguage: string = 'en'
): T {
  if (!doc) return null as unknown as T;

  // 提取 _id 并创建新对象
  const { _id, ...rest } = doc;

  // 基本转换
  const converted = {
    ...rest,
    id: doc.id || _id.toString(),
    language: doc.language || defaultLanguage
  };

  // 返回转换后的对象
  return converted as unknown as T;
}

/**
 * 转换 Tool 类型的 MongoDB 文档
 */
export function convertToolDocument(doc: any): Tool {
  if (!doc) return null as unknown as Tool;

  const converted = convertMongoDocument<Tool>(doc);

  // 处理日期字段
  if (converted) {
    converted.createdAt = new Date(doc.createdAt);
    converted.updatedAt = new Date(doc.updatedAt);
  }

  return converted;
}

/**
 * 转换 Category 类型的 MongoDB 文档
 */
export function convertCategoryDocument(doc: any): Category {
  return convertMongoDocument<Category>(doc);
}

/**
 * 转换 Article 类型的 MongoDB 文档
 */
export function convertArticleDocument(doc: any): Article {
  if (!doc) return null as unknown as Article;

  const converted = convertMongoDocument<Article>(doc);

  // 处理日期字段 - 支持时间戳和日期对象
  if (converted) {
    // 处理 created_at 字段映射
    const createdAt = doc.created_at || doc.createdAt;
    converted.createdAt = createdAt ? new Date(typeof createdAt === 'number' ? createdAt * 1000 : createdAt) : new Date();

    // 处理 updated_at 字段映射
    const updatedAt = doc.updated_at || doc.updatedAt;
    converted.updatedAt = updatedAt ? new Date(typeof updatedAt === 'number' ? updatedAt * 1000 : updatedAt) : new Date();

    // 处理 published 字段映射 - 保持原始数字时间戳
    converted.published = doc.published || (doc.status === 2 ? Date.now() / 1000 : 0);

    // 处理 publishedAt 字段映射
    const publishedAt = doc.published || doc.publishedAt;
    converted.publishedAt = publishedAt ? new Date(typeof publishedAt === 'number' ? publishedAt * 1000 : publishedAt) : new Date();

    // 确保 tags 是数组
    converted.tags = Array.isArray(doc.tags) ? doc.tags : [];

    // 设置默认作者
    converted.author = doc.author || 'X114 Team';

    // 设置默认分类
    converted.category = doc.category || 'General';

    // 处理 featured 字段
    converted.featured = Boolean(doc.featured);
  }

  return converted;
}

/**
 * 转换 MongoDB 文档数组
 */
export function convertDocumentArray<T>(
  docs: any[],
  converter: (doc: any) => T
): T[] {
  if (!docs || !Array.isArray(docs)) return [];
  return docs.map(doc => converter(doc));
}

/**
 * 构建 MongoDB 查询，支持按 ID 或 ObjectId 查询
 */
export function buildIdQuery(id: string, language?: string): any {
  // 基本查询
  let query: any = { id };

  // 添加语言筛选
  if (language) {
    query.language = language;
  }

  return query;
}

/**
 * 构建 MongoDB 查询，支持按 Slug 查询
 */
export function buildSlugQuery(slug: string, language?: string): any {
  // 基本查询
  let query: any = { slug };

  // 添加语言筛选
  if (language) {
    query.language = language;
  }

  return query;
}

/**
 * 尝试使用 ID 或 ObjectId 查找文档
 * 如果找不到，尝试不同的查询策略
 */
export async function findDocumentById<T>(
  collection: any,
  id: string,
  converter: (doc: any) => T,
  language?: string
): Promise<T | null> {
  // 尝试使用 id 字段查找
  let query = buildIdQuery(id, language);
  let doc = await collection.findOne(query);

  // 如果没有找到，尝试使用 _id 查找（如果 id 是有效的 ObjectId）
  if (!doc && ObjectId.isValid(id)) {
    query = { _id: new ObjectId(id) };
    if (language) {
      query.language = language;
    }
    doc = await collection.findOne(query);
  }

  // 如果指定了语言但仍未找到，尝试查找任何语言的文档
  if (!doc && language) {
    if (ObjectId.isValid(id)) {
      doc = await collection.findOne({ _id: new ObjectId(id) });
    }
    if (!doc) {
      doc = await collection.findOne({ id });
    }
  }

  // 如果找到文档，转换并返回
  if (doc) {
    return converter(doc);
  }

  return null;
}

/**
 * 尝试使用 Slug 查找文档
 * 如果找不到，尝试不同的查询策略
 */
export async function findDocumentBySlug<T>(
    collection: any,
    slug: string,
    converter: (doc: any) => T,
    language?: string
): Promise<T | null> {
  // 尝试使用 slug 字段查找
  let query = buildSlugQuery(slug, language);
  let doc = await collection.findOne(query);

  // 如果找到文档，转换并返回
  if (doc) {
    return converter(doc);
  }

  return null;
}
