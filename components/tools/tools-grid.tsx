'use client';

import { Tool } from '@/lib/types';
import ToolCard from './tool-card';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';

interface ToolsGridProps {
  tools: Tool[];
  currentPage: number;
  totalPages: number;
  locale: string;
  dictionary?: any;
  gridClassName?: string;
}

/**
 * 工具网格组件
 * 用于展示工具列表和分页
 */
export default function ToolsGrid({
  tools,
  currentPage,
  totalPages,
  locale,
  dictionary,
  gridClassName = 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
}: ToolsGridProps) {
  // 如果没有找到工具
  if (tools.length === 0) {
    return (
      <div className="py-12 text-center">
        <h3 className="text-xl font-medium mb-2">{dictionary?.tools?.noToolsFound || 'No tools found'}</h3>
        <p className="text-muted-foreground">{dictionary?.tools?.noToolsFoundDescription || 'Try adjusting your filters or search query.'}</p>
      </div>
    );
  }

  return (
    <div>
      <div className={`grid ${gridClassName} gap-6`}>
        {tools.map((tool) => (
          <ToolCard
            key={tool.id}
            tool={tool}
            locale={locale}
            dictionary={dictionary}
            showTooltips={false}
          />
        ))}
      </div>

      {/* 分页 */}
      {renderPagination(currentPage, totalPages, locale)}
    </div>
  );
}

/**
 * 渲染分页组件
 * @param currentPage 当前页码
 * @param totalPages 总页数
 * @param locale 语言
 * @returns 分页组件
 */
function renderPagination(currentPage: number, totalPages: number, locale: string) {
  if (totalPages <= 1) {
    return null;
  }

  return (
    <div className="mt-12">
      <Pagination>
        <PaginationContent>
          {currentPage > 1 && (
            <PaginationItem>
              <PaginationPrevious href={`/${locale}/tools?page=${currentPage - 1}`} />
            </PaginationItem>
          )}

          {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
            <PaginationItem key={page}>
              <PaginationLink
                href={`/${locale}/tools?page=${page}`}
                isActive={page === currentPage}
              >
                {page}
              </PaginationLink>
            </PaginationItem>
          ))}

          {currentPage < totalPages && (
            <PaginationItem>
              <PaginationNext href={`/${locale}/tools?page=${currentPage + 1}`} />
            </PaginationItem>
          )}
        </PaginationContent>
      </Pagination>
    </div>
  );
}