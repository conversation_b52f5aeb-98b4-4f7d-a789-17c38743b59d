import { render } from '@testing-library/react';
import RootLayout from '@/app/layout';

// Mock Next.js Script component
jest.mock('next/script', () => {
  return function MockScript({ children, src, id, strategy }: any) {
    if (src) {
      return <script data-testid={`script-${src}`} data-strategy={strategy} />;
    }
    if (id) {
      return <script data-testid={`script-${id}`} data-strategy={strategy}>{children}</script>;
    }
    return <script>{children}</script>;
  };
});

// Mock components
jest.mock('@/components/client-scripts', () => {
  return function MockClientScripts() {
    return <div data-testid="client-scripts" />;
  };
});

jest.mock('@/components/monitoring/performance-monitor', () => {
  return function MockPerformanceMonitor() {
    return <div data-testid="performance-monitor" />;
  };
});

describe('Google Analytics Integration', () => {
  const originalEnv = process.env.NODE_ENV;

  afterEach(() => {
    process.env.NODE_ENV = originalEnv;
  });

  it('should include Google Analytics scripts in production', () => {
    process.env.NODE_ENV = 'production';
    
    const { getByTestId } = render(
      <RootLayout>
        <div>Test content</div>
      </RootLayout>
    );

    // 检查 Google Analytics 脚本是否存在
    expect(getByTestId('script-https://www.googletagmanager.com/gtag/js?id=G-PR6E12G0V0')).toBeInTheDocument();
    expect(getByTestId('script-google-analytics')).toBeInTheDocument();
    
    // 检查策略是否正确
    expect(getByTestId('script-https://www.googletagmanager.com/gtag/js?id=G-PR6E12G0V0')).toHaveAttribute('data-strategy', 'afterInteractive');
    expect(getByTestId('script-google-analytics')).toHaveAttribute('data-strategy', 'afterInteractive');
  });

  it('should not include Google Analytics scripts in development', () => {
    process.env.NODE_ENV = 'development';
    
    const { queryByTestId } = render(
      <RootLayout>
        <div>Test content</div>
      </RootLayout>
    );

    // 检查 Google Analytics 脚本不存在
    expect(queryByTestId('script-https://www.googletagmanager.com/gtag/js?id=G-PR6E12G0V0')).not.toBeInTheDocument();
    expect(queryByTestId('script-google-analytics')).not.toBeInTheDocument();
  });

  it('should include correct Google Analytics configuration', () => {
    process.env.NODE_ENV = 'production';
    
    const { getByTestId } = render(
      <RootLayout>
        <div>Test content</div>
      </RootLayout>
    );

    const analyticsScript = getByTestId('script-google-analytics');
    expect(analyticsScript.textContent).toContain('G-PR6E12G0V0');
    expect(analyticsScript.textContent).toContain('window.dataLayer = window.dataLayer || []');
    expect(analyticsScript.textContent).toContain('function gtag(){dataLayer.push(arguments);}');
    expect(analyticsScript.textContent).toContain("gtag('config', 'G-PR6E12G0V0')");
  });
});
