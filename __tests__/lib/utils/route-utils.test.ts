import {
  buildLocalizedPath,
  buildPaginationPath,
  buildFilterPath,
  buildToolPath,
  buildCategoryPath,
  buildArticlePath,
  buildSearchPath,
  buildLanguageSwitchPath
} from '@/lib/utils/route-utils';

describe('Route Utils', () => {
  describe('buildLocalizedPath', () => {
    it('should build a localized path with default locale', () => {
      expect(buildLocalizedPath('tools')).toBe('/en/tools');
    });

    it('should build a localized path with specified locale', () => {
      expect(buildLocalizedPath('tools', 'zh')).toBe('/zh/tools');
    });

    it('should handle paths with leading slash', () => {
      expect(buildLocalizedPath('/tools', 'es')).toBe('/es/tools');
    });
  });

  describe('buildPaginationPath', () => {
    it('should build a pagination path with default locale', () => {
      expect(buildPaginationPath('tools', 2)).toBe('/en/tools?page=2');
    });

    it('should build a pagination path with specified locale', () => {
      expect(buildPaginationPath('tools', 3, 'zh')).toBe('/zh/tools?page=3');
    });

    it('should handle paths with leading slash', () => {
      expect(buildPaginationPath('/tools', 4, 'es')).toBe('/es/tools?page=4');
    });
  });

  describe('buildFilterPath', () => {
    it('should build a filter path with default locale', () => {
      expect(buildFilterPath('tools', { category: 'ai', sort: 'newest' }))
        .toBe('/en/tools?category=ai&sort=newest');
    });

    it('should build a filter path with specified locale', () => {
      expect(buildFilterPath('tools', { featured: true, limit: 10 }, 'zh'))
        .toBe('/zh/tools?featured=true&limit=10');
    });

    it('should handle paths with leading slash', () => {
      expect(buildFilterPath('/tools', { trending: true }, 'es'))
        .toBe('/es/tools?trending=true');
    });

    it('should handle empty filters', () => {
      expect(buildFilterPath('tools', {})).toBe('/en/tools');
    });

    it('should handle filters with undefined or null values', () => {
      expect(buildFilterPath('tools', { category: 'ai' }))
        .toBe('/en/tools?category=ai');
    });
  });

  describe('buildToolPath', () => {
    it('should build a tool path with default locale', () => {
      expect(buildToolPath('tool-123')).toBe('/en/tool/tool-123');
    });

    it('should build a tool path with specified locale', () => {
      expect(buildToolPath('tool-456', 'zh')).toBe('/zh/tool/tool-456');
    });
  });

  describe('buildCategoryPath', () => {
    it('should build a category path with default locale', () => {
      expect(buildCategoryPath('category-123')).toBe('/en/categories/category-123');
    });

    it('should build a category path with specified locale', () => {
      expect(buildCategoryPath('category-456', 'zh')).toBe('/zh/categories/category-456');
    });
  });

  describe('buildArticlePath', () => {
    it('should build an article path with default locale', () => {
      expect(buildArticlePath('article-slug')).toBe('/en/articles/article-slug');
    });

    it('should build an article path with specified locale', () => {
      expect(buildArticlePath('article-slug-zh', 'zh')).toBe('/zh/articles/article-slug-zh');
    });
  });

  describe('buildSearchPath', () => {
    it('should build a search path with default locale', () => {
      expect(buildSearchPath('ai tools')).toBe('/en/search?q=ai%20tools');
    });

    it('should build a search path with specified locale', () => {
      expect(buildSearchPath('ai 工具', 'zh')).toBe('/zh/search?q=ai%20%E5%B7%A5%E5%85%B7');
    });

    it('should properly encode special characters', () => {
      expect(buildSearchPath('ai & ml')).toBe('/en/search?q=ai%20%26%20ml');
    });
  });

  describe('buildLanguageSwitchPath', () => {
    it('should replace the locale in the path', () => {
      expect(buildLanguageSwitchPath('/en/tools', 'zh')).toBe('/zh/tools');
    });

    it('should work with different current locale', () => {
      expect(buildLanguageSwitchPath('/es/tools', 'zh', 'es')).toBe('/zh/tools');
    });

    it('should handle paths with query parameters', () => {
      expect(buildLanguageSwitchPath('/en/tools?page=2', 'zh')).toBe('/zh/tools?page=2');
    });
  });
});
