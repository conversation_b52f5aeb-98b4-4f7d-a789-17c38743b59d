'use client';

import Link from 'next/link';
import { Calendar, Clock, Tag, ArrowLeft, Share2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Article } from '@/lib/types';
import { Dictionary } from '@/lib/i18n/types';
import { Locale } from '@/lib/i18n/config';

interface ArticleContentProps {
  article: Article;
  dictionary: Dictionary;
  locale: Locale;
}

export default function ArticleContent({ article, dictionary, locale }: ArticleContentProps) {
  // 计算阅读时间（基于内容长度的简单估算）
  const readTime = Math.max(1, Math.ceil(article.content.length / 1000));

  // 格式化日期
  const formatDate = (date: Date) => {
    const localeMap: Record<string, string> = {
      'zh': 'zh-CN',
      'es': 'es-ES',
      'en': 'en-US'
    };
    return new Intl.DateTimeFormat(localeMap[locale] || 'en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    }).format(date);
  };

  // 分享功能
  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: article.title,
          text: article.summary,
          url: window.location.href,
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    } else {
      // 复制链接到剪贴板
      navigator.clipboard.writeText(window.location.href);
    }
  };

  return (
    <article className="max-w-none">
      {/* 返回按钮 */}
      <div className="mb-8">
        <Link href={`/${locale}/articles`}>
          <Button variant="ghost" className="pl-0">
            <ArrowLeft className="w-4 h-4 mr-2" />
            {dictionary.articles?.backToArticles || 'Back to Articles'}
          </Button>
        </Link>
      </div>

      {/* 文章头部 */}
      <header className="mb-8">
        <h1 className="text-4xl font-bold mb-4 leading-tight">
          {article.title}
        </h1>

        <p className="text-xl text-muted-foreground mb-6 leading-relaxed">
          {article.summary}
        </p>

        {/* 文章元信息 */}
        <div className="flex flex-wrap items-center gap-6 text-sm text-muted-foreground mb-6">
          <div className="flex items-center">
            <Calendar className="w-4 h-4 mr-2" />
            {dictionary.articles?.publishedOn || 'Published on'} {article.publishedAt ? formatDate(article.publishedAt) : 'Unknown date'}
          </div>
          <div className="flex items-center">
            <Clock className="w-4 h-4 mr-2" />
            {readTime} {dictionary.articles?.readTime || 'min read'}
          </div>

          {/* 分享按钮 */}
          <div className="flex items-center justify-between">
            <div></div>
            <Button variant="outline" size="sm" onClick={handleShare}>
              <Share2 className="w-4 h-4 mr-2" />
              {dictionary.articles?.shareArticle || 'Share Article'}
            </Button>
          </div>
        </div>
        <Separator className="mt-6" />
      </header>

      {/* 文章内容 */}
      <div
        key="content"
        className="prose prose-lg max-w-none dark:prose-invert prose-headings:font-semibold prose-h1:text-3xl prose-h2:text-2xl prose-h3:text-xl prose-p:leading-relaxed prose-a:text-primary prose-a:no-underline hover:prose-a:underline prose-blockquote:border-l-primary prose-blockquote:bg-muted/50 prose-blockquote:py-2 prose-blockquote:px-4 prose-code:bg-muted prose-code:px-1 prose-code:py-0.5 prose-code:rounded prose-pre:bg-muted prose-pre:border"
        dangerouslySetInnerHTML={{ __html: article.content }}
      />

      {article.modules?.map((module, index) => (
        <div key={module.handle} className="mt-2 border-t border-muted/100 pb-6">
          <div className="flex items-center gap-2 mt-8 mb-4">
            <span className="text-3xl font-bold">{index + 1}.</span>
            <h2 className="text-2xl font-semibold">{module.name}</h2>
          </div>
          <p className="text-xl text-muted-foreground mb-4 leading-relaxed">
            <a className='hover:underline' href={module.website?.replace('toolify', 'x114.org')} target="_blank" rel="noopener noreferrer" title='{module.name}'>{module.website?.replace('toolify', 'x114.org')}</a>
          </p>
          <div dangerouslySetInnerHTML={{ __html: module.content?.replaceAll('https://cdn-images.toolify.ai/', 'https://assets.x114.org/') || '' }} />
        </div>
      ))}

      <div
        key="ends_content"
        className="prose prose-lg max-w-none dark:prose-invert prose-headings:font-semibold prose-h1:text-3xl prose-h2:text-2xl prose-h3:text-xl prose-p:leading-relaxed prose-a:text-primary prose-a:no-underline hover:prose-a:underline prose-blockquote:border-l-primary prose-blockquote:bg-muted/50 prose-blockquote:py-2 prose-blockquote:px-4 prose-code:bg-muted prose-code:px-1 prose-code:py-0.5 prose-code:rounded prose-pre:bg-muted prose-pre:border"
        dangerouslySetInnerHTML={{ __html: article.ends_content || '' }}
      />
    </article>
  );
}
