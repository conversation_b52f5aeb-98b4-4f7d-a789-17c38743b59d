import { Metadata } from 'next';
import { getDictionary } from '@/lib/i18n/get-dictionary';
import { Locale, i18n } from '@/lib/i18n/config';
import { getAllArticles } from '@/lib/db/articles';
import ArticleCard from '@/components/articles/article-card';
import { Container } from '@/components/ui/container';
import SectionHeader from '@/components/ui/section-header';
import { TopBannerAd, ContentRectangleAd, InFeedAd } from '@/components/ads/client-ad-wrapper';

// 为静态导出配置
export const dynamic = 'force-static';
export const revalidate = 3600; // 每小时重新验证一次

// 为静态导出生成所有支持的语言路径
export function generateStaticParams() {
  return i18n.locales.map((locale) => ({ locale }));
}

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: Locale }>;
}): Promise<Metadata> {
  const locale = (await params).locale;
  const dictionary = await getDictionary(locale);

  return {
    title: dictionary.seo?.articlesTitle || 'AI Articles & Guides | X114 AI Tool Hub',
    description: dictionary.seo?.articlesDescription || 'Discover insights, tips, and comprehensive guides about AI tools. Learn how to maximize your productivity with the latest AI technologies.',
    keywords: ['AI articles', 'AI guides', 'AI tips', 'AI insights', 'AI tutorials', 'X114'],
  };
}

export default async function ArticlesPage({
  params,
}: {
  params: Promise<{ locale: Locale }>;
}) {
  // 在 Next.js 15+ 中，params 是异步的，需要使用 await
  const locale = (await params).locale;
  const dictionary = await getDictionary(locale);

  // 获取文章数据
  const articles = await getAllArticles(locale);

  return (
    <div className="min-h-screen bg-background">
      <Container className="py-24">
        <div className="text-center mb-12">
          <SectionHeader
            title={dictionary.articles?.title || 'Articles'}
            className="text-center"
          />
          <p className="text-muted-foreground text-lg mt-4">
            {dictionary.articles?.description || 'Discover insights, tips, and guides about AI tools'}
          </p>
        </div>

        {/* 顶部横幅广告 */}
        <TopBannerAd adSlot="1620640133" />

        {articles.length > 0 ? (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {articles.map((article, index) => {
                const items = [
                  <ArticleCard
                    key={article.id}
                    article={article}
                    dictionary={dictionary}
                    locale={locale}
                  />
                ];

                // 在第4个文章后插入信息流广告
                if (index === 3) {
                  items.push(
                    <div key="ad-feed" className="md:col-span-2 lg:col-span-3">
                      <InFeedAd adSlot="2345678901" />
                    </div>
                  );
                }

                return items;
              }).flat()}
            </div>

            {/* 底部内容广告 */}
            <ContentRectangleAd adSlot="4626949563" className="mt-12" />
          </>
        ) : (
          <div className="text-center py-16">
            <h3 className="text-xl font-semibold mb-4">
              {dictionary.articles?.noArticles || 'No articles found'}
            </h3>
            <p className="text-muted-foreground">
              {dictionary.articles?.noArticlesDescription || "We're working on adding more content. Check back soon!"}
            </p>
          </div>
        )}
      </Container>
    </div>
  );
}
