import { Locale, defaultLocale } from '@/lib/i18n/config';

/**
 * 构建本地化路由路径
 * @param path 路径
 * @param locale 语言
 * @returns 本地化路径
 */
export function buildLocalizedPath(path: string, locale: Locale = defaultLocale): string {
  // 移除开头的斜杠
  const cleanPath = path.startsWith('/') ? path.substring(1) : path;
  
  // 构建本地化路径
  return `/${locale}/${cleanPath}`;
}

/**
 * 构建分页路由路径
 * @param basePath 基础路径
 * @param page 页码
 * @param locale 语言
 * @returns 分页路径
 */
export function buildPaginationPath(
  basePath: string, 
  page: number, 
  locale: Locale = defaultLocale
): string {
  // 移除开头和结尾的斜杠
  const cleanPath = basePath
    .startsWith('/') ? basePath.substring(1) : basePath;
  
  // 构建分页路径
  return `/${locale}/${cleanPath}?page=${page}`;
}

/**
 * 构建筛选路由路径
 * @param basePath 基础路径
 * @param filters 筛选条件
 * @param locale 语言
 * @returns 筛选路径
 */
export function buildFilterPath(
  basePath: string, 
  filters: Record<string, string | number | boolean>, 
  locale: Locale = defaultLocale
): string {
  // 移除开头和结尾的斜杠
  const cleanPath = basePath
    .startsWith('/') ? basePath.substring(1) : basePath;
  
  // 构建查询字符串
  const queryParams = new URLSearchParams();
  
  // 添加筛选条件
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      queryParams.append(key, value.toString());
    }
  });
  
  // 构建筛选路径
  const queryString = queryParams.toString();
  return `/${locale}/${cleanPath}${queryString ? `?${queryString}` : ''}`;
}

/**
 * 构建工具详情路由路径
 * @param toolId 工具ID
 * @param locale 语言
 * @returns 工具详情路径
 */
export function buildToolPath(toolId: string, locale: Locale = defaultLocale): string {
  return `/${locale}/tool/${toolId}`;
}

/**
 * 构建类别路由路径
 * @param categoryId 类别ID
 * @param locale 语言
 * @returns 类别路径
 */
export function buildCategoryPath(categoryId: string, locale: Locale = defaultLocale): string {
  return `/${locale}/categories/${categoryId}`;
}

/**
 * 构建文章路由路径
 * @param slug 文章别名
 * @param locale 语言
 * @returns 文章路径
 */
export function buildArticlePath(slug: string, locale: Locale = defaultLocale): string {
  return `/${locale}/articles/${slug}`;
}

/**
 * 构建搜索路由路径
 * @param query 搜索关键词
 * @param locale 语言
 * @returns 搜索路径
 */
export function buildSearchPath(query: string, locale: Locale = defaultLocale): string {
  return `/${locale}/search?q=${encodeURIComponent(query)}`;
}

/**
 * 构建语言切换路由路径
 * @param currentPath 当前路径
 * @param newLocale 新语言
 * @param currentLocale 当前语言
 * @returns 语言切换路径
 */
export function buildLanguageSwitchPath(
  currentPath: string, 
  newLocale: Locale, 
  currentLocale: Locale = defaultLocale
): string {
  // 替换路径中的语言部分
  return currentPath.replace(`/${currentLocale}/`, `/${newLocale}/`);
}
