// SEO检查工具
// 用于验证页面的SEO配置是否符合最佳实践

export interface SeoCheckResult {
  score: number;
  issues: SeoIssue[];
  recommendations: string[];
  passed: SeoCheck[];
}

export interface SeoIssue {
  type: 'error' | 'warning' | 'info';
  message: string;
  element?: string;
  impact: 'high' | 'medium' | 'low';
}

export interface SeoCheck {
  name: string;
  description: string;
}

export class SeoChecker {
  private issues: SeoIssue[] = [];
  private passed: SeoCheck[] = [];

  // 检查页面标题
  checkTitle(title?: string): void {
    if (!title) {
      this.addIssue('error', 'Page title is missing', 'title', 'high');
      return;
    }

    if (title.length < 30) {
      this.addIssue('warning', 'Title is too short (< 30 characters)', 'title', 'medium');
    } else if (title.length > 60) {
      this.addIssue('warning', 'Title is too long (> 60 characters)', 'title', 'medium');
    } else {
      this.addPassed('Title Length', 'Title length is optimal (30-60 characters)');
    }

    if (!title.includes('|') && !title.includes('-')) {
      this.addIssue('info', 'Consider adding brand name to title', 'title', 'low');
    }
  }

  // 检查页面描述
  checkDescription(description?: string): void {
    if (!description) {
      this.addIssue('error', 'Meta description is missing', 'meta[name="description"]', 'high');
      return;
    }

    if (description.length < 120) {
      this.addIssue('warning', 'Description is too short (< 120 characters)', 'meta[name="description"]', 'medium');
    } else if (description.length > 160) {
      this.addIssue('warning', 'Description is too long (> 160 characters)', 'meta[name="description"]', 'medium');
    } else {
      this.addPassed('Description Length', 'Meta description length is optimal (120-160 characters)');
    }
  }

  // 检查关键词
  checkKeywords(keywords?: string[]): void {
    if (!keywords || keywords.length === 0) {
      this.addIssue('warning', 'No keywords specified', 'meta[name="keywords"]', 'low');
      return;
    }

    if (keywords.length > 10) {
      this.addIssue('warning', 'Too many keywords (> 10)', 'meta[name="keywords"]', 'medium');
    } else {
      this.addPassed('Keywords Count', 'Keywords count is reasonable');
    }
  }

  // 检查Open Graph标签
  checkOpenGraph(ogData: {
    title?: string;
    description?: string;
    image?: string;
    url?: string;
    type?: string;
  }): void {
    if (!ogData.title) {
      this.addIssue('error', 'Open Graph title is missing', 'meta[property="og:title"]', 'high');
    } else {
      this.addPassed('OG Title', 'Open Graph title is present');
    }

    if (!ogData.description) {
      this.addIssue('error', 'Open Graph description is missing', 'meta[property="og:description"]', 'high');
    } else {
      this.addPassed('OG Description', 'Open Graph description is present');
    }

    if (!ogData.image) {
      this.addIssue('warning', 'Open Graph image is missing', 'meta[property="og:image"]', 'medium');
    } else {
      this.addPassed('OG Image', 'Open Graph image is present');
    }

    if (!ogData.url) {
      this.addIssue('warning', 'Open Graph URL is missing', 'meta[property="og:url"]', 'medium');
    } else {
      this.addPassed('OG URL', 'Open Graph URL is present');
    }

    if (!ogData.type) {
      this.addIssue('info', 'Open Graph type is missing', 'meta[property="og:type"]', 'low');
    } else {
      this.addPassed('OG Type', 'Open Graph type is present');
    }
  }

  // 检查Twitter Card
  checkTwitterCard(twitterData: {
    card?: string;
    title?: string;
    description?: string;
    image?: string;
  }): void {
    if (!twitterData.card) {
      this.addIssue('warning', 'Twitter card type is missing', 'meta[name="twitter:card"]', 'medium');
    } else {
      this.addPassed('Twitter Card', 'Twitter card type is present');
    }

    if (!twitterData.title) {
      this.addIssue('warning', 'Twitter title is missing', 'meta[name="twitter:title"]', 'medium');
    }

    if (!twitterData.description) {
      this.addIssue('warning', 'Twitter description is missing', 'meta[name="twitter:description"]', 'medium');
    }

    if (!twitterData.image) {
      this.addIssue('info', 'Twitter image is missing', 'meta[name="twitter:image"]', 'low');
    }
  }

  // 检查规范链接
  checkCanonical(canonicalUrl?: string): void {
    if (!canonicalUrl) {
      this.addIssue('error', 'Canonical URL is missing', 'link[rel="canonical"]', 'high');
    } else {
      this.addPassed('Canonical URL', 'Canonical URL is present');
      
      if (!canonicalUrl.startsWith('https://')) {
        this.addIssue('warning', 'Canonical URL should use HTTPS', 'link[rel="canonical"]', 'medium');
      }
    }
  }

  // 检查hreflang标签
  checkHreflang(hreflangUrls?: Record<string, string>): void {
    if (!hreflangUrls || Object.keys(hreflangUrls).length === 0) {
      this.addIssue('warning', 'Hreflang tags are missing', 'link[rel="alternate"]', 'medium');
    } else {
      this.addPassed('Hreflang Tags', 'Hreflang tags are present');
      
      if (!hreflangUrls['x-default']) {
        this.addIssue('info', 'x-default hreflang is missing', 'link[rel="alternate"][hreflang="x-default"]', 'low');
      }
    }
  }

  // 检查结构化数据
  checkStructuredData(hasStructuredData: boolean): void {
    if (!hasStructuredData) {
      this.addIssue('warning', 'Structured data (JSON-LD) is missing', 'script[type="application/ld+json"]', 'medium');
    } else {
      this.addPassed('Structured Data', 'Structured data is present');
    }
  }

  // 检查robots标签
  checkRobots(robotsContent?: string): void {
    if (!robotsContent) {
      this.addIssue('info', 'Robots meta tag is missing', 'meta[name="robots"]', 'low');
    } else {
      this.addPassed('Robots Tag', 'Robots meta tag is present');
      
      if (robotsContent.includes('noindex')) {
        this.addIssue('warning', 'Page is set to noindex', 'meta[name="robots"]', 'high');
      }
      
      if (robotsContent.includes('nofollow')) {
        this.addIssue('warning', 'Page is set to nofollow', 'meta[name="robots"]', 'medium');
      }
    }
  }

  // 添加问题
  private addIssue(type: SeoIssue['type'], message: string, element?: string, impact: SeoIssue['impact'] = 'medium'): void {
    this.issues.push({ type, message, element, impact });
  }

  // 添加通过的检查
  private addPassed(name: string, description: string): void {
    this.passed.push({ name, description });
  }

  // 计算SEO分数
  private calculateScore(): number {
    const totalChecks = this.issues.length + this.passed.length;
    if (totalChecks === 0) return 100;

    const errorWeight = 10;
    const warningWeight = 5;
    const infoWeight = 1;

    const penalties = this.issues.reduce((total, issue) => {
      switch (issue.type) {
        case 'error': return total + errorWeight;
        case 'warning': return total + warningWeight;
        case 'info': return total + infoWeight;
        default: return total;
      }
    }, 0);

    const maxScore = 100;
    const score = Math.max(0, maxScore - penalties);
    return Math.round(score);
  }

  // 生成建议
  private generateRecommendations(): string[] {
    const recommendations: string[] = [];

    const errorCount = this.issues.filter(i => i.type === 'error').length;
    const warningCount = this.issues.filter(i => i.type === 'warning').length;

    if (errorCount > 0) {
      recommendations.push(`Fix ${errorCount} critical SEO error(s) first`);
    }

    if (warningCount > 0) {
      recommendations.push(`Address ${warningCount} SEO warning(s) to improve ranking`);
    }

    if (errorCount === 0 && warningCount === 0) {
      recommendations.push('Great job! Your SEO configuration looks good');
      recommendations.push('Consider monitoring your page performance regularly');
    }

    return recommendations;
  }

  // 执行完整的SEO检查
  checkPage(pageData: {
    title?: string;
    description?: string;
    keywords?: string[];
    canonical?: string;
    openGraph?: any;
    twitterCard?: any;
    hreflang?: Record<string, string>;
    hasStructuredData?: boolean;
    robots?: string;
  }): SeoCheckResult {
    // 重置状态
    this.issues = [];
    this.passed = [];

    // 执行各项检查
    this.checkTitle(pageData.title);
    this.checkDescription(pageData.description);
    this.checkKeywords(pageData.keywords);
    this.checkCanonical(pageData.canonical);
    this.checkOpenGraph(pageData.openGraph || {});
    this.checkTwitterCard(pageData.twitterCard || {});
    this.checkHreflang(pageData.hreflang);
    this.checkStructuredData(pageData.hasStructuredData || false);
    this.checkRobots(pageData.robots);

    return {
      score: this.calculateScore(),
      issues: this.issues,
      recommendations: this.generateRecommendations(),
      passed: this.passed,
    };
  }
}
