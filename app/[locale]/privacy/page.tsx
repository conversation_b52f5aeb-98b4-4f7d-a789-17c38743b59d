import { Metadata } from 'next';
import { getDictionary } from '@/lib/i18n/get-dictionary';
import { Locale, i18n } from '@/lib/i18n/config';
import { Container } from '@/components/ui/container';
import { TopBannerAd, SidebarAd } from '@/components/ads/client-ad-wrapper';

// 为静态导出配置
export const dynamic = 'force-static';
export const revalidate = 3600; // 每小时重新验证一次

// 为静态导出生成所有支持的语言路径
export function generateStaticParams() {
  return i18n.locales.map((locale) => ({ locale }));
}

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: Locale }>;
}): Promise<Metadata> {
  const locale = (await params).locale;
  const dictionary = await getDictionary(locale);

  return {
    title: `${dictionary.privacy?.title || 'Privacy Policy'} | X114 AI Tool Hub`,
    description: dictionary.privacy?.description || 'Learn how we collect, use, and protect your personal information.',
    keywords: ['privacy policy', 'data protection', 'personal information', 'X114'],
  };
}

export default async function PrivacyPage({
  params,
}: {
  params: Promise<{ locale: Locale }>;
}) {
  // 在 Next.js 15+ 中，params 是异步的，需要使用 await
  const locale = (await params).locale;
  const dictionary = await getDictionary(locale);

  const privacy = dictionary.privacy;

  if (!privacy) {
    return (
      <Container className="py-24">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl font-bold mb-8">Privacy Policy</h1>
          <p>Privacy policy content is not available in this language.</p>
        </div>
      </Container>
    );
  }

  return (
    <Container className="py-24">
      {/* 顶部横幅广告 */}
      <TopBannerAd adSlot="1620640133" />

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* 主要内容区域 */}
        <div className="lg:col-span-3">
          <div className="mb-8">
            <h1 className="text-3xl font-bold mb-4">{privacy.title}</h1>
            <p className="text-lg text-muted-foreground mb-2">{privacy.description}</p>
            <p className="text-sm text-muted-foreground">{privacy.lastUpdated}</p>
          </div>

          <div className="space-y-8">
          {/* 信息收集 */}
          <section>
            <h2 className="text-2xl font-semibold mb-4">{privacy.sections.dataCollection.title}</h2>
            <p className="text-muted-foreground leading-relaxed">{privacy.sections.dataCollection.content}</p>
          </section>

          {/* 信息使用 */}
          <section>
            <h2 className="text-2xl font-semibold mb-4">{privacy.sections.dataUsage.title}</h2>
            <p className="text-muted-foreground leading-relaxed">{privacy.sections.dataUsage.content}</p>
          </section>

          {/* 数据安全 */}
          <section>
            <h2 className="text-2xl font-semibold mb-4">{privacy.sections.dataSecurity.title}</h2>
            <p className="text-muted-foreground leading-relaxed">{privacy.sections.dataSecurity.content}</p>
          </section>

          {/* Cookie使用 */}
          <section>
            <h2 className="text-2xl font-semibold mb-4">{privacy.sections.cookies.title}</h2>
            <p className="text-muted-foreground leading-relaxed">{privacy.sections.cookies.content}</p>
          </section>

          {/* 第三方服务 */}
          <section>
            <h2 className="text-2xl font-semibold mb-4">{privacy.sections.thirdParty.title}</h2>
            <p className="text-muted-foreground leading-relaxed">{privacy.sections.thirdParty.content}</p>
          </section>

          {/* 用户权利 */}
          <section>
            <h2 className="text-2xl font-semibold mb-4">{privacy.sections.userRights.title}</h2>
            <p className="text-muted-foreground leading-relaxed">{privacy.sections.userRights.content}</p>
          </section>

          {/* 联系我们 */}
          <section>
            <h2 className="text-2xl font-semibold mb-4">{privacy.sections.contact.title}</h2>
            <p className="text-muted-foreground leading-relaxed">{privacy.sections.contact.content}</p>
          </section>
          </div>
        </div>

        {/* 侧边栏 */}
        <div className="lg:col-span-1">
          <SidebarAd adSlot="7890123456" />
        </div>
      </div>
    </Container>
  );
}
