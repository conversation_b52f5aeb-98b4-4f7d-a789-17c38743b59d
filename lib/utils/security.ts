/**
 * 安全工具函数
 * 用于提供安全相关的工具函数
 */

import { isClientSide } from './environment';

/**
 * 清理 HTML 字符串，防止 XSS 攻击
 * @param html HTML 字符串
 * @returns 清理后的 HTML 字符串
 */
export function sanitizeHtml(html: string): string {
  if (!html) return '';
  
  // 替换 HTML 特殊字符
  return html
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;');
}

/**
 * 验证 URL 是否安全
 * @param url URL 字符串
 * @returns 是否安全
 */
export function isSafeUrl(url: string): boolean {
  if (!url) return false;
  
  try {
    const parsedUrl = new URL(url);
    
    // 检查协议是否安全
    return ['http:', 'https:'].includes(parsedUrl.protocol);
  } catch (error) {
    // URL 解析失败
    return false;
  }
}

/**
 * 生成内容安全策略 (CSP) 头
 * @returns CSP 头值
 */
export function generateCspHeader(): string {
  return [
    // 默认策略
    "default-src 'self'",
    
    // 脚本策略
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.googletagmanager.com https://www.google-analytics.com",
    
    // 样式策略
    "style-src 'self' 'unsafe-inline'",
    
    // 图片策略
    "img-src 'self' data: https:",
    
    // 字体策略
    "font-src 'self' data:",
    
    // 连接策略
    "connect-src 'self' https://www.google-analytics.com",
    
    // 框架策略
    "frame-src 'self'",
    
    // 对象策略
    "object-src 'none'",
    
    // 基础 URI 策略
    "base-uri 'self'",
  ].join('; ');
}

/**
 * 生成安全头
 * @returns 安全头对象
 */
export function generateSecurityHeaders(): Record<string, string> {
  return {
    // 内容安全策略
    'Content-Security-Policy': generateCspHeader(),
    
    // 防止 MIME 类型嗅探
    'X-Content-Type-Options': 'nosniff',
    
    // 防止点击劫持
    'X-Frame-Options': 'SAMEORIGIN',
    
    // 防止 XSS 攻击
    'X-XSS-Protection': '1; mode=block',
    
    // 引用策略
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    
    // 严格传输安全
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
    
    // 权限策略
    'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
  };
}

/**
 * 验证输入是否安全
 * @param input 输入字符串
 * @param pattern 正则表达式模式
 * @returns 是否安全
 */
export function validateInput(input: string, pattern: RegExp): boolean {
  if (!input) return false;
  
  return pattern.test(input);
}

/**
 * 验证电子邮件地址
 * @param email 电子邮件地址
 * @returns 是否有效
 */
export function validateEmail(email: string): boolean {
  const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return validateInput(email, emailPattern);
}

/**
 * 验证密码强度
 * @param password 密码
 * @returns 密码强度（0-4，0 表示最弱，4 表示最强）
 */
export function validatePasswordStrength(password: string): number {
  if (!password) return 0;
  
  let strength = 0;
  
  // 长度检查
  if (password.length >= 8) strength += 1;
  
  // 包含小写字母
  if (/[a-z]/.test(password)) strength += 1;
  
  // 包含大写字母
  if (/[A-Z]/.test(password)) strength += 1;
  
  // 包含数字
  if (/[0-9]/.test(password)) strength += 1;
  
  // 包含特殊字符
  if (/[^a-zA-Z0-9]/.test(password)) strength += 1;
  
  return Math.min(strength, 4);
}

/**
 * 生成随机令牌
 * @param length 令牌长度
 * @returns 随机令牌
 */
export function generateRandomToken(length: number = 32): string {
  if (!isClientSide()) {
    // 服务器端生成
    const crypto = require('crypto');
    return crypto.randomBytes(length).toString('hex').slice(0, length);
  } else {
    // 客户端生成
    const array = new Uint8Array(length);
    window.crypto.getRandomValues(array);
    return Array.from(array, (byte) => byte.toString(16).padStart(2, '0')).join('').slice(0, length);
  }
}

/**
 * 检查是否存在常见的安全漏洞
 * @returns 漏洞列表
 */
export function checkSecurityVulnerabilities(): string[] {
  if (!isClientSide()) return [];
  
  const vulnerabilities: string[] = [];
  
  // 检查是否使用 HTTPS
  if (window.location.protocol !== 'https:') {
    vulnerabilities.push('Not using HTTPS');
  }
  
  // 检查是否存在不安全的第三方脚本
  const scripts = document.querySelectorAll('script[src]');
  scripts.forEach((script) => {
    const src = script.getAttribute('src');
    if (src && !isSafeUrl(src)) {
      vulnerabilities.push(`Unsafe script: ${src}`);
    }
  });
  
  // 检查是否存在不安全的表单
  const forms = document.querySelectorAll('form');
  forms.forEach((form) => {
    const action = form.getAttribute('action');
    if (action && !isSafeUrl(action)) {
      vulnerabilities.push(`Unsafe form action: ${action}`);
    }
    
    if (form.getAttribute('method')?.toLowerCase() !== 'post') {
      vulnerabilities.push('Form using GET method instead of POST');
    }
  });
  
  return vulnerabilities;
}

/**
 * 检查是否存在 localStorage 中的敏感数据
 * @param sensitiveKeys 敏感键列表
 * @returns 是否存在敏感数据
 */
export function checkLocalStorageSensitiveData(sensitiveKeys: string[] = ['password', 'token', 'secret', 'key']): boolean {
  if (!isClientSide()) return false;
  
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (!key) continue;
    
    // 检查键名是否包含敏感词
    if (sensitiveKeys.some((sensitiveKey) => key.toLowerCase().includes(sensitiveKey.toLowerCase()))) {
      return true;
    }
    
    // 检查值是否包含敏感词
    const value = localStorage.getItem(key);
    if (value && sensitiveKeys.some((sensitiveKey) => value.toLowerCase().includes(sensitiveKey.toLowerCase()))) {
      return true;
    }
  }
  
  return false;
}
