'use client';

import React from 'react';
import ErrorBoundary from './error-boundary';
import PerformanceMonitor from './performance-monitor';
import { PerformanceMetrics, ErrorInfo } from '@/lib/utils/monitoring';
import { isDevelopment } from '@/lib/utils/environment';

interface MonitoringProviderProps {
  children: React.ReactNode;
  onMetricsCollected?: (metrics: PerformanceMetrics) => void;
  onError?: (errorInfo: ErrorInfo) => void;
  showDebugPanel?: boolean;
  errorFallback?: React.ReactNode;
}

/**
 * 监控提供者组件
 * 用于在应用中提供监控功能
 */
export default function MonitoringProvider({
  children,
  onMetricsCollected,
  onError,
  showDebugPanel = isDevelopment(),
  errorFallback
}: MonitoringProviderProps) {
  return (
    <ErrorBoundary onError={onError} fallback={errorFallback}>
      {children}
      <PerformanceMonitor
        onMetricsCollected={onMetricsCollected}
        onError={onError}
        showDebugPanel={showDebugPanel}
      />
    </ErrorBoundary>
  );
}
