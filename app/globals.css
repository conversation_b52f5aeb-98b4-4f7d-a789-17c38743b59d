@tailwind base;
@tailwind components;
@tailwind utilities;

/* 广告位样式 */
.ad-container {
  @apply w-full overflow-hidden;
  transition: opacity 0.3s ease-in-out;
}

.ad-position-sidebar {
  @apply sticky top-4;
}

.ad-position-content-top,
.ad-position-content-middle,
.ad-position-content-bottom {
  @apply my-6;
}

.ad-position-between-items {
  @apply border-y border-border/20 bg-muted/10 py-4;
}

.responsive-ad {
  @apply w-full max-w-full;
}

.ad-loading {
  @apply animate-pulse bg-muted/50 rounded;
}

.ad-placeholder {
  @apply border-2 border-dashed border-muted-foreground/30 bg-muted/20 text-muted-foreground text-center p-4 rounded;
}

.ad-in-feed {
  @apply border border-border/50 rounded-lg p-4 bg-card/50;
}

.ad-in-article {
  @apply my-8 p-4 border border-border/30 rounded-lg bg-muted/10;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .ad-position-sidebar {
    @apply static;
  }

  .ad-container[data-ad-size="skyscraper"] {
    @apply hidden;
  }
}

/* 平板端优化 */
@media (min-width: 768px) and (max-width: 1024px) {
  .ad-position-sidebar {
    @apply static mb-6;
  }
}

/* 字体配置 */

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  /* Line clamp utilities */
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  /* 移动端优化 */
  .touch-manipulation {
    touch-action: manipulation;
  }

  .scroll-smooth {
    scroll-behavior: smooth;
  }

  /* 安全区域支持 */
  .pb-safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .pt-safe-top {
    padding-top: env(safe-area-inset-top);
  }

  .pl-safe-left {
    padding-left: env(safe-area-inset-left);
  }

  .pr-safe-right {
    padding-right: env(safe-area-inset-right);
  }

  /* 触摸友好的最小尺寸 */
  .min-touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* 减少动画偏好支持 */
  @media (prefers-reduced-motion: reduce) {
    .animate-pulse,
    .animate-spin,
    .animate-in,
    .animate-out {
      animation: none !important;
    }

    .transition-all,
    .transition-colors,
    .transition-transform {
      transition: none !important;
    }
  }

  /* 高对比度模式支持 */
  @media (prefers-contrast: high) {
    .border {
      border-width: 2px;
    }

    .focus\:ring-2:focus {
      --tw-ring-width: 3px;
    }
  }

  /* 移动端特定样式 */
  @media (max-width: 768px) {
    .mobile-padding {
      padding-left: 1rem;
      padding-right: 1rem;
    }

    .mobile-text-size {
      font-size: 16px; /* 防止iOS缩放 */
    }
  }
}
