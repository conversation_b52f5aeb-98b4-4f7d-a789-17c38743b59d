import { Article } from '@/lib/types';
import { Dictionary } from '@/lib/i18n/types';
import { Locale } from '@/lib/i18n/config';
import SectionHeader from '@/components/ui/section-header';
import ArticleCard from './article-card';

interface RelatedArticlesProps {
  articles: Article[];
  dictionary: Dictionary;
  locale: Locale;
}

export default function RelatedArticles({ articles, dictionary, locale }: RelatedArticlesProps) {
  if (articles.length === 0) {
    return null;
  }

  return (
    <section>
      <SectionHeader
        title={dictionary.articles?.relatedArticles || 'Related Articles'}
        className="mb-8"
      />

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {articles.map((article) => (
          <ArticleCard
            key={article.id}
            article={article}
            dictionary={dictionary}
            locale={locale}
          />
        ))}
      </div>
    </section>
  );
}
