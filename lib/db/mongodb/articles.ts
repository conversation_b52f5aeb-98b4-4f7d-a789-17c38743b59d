import { ObjectId } from 'mongodb';
import { Article } from '../../types';
import { connectToDatabase, COLLECTIONS } from '../mongodb';
import {
  convertArticleDocument,
  convertDocumentArray
} from './utils';

// 获取所有文章
export async function getAllArticlesFromMongoDB(language?: string): Promise<Article[]> {
  try {
    const db = await connectToDatabase();
    const collection = db.collection(COLLECTIONS.ARTICLES);

    // 创建索引以提高查询性能
    await collection.createIndex({ language: 1 });
    await collection.createIndex({ id: 1 });
    await collection.createIndex({ slug: 1 });
    await collection.createIndex({ tags: 1 });
    await collection.createIndex({ category: 1 });
    await collection.createIndex({ published: 1 });
    await collection.createIndex({ publishedAt: -1 });

    // 构建查询条件，如果指定了语言，则按语言筛选
    // 支持 status: 2 表示已发布的文章
    const query: any = {
      $or: [
        { published: true },
        { status: 2 }
      ]
    };
    if (language) {
      query.language = language;
    }

    const articles = await collection.find(query)
      .sort({ published: -1, created_at: -1 })
      .toArray();

    // 使用通用转换函数处理文档
    return convertDocumentArray(articles, convertArticleDocument);
  } catch (error) {
    console.error('Error fetching articles from MongoDB:', error);
    throw error;
  }
}

// 根据过滤条件获取文章
export async function getArticlesFromMongoDB(filters?: {
  category?: string;
  featured?: boolean;
  tags?: string[];
  sort?: 'newest' | 'popular';
  limit?: number;
  offset?: number;
  language?: string;
}): Promise<Article[]> {
  try {
    const db = await connectToDatabase();
    const collection = db.collection(COLLECTIONS.ARTICLES);

    // 构建查询条件 - 支持 status: 2 表示已发布的文章
    const query: any = {
      $or: [
        { published: true },
        { status: 2 }
      ]
    };

    if (filters) {
      if (filters.category) {
        query.category = filters.category;
      }

      if (filters.featured) {
        query.featured = true;
      }

      if (filters.tags && filters.tags.length > 0) {
        query.tags = { $in: filters.tags };
      }

      // 按语言筛选
      if (filters.language) {
        query.language = filters.language;
      }
    }

    // 构建排序条件
    let sortOptions = {};

    if (filters?.sort) {
      switch (filters.sort) {
        case 'newest':
          sortOptions = { published: -1, created_at: -1 };
          break;
        case 'popular':
          sortOptions = { viewCount: -1, published: -1 }; // 假设有 viewCount 字段
          break;
      }
    } else {
      // 默认按发布日期降序排序
      sortOptions = { published: -1, created_at: -1 };
    }

    // 执行查询
    let cursor = collection.find(query).sort(sortOptions);

    // 应用分页
    if (filters?.offset !== undefined) {
      cursor = cursor.skip(filters.offset);
    }

    if (filters?.limit !== undefined) {
      cursor = cursor.limit(filters.limit);
    }

    const articles = await cursor.toArray();

    // 使用通用转换函数处理文档
    return convertDocumentArray(articles, convertArticleDocument);
  } catch (error) {
    console.error('Error fetching filtered articles from MongoDB:', error);
    throw error;
  }
}

// 根据 ID 获取文章
export async function getArticleByIdFromMongoDB(id: string, language?: string): Promise<Article | null> {
  try {
    const db = await connectToDatabase();
    const collection = db.collection(COLLECTIONS.ARTICLES);

    // 自定义查找函数，添加已发布条件
    const findArticleById = async (collection: any, id: string, language?: string) => {
      // 构建发布状态查询
      const publishedQuery = {
        $or: [
          { published: true },
          { status: 2 }
        ]
      };

      // 尝试使用 id 字段查找
      let query: any = { id, ...publishedQuery };
      if (language) query.language = language;

      let article = await collection.findOne(query);

      // 如果没有找到，尝试使用 _id 查找
      if (!article && ObjectId.isValid(id)) {
        query = { _id: new ObjectId(id), ...publishedQuery };
        if (language) query.language = language;
        article = await collection.findOne(query);
      }

      // 如果指定了语言但仍未找到，尝试查找任何语言的文章
      if (!article && language) {
        if (ObjectId.isValid(id)) {
          article = await collection.findOne({ _id: new ObjectId(id), ...publishedQuery });
        }
        if (!article) {
          article = await collection.findOne({ id, ...publishedQuery });
        }
      }

      return article ? convertArticleDocument(article) : null;
    };

    return await findArticleById(collection, id, language);
  } catch (error) {
    console.error(`Error fetching article with id ${id} from MongoDB:`, error);
    throw error;
  }
}

// 根据 Slug 获取文章
export async function getArticleBySlugFromMongoDB(slug: string, language?: string): Promise<Article | null> {
  try {
    const db = await connectToDatabase();
    const collection = db.collection(COLLECTIONS.ARTICLES);

    // 构建查询条件 - 支持 status: 2 表示已发布的文章
    let query: any = {
      slug,
      $or: [
        { published: true },
        { status: 2 }
      ]
    };

    // 如果指定了语言，则添加语言筛选
    if (language) {
      query.language = language;
    }

    // 查找文章
    const article = await collection.findOne(query);

    // 如果指定了语言但仍未找到，尝试查找任何语言的文章
    if (!article && language) {
      const anyLanguageArticle = await collection.findOne({
        slug,
        $or: [
          { published: true },
          { status: 2 }
        ]
      });
      if (anyLanguageArticle) {
        return convertArticleDocument(anyLanguageArticle);
      }
    }

    // 使用通用转换函数处理文档
    return article ? convertArticleDocument(article) : null;
  } catch (error) {
    console.error(`Error fetching article with slug ${slug} from MongoDB:`, error);
    throw error;
  }
}

// 创建新文章
export async function createArticleInMongoDB(article: Omit<Article, '_id'>): Promise<Article> {
  try {
    const db = await connectToDatabase();
    const collection = db.collection(COLLECTIONS.ARTICLES);

    const result = await collection.insertOne(article);

    // 使用通用转换函数处理文档
    return convertArticleDocument({
      ...article,
      _id: result.insertedId
    });
  } catch (error) {
    console.error('Error creating article in MongoDB:', error);
    throw error;
  }
}

// 更新文章
export async function updateArticleInMongoDB(id: string, article: Partial<Article>): Promise<Article | null> {
  try {
    const db = await connectToDatabase();
    const collection = db.collection(COLLECTIONS.ARTICLES);

    // 尝试使用 id 字段更新
    let query: any = { id };

    // 如果没有找到，尝试使用 _id 更新（如果 id 是有效的 ObjectId）
    if ((await collection.countDocuments(query)) === 0 && ObjectId.isValid(id)) {
      query = { _id: new ObjectId(id) };
    }

    const result = await collection.findOneAndUpdate(
      query,
      { $set: article },
      { returnDocument: 'after' }
    );

    // 使用通用转换函数处理文档
    return convertArticleDocument(result);
  } catch (error) {
    console.error(`Error updating article with id ${id} in MongoDB:`, error);
    throw error;
  }
}

// 删除文章
export async function deleteArticleFromMongoDB(id: string): Promise<boolean> {
  try {
    const db = await connectToDatabase();
    const collection = db.collection(COLLECTIONS.ARTICLES);

    // 尝试使用 id 字段删除
    let result = await collection.deleteOne({ id });

    // 如果没有删除任何文档，尝试使用 _id 删除（如果 id 是有效的 ObjectId）
    if (result.deletedCount === 0 && ObjectId.isValid(id)) {
      result = await collection.deleteOne({ _id: new ObjectId(id) });
    }

    return result.deletedCount > 0;
  } catch (error) {
    console.error(`Error deleting article with id ${id} from MongoDB:`, error);
    throw error;
  }
}
