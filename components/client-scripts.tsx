'use client';

import { useEffect } from 'react';
import { setupAdErrorHandling, setupAdVisibilityTracking } from '@/components/ads';
import dynamic from 'next/dynamic';

// 动态导入广告性能监控组件，避免 SSR 问题
const AdPerformanceMonitor = dynamic(
  () => import('@/components/ads/ad-performance-monitor'),
  { ssr: false }
);

/**
 * 客户端脚本组件
 *
 * 处理需要在客户端运行的脚本，如Service Worker注册、广告监控等
 */
export default function ClientScripts() {
  useEffect(() => {
    // 注册 Service Worker
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', function() {
        navigator.serviceWorker.register('/sw.js')
          .then(function(registration) {
            console.log('SW registered: ', registration);
          })
          .catch(function(registrationError) {
            console.log('SW registration failed: ', registrationError);
          });
      });
    }

    // 设置广告错误处理
    setupAdErrorHandling();

    // 设置广告可见性跟踪
    const cleanup = setupAdVisibilityTracking();

    return cleanup;
  }, []);

  return (
    <>
      {/* 开发环境显示广告性能监控 */}
      {process.env.NODE_ENV === 'development' && (
        <AdPerformanceMonitor showDebugInfo={true} />
      )}
    </>
  );
}
