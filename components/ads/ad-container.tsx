'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { AdContainerProps } from './core/ad-types';
import { getAdContainerClasses } from './core/ad-utils';

/**
 * 广告容器组件
 * 提供统一的广告位布局和样式
 */
export default function AdContainer({
  children,
  position,
  className = '',
  spacing = 'md',
  centered = true,
  maxWidth = '100%',
}: AdContainerProps) {
  const containerClasses = getAdContainerClasses(position, spacing, centered);

  return (
    <div 
      className={cn(
        containerClasses,
        'w-full overflow-hidden',
        className
      )}
      style={{ maxWidth }}
      data-ad-position={position}
    >
      {children}
    </div>
  );
}

/**
 * 侧边栏广告容器
 */
export function SidebarAdContainer({ 
  children, 
  className = '',
  ...props 
}: Omit<AdContainerProps, 'position'>) {
  return (
    <AdContainer
      position="sidebar"
      className={cn('sticky top-4', className)}
      spacing="lg"
      {...props}
    >
      {children}
    </AdContainer>
  );
}

/**
 * 内容区域广告容器
 */
export function ContentAdContainer({ 
  children, 
  className = '',
  ...props 
}: Omit<AdContainerProps, 'position'>) {
  return (
    <AdContainer
      position="content-middle"
      className={cn('max-w-4xl mx-auto', className)}
      spacing="xl"
      {...props}
    >
      {children}
    </AdContainer>
  );
}

/**
 * 页眉广告容器
 */
export function HeaderAdContainer({ 
  children, 
  className = '',
  ...props 
}: Omit<AdContainerProps, 'position'>) {
  return (
    <AdContainer
      position="header"
      className={cn('border-b border-border/50', className)}
      spacing="sm"
      {...props}
    >
      {children}
    </AdContainer>
  );
}

/**
 * 页脚广告容器
 */
export function FooterAdContainer({ 
  children, 
  className = '',
  ...props 
}: Omit<AdContainerProps, 'position'>) {
  return (
    <AdContainer
      position="footer"
      className={cn('border-t border-border/50', className)}
      spacing="lg"
      {...props}
    >
      {children}
    </AdContainer>
  );
}

/**
 * 列表项之间的广告容器
 */
export function BetweenItemsAdContainer({ 
  children, 
  className = '',
  ...props 
}: Omit<AdContainerProps, 'position'>) {
  return (
    <AdContainer
      position="between-items"
      className={cn('border-y border-border/30 bg-muted/20', className)}
      spacing="md"
      {...props}
    >
      {children}
    </AdContainer>
  );
}

/**
 * 浮动广告容器
 */
export function FloatingAdContainer({ 
  children, 
  className = '',
  ...props 
}: Omit<AdContainerProps, 'position'>) {
  return (
    <AdContainer
      position="floating"
      className={cn(
        'fixed bottom-4 right-4 z-50',
        'shadow-lg rounded-lg border border-border',
        'bg-background/95 backdrop-blur-sm',
        className
      )}
      spacing="none"
      centered={false}
      {...props}
    >
      {children}
    </AdContainer>
  );
}
