'use client';

import React from 'react';
import { cn } from '@/lib/utils';
import { AdPosition } from './core/ad-types';
import { ResponsiveBannerAd, ResponsiveRectangleAd, ResponsiveSidebarAd } from './responsive-ad';
import { ContentAdContainer, SidebarAdContainer, HeaderAdContainer, FooterAdContainer, BetweenItemsAdContainer } from './ad-container';

/**
 * 页面顶部横幅广告
 */
export function TopBannerAd({
  adSlot,
  className = '',
  ...props
}: {
  adSlot: string;
  className?: string;
  lazy?: boolean;
}) {
  return (
    <HeaderAdContainer className={className}>
      <ResponsiveBannerAd
        adSlot={adSlot}
        position="header"
        {...props}
      />
    </HeaderAdContainer>
  );
}

/**
 * 内容区域中间的方形广告
 */
export function ContentRectangleAd({
  adSlot,
  className = '',
  ...props
}: {
  adSlot: string;
  className?: string;
  lazy?: boolean;
}) {
  return (
    <ContentAdContainer className={className}>
      <ResponsiveRectangleAd
        adSlot={adSlot}
        position="content-middle"
        lazy={true}
        {...props}
      />
    </ContentAdContainer>
  );
}

/**
 * 侧边栏广告
 */
export function SidebarAd({
  adSlot,
  className = '',
  ...props
}: {
  adSlot: string;
  className?: string;
  lazy?: boolean;
}) {
  return (
    <SidebarAdContainer className={className}>
      <ResponsiveSidebarAd
        adSlot={adSlot}
        position="sidebar"
        lazy={true}
        {...props}
      />
    </SidebarAdContainer>
  );
}

/**
 * 页面底部横幅广告
 */
export function BottomBannerAd({
  adSlot,
  className = '',
  ...props
}: {
  adSlot: string;
  className?: string;
  lazy?: boolean;
}) {
  return (
    <FooterAdContainer className={className}>
      <ResponsiveBannerAd
        adSlot={adSlot}
        position="footer"
        lazy={true}
        {...props}
      />
    </FooterAdContainer>
  );
}

/**
 * 列表项之间的广告
 */
export function InFeedAd({
  adSlot,
  className = '',
  ...props
}: {
  adSlot: string;
  className?: string;
  lazy?: boolean;
}) {
  return (
    <BetweenItemsAdContainer className={cn('ad-in-feed', className)}>
      <ResponsiveRectangleAd
        adSlot={adSlot}
        position="between-items"
        lazy={true}
        {...props}
      />
    </BetweenItemsAdContainer>
  );
}

/**
 * 文章内容中的广告
 */
export function InArticleAd({
  adSlot,
  className = '',
  ...props
}: {
  adSlot: string;
  className?: string;
  lazy?: boolean;
}) {
  return (
    <div className={cn('ad-in-article flex justify-center', className)}>
      <ResponsiveRectangleAd
        adSlot={adSlot}
        position="content-middle"
        lazy={true}
        {...props}
      />
    </div>
  );
}

/**
 * 工具详情页面的广告布局
 */
export function ToolPageAdLayout({
  children,
  sidebarAdSlot,
  contentAdSlot,
  className = '',
}: {
  children: React.ReactNode;
  sidebarAdSlot?: string;
  contentAdSlot?: string;
  className?: string;
}) {
  return (
    <div className={cn('grid grid-cols-1 lg:grid-cols-4 gap-8', className)}>
      {/* 主要内容区域 */}
      <div className="lg:col-span-3">
        {children}
        
        {/* 内容底部广告 */}
        {contentAdSlot && (
          <ContentRectangleAd
            adSlot={contentAdSlot}
            className="mt-8"
          />
        )}
      </div>
      
      {/* 侧边栏 */}
      <div className="lg:col-span-1">
        {sidebarAdSlot && (
          <SidebarAd adSlot={sidebarAdSlot} />
        )}
      </div>
    </div>
  );
}

/**
 * 列表页面的广告布局
 */
export function ListPageAdLayout({
  children,
  topAdSlot,
  sidebarAdSlot,
  inFeedAdSlot,
  className = '',
}: {
  children: React.ReactNode;
  topAdSlot?: string;
  sidebarAdSlot?: string;
  inFeedAdSlot?: string;
  className?: string;
}) {
  return (
    <div className={className}>
      {/* 顶部广告 */}
      {topAdSlot && (
        <TopBannerAd adSlot={topAdSlot} />
      )}
      
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* 主要内容区域 */}
        <div className="lg:col-span-3">
          {children}
        </div>
        
        {/* 侧边栏 */}
        <div className="lg:col-span-1">
          {sidebarAdSlot && (
            <SidebarAd adSlot={sidebarAdSlot} />
          )}
        </div>
      </div>
    </div>
  );
}
