import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { getCategoryBySlug, getAllCategories } from '@/lib/db/categories';
import { getTools } from '@/lib/db/tools';
import { getDictionary } from '@/lib/i18n/get-dictionary';
import { Locale, i18n } from '@/lib/i18n/config';
import ToolsGrid from '@/components/tools/tools-grid';
import { Container } from '@/components/ui/container';
import { ListPageAdLayout } from '@/components/ads/client-ad-wrapper';
import { generateStaticMetadata } from '@/lib/seo-static';
import { CategoryStructuredData } from '@/components/structured-data';
import HreflangTags from '@/components/seo/hreflang-tags';
import CanonicalLink from '@/components/seo/canonical-link';
import DynamicMetadata from '@/components/seo/dynamic-metadata';
import { isStaticExport } from '@/lib/utils/environment';

// 为静态导出配置
export const dynamic = 'force-static';
export const revalidate = 3600; // 每小时重新验证一次

interface CategoryPageProps {
  params: Promise<{
    locale: Locale;
    slug: string;
  }>;
}

// 为静态导出生成所有支持的语言和类别路径
export async function generateStaticParams() {
  // 获取所有支持的语言
  const locales = i18n.locales;
  const params = [];

  // 为每种语言获取类别并生成参数
  for (const locale of locales) {
    const categories = await getAllCategories(locale);

    // 为每个类别和语言组合生成参数
    for (const category of categories) {
      params.push({
        locale: locale,
        slug: category.slug
      });
    }

    // 如果没有类别，至少添加一个默认类别
    if (categories.length === 0) {
      params.push({
        locale: locale,
        slug: 'default'
      });
    }
  }

  return params;
}

export async function generateMetadata({ params }: CategoryPageProps): Promise<Metadata> {
  // 在 Next.js 15+ 中，params 是异步的，需要使用 await
  const { slug, locale } = await params;
  const category = await getCategoryBySlug(slug, locale);
  const dictionary = await getDictionary(locale);

  if (!category) {
    return {
      title: 'Category Not Found | X114 AI Tool Hub',
    };
  }

  const title = `${category.name} - ${dictionary.categories?.title || 'Categories'} | ${dictionary.common?.appName || 'X114'}`;
  const description = category.description || `Explore AI tools in the ${category.name} category.`;

  // 使用 generateStaticMetadata 来确保包含正确的 canonical 和 alternate 链接
  return generateStaticMetadata(
    title,
    description,
    `/category/${slug}`,
    locale
  );
}

export default async function CategoryPage({ params }: CategoryPageProps) {
  // 在 Next.js 15+ 中，params 是异步的，需要使用 await
  const { locale, slug } = await params;
  const dictionary = await getDictionary(locale);

  // 添加调试日志
  console.log(`[Static Data] Loading category ${slug} for language ${locale}`);
  console.log(`[Static Data] Is static export: ${isStaticExport()}`);

  let category = await getCategoryBySlug(slug, locale);

  // 如果找不到类别，使用默认类别
  if (!category) {
    notFound();
  }

  // 获取该类别下的工具
  const tools = await getTools({ category: category.slug }, locale);

  return (
    <Container className="py-8">
      {/* 添加结构化数据 */}
      <CategoryStructuredData category={category} locale={locale} />

      {/* 添加SEO元数据 */}
      <HreflangTags path={`/categories/${category.slug}`} locale={locale} baseUrl="https://x114.org" />
      <CanonicalLink url={`https://x114.org/${locale}/categories/${category.slug}`} />
      <DynamicMetadata
        locale={locale}
        title={`${category.name} - ${dictionary.categories?.title || 'Categories'} | ${dictionary.common?.appName || 'X114'}`}
        description={category.description || `Explore AI tools in the ${category.name} category.`}
        keywords={[category.name, 'AI tools', 'AI category', 'X114']}
        ogTitle={`${category.name} - ${dictionary.categories?.title || 'Categories'}`}
        ogDescription={category.description || `Explore AI tools in the ${category.name} category.`}
      />

      <div className="mb-8 mt-12">
        <h1 className="text-3xl font-bold mb-2">{category.name}</h1>
        {category.description && (
          <p className="text-muted-foreground">{category.description}</p>
        )}
      </div>

      <div className="mb-6">
        <p className="text-muted-foreground">
          {tools.length === 0
            ? 'No tools found in this category'
            : `Found ${tools.length} ${dictionary.categories?.tools || 'tools'}`}
        </p>
      </div>

      <ListPageAdLayout
        topAdSlot="1620640133"
        sidebarAdSlot="7890123456"
        inFeedAdSlot="2345678901"
      >
        {tools.length > 0 && (
          <ToolsGrid
            tools={tools}
            currentPage={1}
            totalPages={1}
            locale={locale}
            dictionary={dictionary}
          />
        )}

        {tools.length === 0 && (
          <div className="text-center py-12">
            <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-muted mb-4">
              <span className="text-2xl">{category.icon || '🔍'}</span>
            </div>
            <h2 className="text-xl font-semibold mb-2">
              No tools found in this category
            </h2>
            <p className="text-muted-foreground max-w-md mx-auto">
              Check back later for new tools in this category.
            </p>
          </div>
        )}
      </ListPageAdLayout>
    </Container>
  );
}
