import React from 'react';
import { render, screen } from '@testing-library/react';
import ToolCard from '@/components/tools/tool-card';

// Mock tool data
const mockTool = {
  id: 'test-tool',
  slug: 'test-tool',
  name: 'Test Tool',
  description: 'This is a test tool description',
  image: '/images/test-tool.jpg',
  url: 'https://test-tool.com',
  rating: 4.5,
  reviewCount: 100,
  tags: ['ai', 'test'],
  featured: false,
  trending: true,
  isNew: false,
  createdAt: '2023-01-01',
  language: 'en'
};

describe('ToolCard', () => {
  it('renders the tool name', () => {
    render(<ToolCard tool={mockTool} locale="en" />);
    expect(screen.getByText('Test Tool')).toBeInTheDocument();
  });

  it('renders the tool description', () => {
    render(<ToolCard tool={mockTool} locale="en" />);
    expect(screen.getByText('This is a test tool description')).toBeInTheDocument();
  });



  it('renders the tool rating', () => {
    render(<ToolCard tool={mockTool} locale="en" />);
    expect(screen.getByText('4.5')).toBeInTheDocument();
  });

  it('renders the tool review count', () => {
    render(<ToolCard tool={mockTool} locale="en" />);
    expect(screen.getByText('(100)')).toBeInTheDocument();
  });

  it('renders the "View Details" button by default', () => {
    render(<ToolCard tool={mockTool} locale="en" dictionary={{}} />);
    expect(screen.getByText('View Details')).toBeInTheDocument();
  });

  it('does not render the "View Details" button when showViewDetails is false', () => {
    render(<ToolCard tool={mockTool} locale="en" dictionary={{}} showViewDetails={false} />);
    expect(screen.queryByText('View Details')).not.toBeInTheDocument();
  });

  it('renders the time ago when showTimeAgo is true', () => {
    // Mock Date.now to return a fixed date
    const originalDateNow = Date.now;
    Date.now = jest.fn(() => new Date('2023-01-10').getTime());

    render(<ToolCard tool={mockTool} locale="en" dictionary={{}} showTimeAgo={true} />);

    // Restore original Date.now
    Date.now = originalDateNow;

    // Check if the time ago text is rendered
    expect(screen.getByText(/d ago/)).toBeInTheDocument();
  });

  it('does not render the time ago when showTimeAgo is false', () => {
    render(<ToolCard tool={mockTool} locale="en" dictionary={{}} showTimeAgo={false} />);
    expect(screen.queryByText(/d ago/)).not.toBeInTheDocument();
  });

  it('renders the trending badge for trending tools', () => {
    render(<ToolCard tool={mockTool} locale="en" dictionary={{}} />);
    expect(screen.getByText('Trending')).toBeInTheDocument();
  });

  it('renders the featured badge for featured tools', () => {
    const featuredTool = { ...mockTool, featured: true, trending: false };
    render(<ToolCard tool={featuredTool} locale="en" dictionary={{}} />);
    expect(screen.getByText('Featured')).toBeInTheDocument();
  });

  it('renders the new badge for new tools', () => {
    const newTool = { ...mockTool, featured: false, trending: false, isNew: true };
    render(<ToolCard tool={newTool} locale="en" dictionary={{}} />);
    expect(screen.getByText('New')).toBeInTheDocument();
  });

  it('renders the tool image', () => {
    render(<ToolCard tool={mockTool} locale="en" dictionary={{}} />);
    const image = screen.getByAltText('Test Tool');
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute('src', expect.stringContaining('test-tool.jpg'));
  });

  it('renders the tool website link', () => {
    render(<ToolCard tool={mockTool} locale="en" dictionary={{}} />);
    const link = screen.getByRole('link', { name: '' }); // The arrow icon link
    expect(link).toHaveAttribute('href', 'https://test-tool.com');
    expect(link).toHaveAttribute('target', '_blank');
    expect(link).toHaveAttribute('rel', 'noopener noreferrer');
  });

  it('renders tooltips when showTooltips is true', () => {
    const { container } = render(<ToolCard tool={mockTool} locale="en" dictionary={{}} showTooltips={true} />);
    // Check for tooltip trigger elements (buttons with data-state attribute)
    const tooltipTriggers = container.querySelectorAll('[data-state="closed"]');
    expect(tooltipTriggers.length).toBeGreaterThan(0);
  });

  it('does not render tooltips when showTooltips is false', () => {
    const { container } = render(<ToolCard tool={mockTool} locale="en" dictionary={{}} showTooltips={false} />);
    // When tooltips are disabled, buttons should not have tooltip wrapper
    const tooltipTriggers = container.querySelectorAll('[data-state="closed"]');
    expect(tooltipTriggers.length).toBe(0);
  });

  it('applies custom className', () => {
    const { container } = render(
      <ToolCard tool={mockTool} locale="en" dictionary={{}} className="custom-class" />
    );

    const cardElement = container.firstChild;
    expect(cardElement).toHaveClass('custom-class');
  });
});
