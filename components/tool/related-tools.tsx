'use client';

import Link from 'next/link';
import { Tool } from '@/lib/types';
import { Locale } from '@/lib/i18n/config';
import { Button } from '@/components/ui/button';
import { ArrowRight } from 'lucide-react';
import ToolCard from '@/components/tools/tool-card';

interface RelatedToolsProps {
  tools: Tool[];
  dictionary: any;
  locale: Locale;
}

export default function RelatedTools({ tools, dictionary, locale }: RelatedToolsProps) {
  if (tools.length === 0) {
    return null;
  }

  return (
    <div className="my-12">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold">{dictionary.tool.similarTools}</h2>
        <Button variant="ghost" size="sm" className="gap-1" asChild>
          <Link href={`/${locale}/tools`}>
            {dictionary.tool?.viewAllTools || 'View all tools'}
            <ArrowRight className="h-4 w-4" />
          </Link>
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {tools.map((tool) => (
          <ToolCard
            key={tool.id}
            tool={tool}
            locale={locale}
            dictionary={dictionary}
            showTimeAgo={false}
            showRating={true}
            showViewDetails={true}
            showTooltips={true}
            className="h-full"
          />
        ))}
      </div>
    </div>
  );
}

