import Link from 'next/link';
import { ChevronRight } from 'lucide-react';

export interface SectionHeaderProps {
  title: string;
  viewAllText?: string;
  viewAllLink?: string;
  className?: string;
}

/**
 * 通用的区块标题组件
 * 用于展示区块标题和"查看全部"链接
 */
export default function SectionHeader({
  title,
  viewAllText,
  viewAllLink,
  className = ''
}: SectionHeaderProps) {
  return (
    <div className={`flex flex-wrap items-center justify-between mb-8 ${className}`}>
      <h2 className="text-3xl font-bold">{title}</h2>
      
      {viewAllText && viewAllLink && (
        <Link 
          href={viewAllLink} 
          className="group inline-flex items-center text-blue-600 hover:text-blue-700"
        >
          <span>{viewAllText}</span>
          <ChevronRight className="h-4 w-4 ml-1 transition-transform group-hover:translate-x-0.5" />
        </Link>
      )}
    </div>
  );
}
