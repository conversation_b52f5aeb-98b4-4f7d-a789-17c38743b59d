'use client';

import Link from 'next/link';
import Image from 'next/image';
import { ArrowUpRight, Clock } from 'lucide-react';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { LikeButton, CopyToClipboard, Rating } from '@/components/ui/feedback';
import { AccessibleLink } from '@/components/ui/accessibility';
import { useUserPreferences } from '@/hooks/use-ux-enhancements';
import { Tool } from '@/lib/types';
import { Locale } from '@/lib/i18n/config';
import { cn } from '@/lib/utils';

export interface ToolCardProps {
  tool: Tool;
  locale: string | Locale;
  dictionary?: any;
  showTimeAgo?: boolean;
  showRating?: boolean;
  showViewDetails?: boolean;
  showTooltips?: boolean;
  className?: string;
}

/**
 * 通用工具卡片组件
 * 用于展示工具信息
 */
export default function ToolCard({
  tool,
  locale,
  dictionary,
  showTimeAgo = false,
  showRating = true,
  showViewDetails = true,
  showTooltips = true,
  className = ''
}: ToolCardProps) {
  const { prefersReducedMotion, isMobile } = useUserPreferences();
  const badges = getToolBadge(tool, dictionary);

  // 处理工具点击
  const handleToolClick = () => {
    // 可以在这里添加分析跟踪
    console.log(`Tool clicked: ${tool.name}`);
  };

  // 处理点赞
  const handleLike = (liked: boolean) => {
    console.log(`Tool ${liked ? 'liked' : 'unliked'}: ${tool.name}`);
    // 这里可以调用API更新点赞状态
  };

  // 处理分享
  const handleShare = () => {
    const url = typeof window !== 'undefined'
      ? `${window.location.origin}/${locale}/tool/${tool.slug}`
      : `/${locale}/tool/${tool.slug}`;
    return url;
  };

  const cardClasses = cn(
    'overflow-hidden group min-touch-target',
    'transition-all duration-300 hover:shadow-lg',
    !prefersReducedMotion && 'hover:scale-[1.01]',
    className
  );

  return (
    <Card className={cardClasses} onClick={handleToolClick}>
      <div className="relative h-48 overflow-hidden">
        <Image
          src={tool.image || '/images/placeholder.jpg'}
          alt={`${tool.name} - AI Tool`}
          fill
          style={{ objectFit: 'cover' }}
          loading="lazy"
        />

        {/* 悬停遮罩 */}
        <div className={cn(
          'absolute inset-0 bg-gradient-to-t from-black/70 to-transparent',
          'opacity-0 group-hover:opacity-100 transition-opacity',
          prefersReducedMotion && 'transition-none'
        )} />

        {/* 操作按钮 */}
        <div className={cn(
          'absolute bottom-3 right-3 flex space-x-2',
          'opacity-0 group-hover:opacity-100 transition-opacity',
          prefersReducedMotion && 'transition-none'
        )}>
          {renderActionButtons(tool, showTooltips, handleLike, handleShare, dictionary)}
        </div>

        {/* 徽章 */}
        <div className="absolute top-3 left-3 flex space-x-2">
          {badges.map((badge, index) => (
            <Badge key={index} className={badge.className}>
              {badge.label}
            </Badge>
          ))}
        </div>
      </div>

      <CardContent className="p-4">
        <AccessibleLink
          href={`/${locale}/tool/${tool.slug}`}
          description={`${dictionary?.tool?.viewDetails || 'View details for'} ${tool.name}`}
          className="block"
        >
          <div className="flex items-center gap-2">
            {tool.website_logo && (
              <Image
                src={tool.website_logo}
                alt={`${tool.name} logo`}
                width={24}
                height={24}
                className="rounded-sm"
                loading="lazy"
              />
            )}
            <h3 className={cn(
              'font-semibold text-lg hover:text-primary transition-colors',
              'truncate whitespace-nowrap',
              isMobile && 'text-base'
            )}>
              {tool.name}
            </h3>
          </div>
        </AccessibleLink>
        <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
          {tool.description}
        </p>
      </CardContent>

      <CardFooter className="p-4 pt-0 flex items-center justify-between">
        <div className="flex items-center space-x-3">
          {showTimeAgo && (
            <div className="flex items-center">
              <Clock className="h-3 w-3 text-muted-foreground" />
              <span className="text-xs text-muted-foreground ml-1">
                {Math.floor((Date.now() - new Date(tool.createdAt || Date.now()).getTime()) / (1000 * 60 * 60 * 24))}d {dictionary?.common?.ago || 'ago'}
              </span>
            </div>
          )}

          {showRating && (
            <div className="flex items-center">
              <Rating
                value={tool.rating || 0}
                readonly
                size={isMobile ? 'sm' : 'md'}
              />
              <span className="ml-1 text-sm font-medium">{tool.rating || 0}</span>
              <span className="text-xs text-muted-foreground ml-1">
                ({tool.reviewCount || 0})
              </span>
            </div>
          )}
        </div>

        {showViewDetails && (
          <Button
            variant="outline"
            size={isMobile ? 'sm' : 'default'}
            asChild
            className="min-touch-target"
          >
            <Link href={`/${locale}/tool/${tool.slug}`}>
              {isMobile ? (dictionary?.common?.view || 'View') : (dictionary?.tool?.viewDetails || 'View Details')}
            </Link>
          </Button>
        )}
      </CardFooter>
    </Card>
  );
}

/**
 * 获取工具徽章
 * @param tool 工具对象
 * @param dictionary 语言包
 * @returns 徽章配置数组
 */
function getToolBadge(tool: Tool, dictionary?: any): { label: string; className: string }[] {
  const badges: { label: string; className: string }[] = [];

  if (tool.isNew) {
    badges.push({
      label: dictionary?.tool?.badges?.new || 'New',
      className: 'bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600'
    });
  }

  if (tool.trending) {
    badges.push({
      label: dictionary?.tool?.badges?.trending || 'Trending',
      className: 'bg-gradient-to-r from-pink-500 to-purple-500 hover:from-pink-600 hover:to-purple-600'
    });
  }

  if (tool.featured) {
    badges.push({
      label: dictionary?.tool?.badges?.featured || 'Featured',
      className: 'bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600'
    });
  }

  return badges;
}

/**
 * 渲染工具卡片上的操作按钮
 * @param tool 工具对象
 * @param showTooltips 是否显示工具提示
 * @param onLike 点赞回调
 * @param onShare 分享回调
 * @param dictionary 语言包
 * @returns 操作按钮
 */
function renderActionButtons(
  tool: Tool,
  showTooltips: boolean,
  onLike: (liked: boolean) => void,
  onShare: () => string,
  dictionary?: any
) {
  // 访问按钮
  const visitButton = (
    <Button
      size="icon"
      className="h-8 w-8 rounded-full bg-blue-600/90 hover:bg-blue-600 text-white min-touch-target"
      asChild
    >
      <AccessibleLink
        href={tool.website || tool.url || '#'}
        external
        description={`${dictionary?.tool?.visitWebsite || 'Visit'} ${tool.name} ${dictionary?.common?.website || 'website'}`}
      >
        <ArrowUpRight className="h-4 w-4" />
      </AccessibleLink>
    </Button>
  );

  if (!showTooltips) {
    return (
      <>
        {visitButton}
      </>
    );
  }

  return (
    <>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            {visitButton}
          </TooltipTrigger>
          <TooltipContent>
            <p>{dictionary?.tool?.visitWebsite || 'Visit website'}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </>
  );
}
