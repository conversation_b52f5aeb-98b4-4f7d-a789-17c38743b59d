/**
 * 广告位响应式样式
 * 确保广告在不同设备上的最佳显示效果
 */

/* 广告容器基础样式 */
.ad-container {
  @apply w-full overflow-hidden;
  transition: opacity 0.3s ease-in-out;
}

/* 广告位置相关样式 */
.ad-position-header {
  @apply border-b border-border/30 bg-background/95 backdrop-blur-sm;
}

.ad-position-footer {
  @apply border-t border-border/30 bg-background/95 backdrop-blur-sm;
}

.ad-position-sidebar {
  @apply sticky top-4;
}

.ad-position-content-top,
.ad-position-content-middle,
.ad-position-content-bottom {
  @apply my-6;
}

.ad-position-between-items {
  @apply border-y border-border/20 bg-muted/10 py-4;
}

.ad-position-floating {
  @apply fixed bottom-4 right-4 z-50 shadow-lg rounded-lg border border-border bg-background/95 backdrop-blur-sm;
}

/* 响应式广告样式 */
.responsive-ad {
  @apply w-full max-w-full;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .ad-position-sidebar {
    @apply static;
  }
  
  .ad-position-floating {
    @apply bottom-2 right-2;
  }
  
  /* 移动端隐藏摩天楼广告 */
  .ad-container[data-ad-size="skyscraper"] {
    @apply hidden;
  }
  
  /* 移动端横幅广告调整 */
  .ad-container[data-ad-size="banner"] {
    @apply max-w-full;
  }
  
  .ad-container[data-ad-size="banner"] .adsbygoogle {
    @apply w-full max-w-full;
    min-height: 50px !important;
    height: auto !important;
  }
}

/* 平板端优化 */
@media (min-width: 768px) and (max-width: 1024px) {
  .ad-position-sidebar {
    @apply static mb-6;
  }
  
  /* 平板端摩天楼广告改为方形 */
  .ad-container[data-ad-size="skyscraper"] {
    @apply flex justify-center;
  }
  
  .ad-container[data-ad-size="skyscraper"] .adsbygoogle {
    width: 300px !important;
    height: 250px !important;
  }
}

/* 桌面端优化 */
@media (min-width: 1024px) {
  .ad-position-sidebar {
    @apply sticky top-4;
  }
}

/* 广告加载状态 */
.ad-loading {
  @apply animate-pulse bg-muted/50 rounded;
}

.ad-loading::before {
  content: '';
  @apply block w-full h-full bg-gradient-to-r from-transparent via-muted to-transparent;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* 广告错误状态 */
.ad-error {
  @apply border-2 border-dashed border-destructive/30 bg-destructive/5 text-destructive/70 text-center p-4 rounded;
}

/* 广告占位符样式 */
.ad-placeholder {
  @apply border-2 border-dashed border-muted-foreground/30 bg-muted/20 text-muted-foreground text-center p-4 rounded;
}

/* 信息流广告特殊样式 */
.ad-in-feed {
  @apply border border-border/50 rounded-lg p-4 bg-card/50;
}

.ad-in-feed::before {
  content: 'Advertisement';
  @apply block text-xs text-muted-foreground/60 mb-2 uppercase tracking-wide;
}

/* 文章内广告样式 */
.ad-in-article {
  @apply my-8 p-4 border border-border/30 rounded-lg bg-muted/10;
}

.ad-in-article::before {
  content: 'Advertisement';
  @apply block text-xs text-muted-foreground/60 mb-3 text-center uppercase tracking-wide;
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
  .ad-container {
    @apply border-border/20;
  }
  
  .ad-placeholder {
    @apply border-muted-foreground/20 bg-muted/10 text-muted-foreground/80;
  }
  
  .ad-in-feed,
  .ad-in-article {
    @apply bg-muted/5 border-border/20;
  }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  .ad-container,
  .ad-loading {
    transition: none;
    animation: none;
  }
  
  .ad-loading::before {
    animation: none;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .ad-container {
    @apply border-2 border-foreground/50;
  }
  
  .ad-placeholder {
    @apply border-foreground/50 bg-background text-foreground;
  }
}

/* 打印样式 */
@media print {
  .ad-container,
  .ad-position-floating {
    @apply hidden;
  }
}
