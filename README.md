# X114 AI工具库 - 完整开发文档

X114 是一个基于 Next.js 15 构建的现代化 AI 工具目录平台，支持多语言、SEO 优化和静态站点生成。

## 目录

- [项目概述](#项目概述)
- [技术栈](#技术栈)
- [项目结构](#项目结构)
- [开发指南](#开发指南)
- [部署指南](#部署指南)
- [功能特性](#功能特性)
- [国际化](#国际化)
- [SEO 优化](#seo-优化)
- [性能优化](#性能优化)
- [测试](#测试)
- [贡献指南](#贡献指南)

## 项目概述

X114 AI工具库是一个专注于AI工具展示、分类和搜索的现代化Web应用，具有以下核心特性：

- **响应式设计**：完全适配移动端、平板和桌面端
- **多语言支持**：内置国际化功能，支持英语、西班牙语和中文
- **SEO优化**：内置SEO功能，包括元数据、结构化数据和站点地图
- **静态站点生成**：可导出为静态站点，便于部署到CDN
- **工具目录**：按类别、流行度或最新程度浏览工具
- **搜索功能**：按名称、描述、类别或标签搜索工具
- **暗黑模式**：支持浅色和深色主题切换

## 技术栈

### 前端框架
- **Next.js**: 15.3.2 (App Router)
- **React**: 19.1.0
- **TypeScript**: 5.2.2

### UI 和样式
- **Tailwind CSS**: 3.3.3
- **Radix UI**: 组件库基础
- **shadcn/ui**: 可复用组件
- **Lucide React**: 图标库

### 状态管理和表单
- **React Hooks**: 状态管理
- **React Hook Form**: 7.53.0 + Zod 3.23.8 验证
- **next-themes**: 主题切换

### 国际化和工具
- **next-intl**: 3.11.1 国际化
- **date-fns**: 日期处理
- **clsx**: 类名工具

### 数据库和部署
- **MongoDB**: 6.16.0 (开发环境)
- **静态数据**: 生产环境使用预生成的JSON文件
- **Cloudflare Pages**: 推荐部署平台

### 开发工具
- **ESLint**: 代码规范
- **Jest + React Testing Library**: 测试
- **Bundle Analyzer**: 包分析

## 项目结构

```
x114/
├── app/                    # Next.js App Router
│   ├── [locale]/          # 多语言路由
│   │   ├── layout.tsx     # 布局组件
│   │   ├── page.tsx       # 首页
│   │   ├── tools/         # 工具相关页面
│   │   └── articles/      # 文章页面
│   ├── globals.css        # 全局样式
│   └── layout.tsx         # 根布局
├── components/            # React 组件
│   ├── layout/           # 布局组件
│   ├── sections/         # 页面区块组件
│   ├── seo/              # SEO 相关组件
│   ├── tools/            # 工具相关组件
│   └── ui/               # 基础 UI 组件
├── lib/                  # 工具库
│   ├── db/               # 数据访问层
│   │   ├── mongodb/      # MongoDB 相关
│   │   ├── static-data/  # 静态数据
│   │   └── mongodb-wrapper.ts # 数据访问包装器
│   ├── i18n/             # 国际化
│   │   ├── dictionaries/ # 语言字典
│   │   ├── config.ts     # 配置
│   │   ├── server.ts     # 服务端工具
│   │   └── client.ts     # 客户端工具
│   ├── seo/              # SEO 工具
│   ├── types/            # TypeScript 类型定义
│   └── utils/            # 通用工具函数
├── public/               # 静态资源
│   ├── images/           # 图片资源
│   └── icons/            # 图标文件
├── scripts/              # 构建脚本
│   ├── fetch-static-data.js # 数据获取脚本
│   └── analyze-bundle.js    # 包分析脚本
├── docs/                 # 项目文档
├── next.config.mjs       # Next.js 配置
├── tailwind.config.js    # Tailwind 配置
├── tsconfig.json         # TypeScript 配置
└── package.json          # 项目依赖
```

## 开发指南

### 环境要求

- Node.js 18+
- npm 8+
- MongoDB 6+ (可选，用于开发环境)

### 安装和启动

```bash
# 克隆项目
git clone <repository-url>
cd x114

# 安装依赖
npm install --legacy-peer-dep

# 启动开发服务器
npm run dev
```

### 环境变量

创建 `.env.local` 文件（可选，用于 MongoDB 连接）：

```env
MONGODB_URI=mongodb://localhost:27017
MONGODB_DB=x114
```

### 开发命令

```bash
# 开发服务器
npm run dev

# 构建项目
npm run build

# 启动生产服务器
npm start

# 静态导出
npm run export

# 本地预览静态文件
npm run static-serve

# 代码检查
npm run lint

# 运行测试
npm test

# 获取静态数据
npm run fetch-data

# 包分析
npm run build:analyze
```

## 部署指南

### 静态部署（推荐）

项目配置为静态导出，可部署到任何静态托管服务：

```bash
# 1. 生成静态文件
npm run export

# 2. 部署 out 目录到 Cloudflare Pages
npx wrangler pages deploy
```

### Cloudflare Pages 部署

1. **连接 Git 仓库**：在 Cloudflare Pages 中连接你的 Git 仓库

2. **构建设置**：
   - 构建命令：`npm run export`
   - 输出目录：`out`
   - Node.js 版本：18+

3. **环境变量**（如果需要）：
   - `MONGODB_URI`: MongoDB 连接字符串
   - `MONGODB_DB`: 数据库名称

### 传统部署

如果需要服务端渲染，可以部署到支持 Node.js 的平台：

```bash
npm run build
npm start
```

## 功能特性

### 工具展示
- 工具卡片展示，包含名称、描述、评分等信息
- 按类别、流行度、最新程度筛选
- 响应式网格布局
- 懒加载优化

### 搜索和筛选
- 实时搜索功能
- 多维度筛选（类别、标签、评分等）
- URL 状态同步
- 搜索结果高亮

### 多语言支持
- 支持10种语言：英语(en)、中文简体(zh)、中文繁体(tw)、韩语(ko)、日语(ja)、葡萄牙语(pt)、西班牙语(es)、德语(de)、法语(fr)、越南语(vi)
- 自动语言检测
- URL 路径本地化
- 语言切换组件

### SEO 优化
- 自动生成 meta 标签
- 结构化数据 (JSON-LD)
- 多语言 hreflang 标签
- 自动生成 sitemap
- Open Graph 和 Twitter Cards

## 国际化

### 支持的语言

项目目前支持以下10种语言：

| 语言代码 | 语言名称 | 本地化名称 |
|---------|---------|-----------|
| `en` | 英语 | English |
| `zh` | 中文简体 | 中文简体 |
| `tw` | 中文繁体 | 中文繁體 |
| `ko` | 韩语 | 한국어 |
| `ja` | 日语 | 日本語 |
| `pt` | 葡萄牙语 | Português |
| `es` | 西班牙语 | Español |
| `de` | 德语 | Deutsch |
| `fr` | 法语 | Français |
| `vi` | 越南语 | Tiếng Việt |

### 添加新语言

1. **更新配置**：在 `lib/i18n/config.ts` 中添加新语言

```typescript
export type Locale = 'en' | 'zh' | 'tw' | 'ko' | 'ja' | 'pt' | 'es' | 'de' | 'fr' | 'vi' | 'new-locale';
export const locales: Locale[] = ['en', 'zh', 'tw', 'ko', 'ja', 'pt', 'es', 'de', 'fr', 'vi', 'new-locale'];
```

2. **创建字典文件**：在 `lib/i18n/dictionaries/` 目录中添加新语言的字典文件

3. **更新类型定义**：确保所有翻译键都有对应的类型定义

### 使用翻译

**服务端组件**：
```typescript
import { getTranslations } from '@/lib/i18n/server';

export default async function Page({ params: { locale } }) {
  const t = await getTranslations(locale);
  return <h1>{t.home.title}</h1>;
}
```

**客户端组件**：
```typescript
'use client';
import { useTranslations } from '@/lib/i18n/client';

export default function Component() {
  const t = useTranslations();
  return <h1>{t.home.title}</h1>;
}
```

## SEO 优化

### 页面 SEO 组件

使用统一的 `PageSeo` 组件：

```typescript
import { PageSeo } from '@/components/seo';

export default function Page({ params: { locale } }) {
  return (
    <>
      <PageSeo
        locale={locale}
        title="页面标题"
        description="页面描述"
        path="/current-path"
        keywords={['关键词1', '关键词2']}
        structuredDataType="website"
      />
      {/* 页面内容 */}
    </>
  );
}
```

### 结构化数据

项目自动生成以下类型的结构化数据：
- WebSite
- Organization
- SoftwareApplication (工具页面)
- Article (文章页面)

## 性能优化

### 已实现的优化
- 静态站点生成 (SSG)
- 图片懒加载
- 代码分割
- Tree shaking
- 组件级缓存

### 性能监控
项目内置性能监控组件，可以收集和分析关键性能指标。

## 测试

### 运行测试

```bash
# 运行所有测试
npm test

# 监视模式
npm run test:watch

# 生成覆盖率报告
npm run test:coverage
```

### 测试结构

测试文件位于 `__tests__` 目录中，与源文件结构保持一致。

## 贡献指南

### 代码规范

- 使用 TypeScript 进行类型检查
- 遵循 ESLint 规则
- 组件使用 PascalCase 命名
- 文件使用 kebab-case 命名
- 提交信息遵循 Conventional Commits 规范

### 开发流程

1. Fork 项目
2. 创建功能分支
3. 编写代码和测试
4. 提交 Pull Request

### 分支策略

- `main`: 主分支，保持稳定
- `develop`: 开发分支
- `feature/*`: 功能分支
- `bugfix/*`: 修复分支

---

## 技术支持

如有问题，请：
1. 查看项目文档
2. 搜索已有 Issues
3. 创建新的 Issue
4. 联系项目维护者


## Cloudflare D1 数据库初始化
```bash
 npx wrangler d1 execute x114-prod --remote --file=./migrations/001_create_tool_submissions.sql

```
