'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Tool, Category } from '@/lib/types';
import ToolCard from '@/components/tools/tool-card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Search } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import { TopBannerAd, SidebarAd, InFeedAd } from '@/components/ads';

interface ClientToolsPageProps {
  tools: Tool[];
  categories: Category[];
  dictionary: any;
  locale: string;
}

const ITEMS_PER_PAGE = 12;

export default function ClientToolsPage({
  tools,
  categories,
  dictionary,
  locale
}: ClientToolsPageProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  // 从URL参数读取初始值
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [selectedSort, setSelectedSort] = useState<string>('rating');
  const [currentPage, setCurrentPage] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState<string>('');

  // 在组件挂载后从URL参数初始化状态
  useEffect(() => {
    // 只在初始挂载时从URL参数读取，避免在测试中的循环更新
    const isInitialMount = selectedSort === 'rating' && selectedCategory === '' && searchQuery === '' && currentPage === 1;

    if (isInitialMount) {
      const sort = searchParams.get('sort') || 'rating';
      const category = searchParams.get('category') || '';
      const search = searchParams.get('q') || '';
      const page = parseInt(searchParams.get('page') || '1', 10);



      setSelectedSort(sort);
      setSelectedCategory(category);
      setSearchQuery(search);
      setCurrentPage(page);
    }
  }, []); // 只在挂载时运行一次

  // 筛选和排序工具
  const filteredAndSortedTools = useMemo(() => {
    let filtered = tools;

    // 按搜索查询筛选
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase().trim();
      filtered = filtered.filter(tool =>
        tool.name.toLowerCase().includes(query) ||
        tool.description?.toLowerCase().includes(query) ||
        tool.tags?.some(tag => tag.toLowerCase().includes(query))
      );
    }

    // 按分类筛选
    if (selectedCategory) {
      filtered = filtered.filter(tool =>
        tool.groups?.some(group => group.slug === selectedCategory)
      );
    }

    // 排序
    filtered.sort((a, b) => {
      switch (selectedSort) {
        case 'rating':
          return (b.rating || 0) - (a.rating || 0);
        case 'popular':
          return (b.monthlyVisits || 0) - (a.monthlyVisits || 0);
        case 'newest':
          return new Date(b.createdAt || 0).getTime() - new Date(a.createdAt || 0).getTime();
        default:
          return 0;
      }
    });

    return filtered;
  }, [tools, selectedCategory, selectedSort, searchQuery]);

  // 分页
  const totalPages = Math.ceil(filteredAndSortedTools.length / ITEMS_PER_PAGE);
  const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
  const endIndex = startIndex + ITEMS_PER_PAGE;
  const currentTools = filteredAndSortedTools.slice(startIndex, endIndex);

  // 更新URL参数的函数
  const updateUrlParams = (updates: { [key: string]: string | number | null }) => {
    const params = new URLSearchParams(searchParams);

    Object.entries(updates).forEach(([key, value]) => {
      if (value === null || value === '' || (key === 'page' && value === 1)) {
        params.delete(key);
      } else {
        params.set(key, value.toString());
      }
    });

    const newUrl = `/${locale}/tools${params.toString() ? `?${params.toString()}` : ''}`;
    router.push(newUrl);
  };

  const handleCategoryChange = (category: string) => {
    const newCategory = category === selectedCategory ? '' : category;
    setSelectedCategory(newCategory);
    setCurrentPage(1); // 重置到第一页
    updateUrlParams({ category: newCategory, page: null });
  };

  const handleSortChange = (sort: string) => {
    setSelectedSort(sort);
    setCurrentPage(1); // 重置到第一页
    updateUrlParams({ sort, page: null });
  };

  const handlePageChange = (page: number) => {
    setIsLoading(true);
    setCurrentPage(page);
    updateUrlParams({ page: page === 1 ? null : page });
    // 滚动到顶部
    window.scrollTo({ top: 0, behavior: 'smooth' });
    // 模拟加载延迟以提供更好的用户体验
    setTimeout(() => setIsLoading(false), 100);
  };

  const resetFilters = () => {
    setSelectedCategory('');
    setSelectedSort('rating');
    setSearchQuery('');
    setCurrentPage(1);
    updateUrlParams({ category: null, sort: null, q: null, page: null });
  };

  // 处理搜索查询变化
  const handleSearchChange = (query: string) => {
    setSearchQuery(query);
    setCurrentPage(1); // 重置到第一页
    updateUrlParams({ q: query || null, page: null });
  };

  return (
    <div className="container mx-auto px-4 py-24">
      <div className="max-w-3xl mx-auto text-center mb-12">
        <h1 className="text-4xl font-bold mb-4">
          {dictionary.tools?.title || 'AI Tools Directory'}
        </h1>
        <p className="text-xl text-muted-foreground mb-8">
          {dictionary.tools?.description || 'Discover and compare the best AI tools to enhance your workflow and boost productivity.'}
        </p>

        {/* 搜索框 */}
        <div className="relative max-w-md mx-auto">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            type="text"
            placeholder={dictionary.common?.searchPlaceholder || 'Search AI tools...'}
            value={searchQuery}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="pl-10 pr-4 py-2 w-full"
          />
        </div>
      </div>

      {/* 顶部横幅广告 */}
      <TopBannerAd adSlot="1620640133" />

      <div className="flex flex-col lg:flex-row gap-8">
        {/* 筛选侧边栏 */}
        <div className="lg:w-1/4">
          <div className="rounded-lg border bg-card p-4 lg:p-6 lg:sticky lg:top-24">
            <h2 className="font-semibold text-lg mb-6">
              {dictionary.tools?.filters || 'Filters'}
            </h2>

            <Accordion type="multiple" defaultValue={['categories', 'sort']}>
              <AccordionItem value="categories">
                <AccordionTrigger className="text-sm font-medium">
                  {dictionary.tools?.categories || 'Categories'}
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-3 pt-2">
                    <div
                      className={`px-3 py-2 rounded-md cursor-pointer text-sm ${
                        !selectedCategory ? 'bg-muted font-medium' : 'hover:bg-muted/50'
                      }`}
                      onClick={() => setSelectedCategory('')}
                    >
                      {dictionary.tools?.allCategories || 'All Categories'}
                    </div>
                    {categories.map((category) => (
                      <div
                        key={category.id}
                        className={`px-3 py-2 rounded-md cursor-pointer text-sm flex items-center justify-between ${
                          selectedCategory === category.slug ? 'bg-muted font-medium' : 'hover:bg-muted/50'
                        }`}
                        onClick={() => handleCategoryChange(category.slug)}
                      >
                        <span>{category.name}</span>
                        <span className="text-xs text-muted-foreground">{category.toolCount}</span>
                      </div>
                    ))}
                  </div>
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="sort">
                <AccordionTrigger className="text-sm font-medium">
                  {dictionary.tools?.sortBy || 'Sort By'}
                </AccordionTrigger>
                <AccordionContent>
                  <Select value={selectedSort} onValueChange={handleSortChange}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder={dictionary.tools?.sortBy || 'Sort by'} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="rating">
                        {dictionary.tools?.highestRated || 'Highest Rated'}
                      </SelectItem>
                      <SelectItem value="popular">
                        {dictionary.tools?.mostPopular || 'Most Popular'}
                      </SelectItem>
                      <SelectItem value="newest">
                        {dictionary.tools?.newestFirst || 'Newest First'}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </AccordionContent>
              </AccordionItem>
            </Accordion>

            <Button
              variant="outline"
              className="w-full mt-6"
              onClick={resetFilters}
            >
              {dictionary.tools?.resetFilters || 'Reset Filters'}
            </Button>
          </div>

          {/* 侧边栏广告 */}
          <SidebarAd adSlot="7890123456" className="mt-6" />
        </div>

        {/* 工具网格 */}
        <div className="lg:w-3/4">
          {/* 结果统计 */}
          <div className="mb-6 flex items-center justify-between">
            <div className="text-sm text-muted-foreground">
              {filteredAndSortedTools.length === tools.length
                ? (dictionary.tools?.totalTools?.replace('{total}', tools.length.toString()) || `${tools.length} tools total`)
                : (dictionary.tools?.foundTools
                    ?.replace('{found}', filteredAndSortedTools.length.toString())
                    ?.replace('{total}', tools.length.toString())
                  || `Found ${filteredAndSortedTools.length} of ${tools.length} tools`)
              }
            </div>
            {(selectedCategory || searchQuery.trim()) && (
              <Button
                variant="ghost"
                size="sm"
                onClick={resetFilters}
                className="text-xs"
              >
                {dictionary.tools?.clearFilters || 'Clear Filters'}
              </Button>
            )}
          </div>

          {currentTools.length === 0 ? (
            <div className="py-12 text-center">
              <h3 className="text-xl font-medium mb-2">
                {dictionary.tools?.noToolsFound || 'No tools found'}
              </h3>
              <p className="text-muted-foreground">
                {dictionary.tools?.noToolsFoundDescription || 'Try adjusting your filters or search query.'}
              </p>
            </div>
          ) : (
            <>
              <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 transition-opacity duration-200 ${
                isLoading ? 'opacity-50' : 'opacity-100'
              }`}>
                {currentTools.map((tool, index) => (
                  <React.Fragment key={tool.id}>
                    <ToolCard
                      tool={tool}
                      locale={locale}
                      dictionary={dictionary}
                      showTooltips={false}
                    />
                    {/* 在第6个工具后插入信息流广告 */}
                    {index === 5 && (
                      <div className="md:col-span-2 lg:col-span-3">
                        <InFeedAd adSlot="2345678901" />
                      </div>
                    )}
                  </React.Fragment>
                ))}
              </div>

              {/* 分页 */}
              {totalPages > 1 && (
                <div className="mt-12">
                  <Pagination>
                    <PaginationContent className="flex-wrap justify-center gap-1">
                      {currentPage > 1 && (
                        <PaginationItem>
                          <PaginationPrevious
                            onClick={() => handlePageChange(currentPage - 1)}
                            className="cursor-pointer hover:bg-muted"
                          />
                        </PaginationItem>
                      )}

                      {/* 智能分页显示 */}
                      {(() => {
                        const pages = [];
                        const showEllipsis = totalPages > 7;

                        if (!showEllipsis) {
                          // 如果总页数不超过7页，显示所有页码
                          for (let i = 1; i <= totalPages; i++) {
                            pages.push(i);
                          }
                        } else {
                          // 智能显示页码
                          if (currentPage <= 4) {
                            // 当前页在前面，显示 1,2,3,4,5...last
                            for (let i = 1; i <= 5; i++) {
                              pages.push(i);
                            }
                            pages.push('ellipsis');
                            pages.push(totalPages);
                          } else if (currentPage >= totalPages - 3) {
                            // 当前页在后面，显示 1...last-4,last-3,last-2,last-1,last
                            pages.push(1);
                            pages.push('ellipsis');
                            for (let i = totalPages - 4; i <= totalPages; i++) {
                              pages.push(i);
                            }
                          } else {
                            // 当前页在中间，显示 1...current-1,current,current+1...last
                            pages.push(1);
                            pages.push('ellipsis');
                            for (let i = currentPage - 1; i <= currentPage + 1; i++) {
                              pages.push(i);
                            }
                            pages.push('ellipsis');
                            pages.push(totalPages);
                          }
                        }

                        return pages.map((page, index) => {
                          if (page === 'ellipsis') {
                            return (
                              <PaginationItem key={`ellipsis-${index}`}>
                                <span className="flex h-10 w-10 items-center justify-center text-muted-foreground">
                                  ...
                                </span>
                              </PaginationItem>
                            );
                          }

                          return (
                            <PaginationItem key={page}>
                              <PaginationLink
                                onClick={() => handlePageChange(page as number)}
                                isActive={page === currentPage}
                                className="cursor-pointer hover:bg-muted"
                              >
                                {page}
                              </PaginationLink>
                            </PaginationItem>
                          );
                        });
                      })()}

                      {currentPage < totalPages && (
                        <PaginationItem>
                          <PaginationNext
                            onClick={() => handlePageChange(currentPage + 1)}
                            className="cursor-pointer hover:bg-muted"
                          />
                        </PaginationItem>
                      )}
                    </PaginationContent>
                  </Pagination>

                  {/* 分页信息 */}
                  <div className="text-center mt-4 text-sm text-muted-foreground">
                    {dictionary.tools?.showing
                      ? dictionary.tools.showing
                          .replace('{start}', (startIndex + 1).toString())
                          .replace('{end}', Math.min(endIndex, filteredAndSortedTools.length).toString())
                          .replace('{total}', filteredAndSortedTools.length.toString())
                      : `Showing ${startIndex + 1} - ${Math.min(endIndex, filteredAndSortedTools.length)} of ${filteredAndSortedTools.length} tools`
                    }
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
}
