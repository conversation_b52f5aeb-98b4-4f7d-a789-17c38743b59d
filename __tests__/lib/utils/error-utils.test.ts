import { AppError, ErrorType, handleError, catchAsync } from '@/lib/utils/error-utils';

describe('Error Utils', () => {
  describe('AppError', () => {
    it('should create an error with default values', () => {
      const error = new AppError('Test error');
      expect(error.message).toBe('Test error');
      expect(error.type).toBe(ErrorType.INTERNAL_SERVER);
      expect(error.statusCode).toBe(500);
      expect(error.details).toBeUndefined();
    });

    it('should create an error with custom values', () => {
      const details = { field: 'name', issue: 'required' };
      const error = new AppError('Validation error', ErrorType.VALIDATION, 400, details);
      expect(error.message).toBe('Validation error');
      expect(error.type).toBe(ErrorType.VALIDATION);
      expect(error.statusCode).toBe(400);
      expect(error.details).toEqual(details);
    });

    it('should create a not found error', () => {
      const error = AppError.notFound();
      expect(error.message).toBe('Resource not found');
      expect(error.type).toBe(ErrorType.NOT_FOUND);
      expect(error.statusCode).toBe(404);
    });

    it('should create a not found error with custom message', () => {
      const error = AppError.notFound('Tool not found');
      expect(error.message).toBe('Tool not found');
      expect(error.type).toBe(ErrorType.NOT_FOUND);
      expect(error.statusCode).toBe(404);
    });

    it('should create an unauthorized error', () => {
      const error = AppError.unauthorized();
      expect(error.message).toBe('Unauthorized');
      expect(error.type).toBe(ErrorType.UNAUTHORIZED);
      expect(error.statusCode).toBe(401);
    });

    it('should create a forbidden error', () => {
      const error = AppError.forbidden();
      expect(error.message).toBe('Forbidden');
      expect(error.type).toBe(ErrorType.FORBIDDEN);
      expect(error.statusCode).toBe(403);
    });

    it('should create a bad request error', () => {
      const error = AppError.badRequest();
      expect(error.message).toBe('Bad request');
      expect(error.type).toBe(ErrorType.BAD_REQUEST);
      expect(error.statusCode).toBe(400);
    });

    it('should create an internal server error', () => {
      const error = AppError.internal();
      expect(error.message).toBe('Internal server error');
      expect(error.type).toBe(ErrorType.INTERNAL_SERVER);
      expect(error.statusCode).toBe(500);
    });

    it('should create a database error', () => {
      const error = AppError.database();
      expect(error.message).toBe('Database error');
      expect(error.type).toBe(ErrorType.DATABASE);
      expect(error.statusCode).toBe(500);
    });

    it('should create a network error', () => {
      const error = AppError.network();
      expect(error.message).toBe('Network error');
      expect(error.type).toBe(ErrorType.NETWORK);
      expect(error.statusCode).toBe(500);
    });

    it('should create a validation error', () => {
      const error = AppError.validation();
      expect(error.message).toBe('Validation error');
      expect(error.type).toBe(ErrorType.VALIDATION);
      expect(error.statusCode).toBe(400);
    });
  });

  describe('handleError', () => {
    it('should handle AppError', () => {
      const error = AppError.notFound('Resource not found');
      const result = handleError(error);
      expect(result).toEqual({
        message: 'Resource not found',
        statusCode: 404,
        type: ErrorType.NOT_FOUND
      });
    });

    it('should handle standard Error', () => {
      const error = new Error('Standard error');
      const result = handleError(error);
      expect(result).toEqual({
        message: 'Standard error',
        statusCode: 500,
        type: ErrorType.INTERNAL_SERVER
      });
    });

    it('should handle unknown error', () => {
      const result = handleError('String error');
      expect(result).toEqual({
        message: 'An unknown error occurred',
        statusCode: 500,
        type: ErrorType.INTERNAL_SERVER
      });
    });
  });

  describe('catchAsync', () => {
    it('should return the result of the async function if successful', async () => {
      const asyncFn = async () => 'success';
      const wrappedFn = catchAsync(asyncFn);
      await expect(wrappedFn()).resolves.toBe('success');
    });

    it('should pass through AppError', async () => {
      const asyncFn = async () => {
        throw AppError.notFound('Resource not found');
      };
      const wrappedFn = catchAsync(asyncFn);
      await expect(wrappedFn()).rejects.toEqual(
        expect.objectContaining({
          message: 'Resource not found',
          type: ErrorType.NOT_FOUND,
          statusCode: 404
        })
      );
    });

    it('should convert standard Error to AppError', async () => {
      const asyncFn = async () => {
        throw new Error('Standard error');
      };
      const wrappedFn = catchAsync(asyncFn);
      await expect(wrappedFn()).rejects.toEqual(
        expect.objectContaining({
          message: 'Standard error',
          type: ErrorType.INTERNAL_SERVER,
          statusCode: 500
        })
      );
    });

    it('should convert unknown error to AppError', async () => {
      const asyncFn = async () => {
        throw 'String error';
      };
      const wrappedFn = catchAsync(asyncFn);
      await expect(wrappedFn()).rejects.toEqual(
        expect.objectContaining({
          message: 'An unknown error occurred',
          type: ErrorType.INTERNAL_SERVER,
          statusCode: 500
        })
      );
    });
  });
});
