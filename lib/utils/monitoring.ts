'use client';

/**
 * 性能监控工具
 * 用于监控应用性能和错误
 */

import { useEffect } from 'react';
import { isClientSide, isDevelopment } from './environment';

// 性能指标类型
export interface PerformanceMetrics {
  // 页面加载时间
  pageLoadTime?: number;
  // 首次内容绘制时间
  firstContentfulPaint?: number;
  // 最大内容绘制时间
  largestContentfulPaint?: number;
  // 首次输入延迟
  firstInputDelay?: number;
  // 累积布局偏移
  cumulativeLayoutShift?: number;
  // 首次字节时间
  timeToFirstByte?: number;
  // DOM 内容加载时间
  domContentLoaded?: number;
  // 页面完全加载时间
  loadComplete?: number;
  // 资源加载时间
  resourceLoadTimes?: Record<string, number>;
}

// 错误信息类型
export interface ErrorInfo {
  message: string;
  source?: string;
  lineno?: number;
  colno?: number;
  error?: Error;
  componentStack?: string;
  timestamp: number;
  url: string;
}

// 全局错误处理器
export function setupGlobalErrorHandlers(
  onError?: (errorInfo: ErrorInfo) => void
) {
  if (!isClientSide()) return;

  // 处理未捕获的 JavaScript 错误
  window.addEventListener('error', (event) => {
    const errorInfo: ErrorInfo = {
      message: event.message,
      source: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      error: event.error,
      timestamp: Date.now(),
      url: window.location.href,
    };

    // 如果提供了错误处理回调，则调用它
    if (onError) {
      onError(errorInfo);
    }

    // 在开发环境中打印错误
    if (isDevelopment()) {
      console.error('Uncaught error:', errorInfo);
    }
  });

  // 处理未处理的 Promise 拒绝
  window.addEventListener('unhandledrejection', (event) => {
    const errorInfo: ErrorInfo = {
      message: event.reason?.message || 'Unhandled Promise rejection',
      error: event.reason,
      timestamp: Date.now(),
      url: window.location.href,
    };

    // 如果提供了错误处理回调，则调用它
    if (onError) {
      onError(errorInfo);
    }

    // 在开发环境中打印错误
    if (isDevelopment()) {
      console.error('Unhandled Promise rejection:', errorInfo);
    }
  });
}

// 收集性能指标
export function collectPerformanceMetrics(): PerformanceMetrics {
  if (!isClientSide()) return {};

  const metrics: PerformanceMetrics = {};

  // 使用 Navigation Timing API 收集基本指标
  if (performance && performance.timing) {
    const timing = performance.timing;

    // 页面加载时间
    metrics.pageLoadTime = timing.loadEventEnd - timing.navigationStart;

    // 首次字节时间
    metrics.timeToFirstByte = timing.responseStart - timing.navigationStart;

    // DOM 内容加载时间
    metrics.domContentLoaded = timing.domContentLoadedEventEnd - timing.navigationStart;

    // 页面完全加载时间
    metrics.loadComplete = timing.loadEventEnd - timing.navigationStart;
  }

  // 使用 Performance API 收集更多指标
  if (performance && performance.getEntriesByType) {
    // 收集资源加载时间
    const resourceEntries = performance.getEntriesByType('resource');
    const resourceLoadTimes: Record<string, number> = {};

    resourceEntries.forEach((entry) => {
      resourceLoadTimes[entry.name] = entry.duration;
    });

    metrics.resourceLoadTimes = resourceLoadTimes;

    // 收集绘制指标
    const paintEntries = performance.getEntriesByType('paint');
    paintEntries.forEach((entry) => {
      if (entry.name === 'first-contentful-paint') {
        metrics.firstContentfulPaint = entry.startTime;
      }
    });
  }

  // 使用 Web Vitals API 收集更多指标（如果可用）
  if ('PerformanceObserver' in window) {
    // 最大内容绘制时间
    try {
      const lcpObserver = new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries();
        const lastEntry = entries[entries.length - 1];
        metrics.largestContentfulPaint = lastEntry.startTime;
      });
      lcpObserver.observe({ type: 'largest-contentful-paint', buffered: true });
    } catch (e) {
      console.warn('LCP measurement not supported', e);
    }

    // 累积布局偏移
    try {
      let clsValue = 0;
      const clsObserver = new PerformanceObserver((entryList) => {
        for (const entry of entryList.getEntries()) {
          // 使用类型断言处理 LayoutShift 类型
          const layoutShift = entry as any;
          if (!layoutShift.hadRecentInput) {
            clsValue += layoutShift.value;
          }
        }
        metrics.cumulativeLayoutShift = clsValue;
      });
      clsObserver.observe({ type: 'layout-shift', buffered: true });
    } catch (e) {
      console.warn('CLS measurement not supported', e);
    }

    // 首次输入延迟
    try {
      const fidObserver = new PerformanceObserver((entryList) => {
        const firstInput = entryList.getEntries()[0] as any;
        if (firstInput && firstInput.processingStart) {
          metrics.firstInputDelay = firstInput.processingStart - firstInput.startTime;
        }
      });
      fidObserver.observe({ type: 'first-input', buffered: true });
    } catch (e) {
      console.warn('FID measurement not supported', e);
    }
  }

  return metrics;
}

// 性能监控 Hook
export function usePerformanceMonitoring(
  onMetricsCollected?: (metrics: PerformanceMetrics) => void,
  onError?: (errorInfo: ErrorInfo) => void
) {
  useEffect(() => {
    if (!isClientSide()) return;

    // 设置全局错误处理器
    setupGlobalErrorHandlers(onError);

    // 在页面加载完成后收集性能指标
    const handleLoad = () => {
      // 等待一段时间，确保所有指标都已收集
      setTimeout(() => {
        const metrics = collectPerformanceMetrics();

        // 如果提供了指标收集回调，则调用它
        if (onMetricsCollected) {
          onMetricsCollected(metrics);
        }

        // 在开发环境中打印指标
        if (isDevelopment()) {
          console.log('Performance metrics:', metrics);
        }
      }, 1000);
    };

    // 如果页面已经加载完成，立即收集指标
    if (document.readyState === 'complete') {
      handleLoad();
    } else {
      // 否则，等待页面加载完成
      window.addEventListener('load', handleLoad);
      return () => window.removeEventListener('load', handleLoad);
    }
  }, [onMetricsCollected, onError]);
}
