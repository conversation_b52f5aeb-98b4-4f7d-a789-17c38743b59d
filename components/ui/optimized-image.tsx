'use client';

import Image from 'next/image';
import { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';

interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  priority?: boolean;
  placeholder?: 'blur' | 'empty';
  blurDataURL?: string;
  sizes?: string;
  fill?: boolean;
  quality?: number;
  loading?: 'lazy' | 'eager';
  onLoad?: () => void;
  onError?: () => void;
}

/**
 * 优化的图片组件
 * 
 * 提供以下优化功能：
 * - 自动WebP/AVIF格式支持
 * - 响应式图片
 * - 懒加载
 * - 错误处理和占位图
 * - 性能监控
 */
export function OptimizedImage({
  src,
  alt,
  width,
  height,
  className,
  priority = false,
  placeholder = 'empty',
  blurDataURL,
  sizes,
  fill = false,
  quality = 75,
  loading = 'lazy',
  onLoad,
  onError,
}: OptimizedImageProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [imageSrc, setImageSrc] = useState(src);

  // 生成默认的模糊占位图
  const generateBlurDataURL = (w: number, h: number) => {
    const canvas = document.createElement('canvas');
    canvas.width = w;
    canvas.height = h;
    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.fillStyle = '#f3f4f6';
      ctx.fillRect(0, 0, w, h);
    }
    return canvas.toDataURL();
  };

  // 处理图片加载完成
  const handleLoad = () => {
    setIsLoading(false);
    onLoad?.();
  };

  // 处理图片加载错误
  const handleError = () => {
    setHasError(true);
    setIsLoading(false);
    
    // 设置占位图
    const fallbackSrc = `data:image/svg+xml;base64,${btoa(`
      <svg width="${width || 400}" height="${height || 300}" xmlns="http://www.w3.org/2000/svg">
        <rect width="100%" height="100%" fill="#f3f4f6"/>
        <text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="#9ca3af" font-family="system-ui, sans-serif" font-size="14">
          Image not available
        </text>
      </svg>
    `)}`;
    
    setImageSrc(fallbackSrc);
    onError?.();
  };

  // 检测WebP支持
  useEffect(() => {
    const checkWebPSupport = () => {
      const canvas = document.createElement('canvas');
      canvas.width = 1;
      canvas.height = 1;
      return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
    };

    if (typeof window !== 'undefined' && !hasError) {
      const supportsWebP = checkWebPSupport();
      
      // 如果支持WebP且原图不是WebP，尝试使用WebP版本
      if (supportsWebP && !src.includes('.webp') && !src.startsWith('data:')) {
        const webpSrc = src.replace(/\.(jpg|jpeg|png)$/i, '.webp');
        
        // 测试WebP版本是否存在
        const img = new window.Image();
        img.onload = () => setImageSrc(webpSrc);
        img.onerror = () => setImageSrc(src);
        img.src = webpSrc;
      }
    }
  }, [src, hasError]);

  // 响应式尺寸
  const responsiveSizes = sizes || (
    fill 
      ? '100vw'
      : '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw'
  );

  // 图片属性
  const imageProps = {
    src: imageSrc,
    alt,
    className: cn(
      'transition-opacity duration-300',
      isLoading && 'opacity-0',
      !isLoading && 'opacity-100',
      className
    ),
    priority,
    quality,
    onLoad: handleLoad,
    onError: handleError,
    sizes: responsiveSizes,
    placeholder: placeholder as any,
    blurDataURL: blurDataURL || (
      placeholder === 'blur' && width && height 
        ? generateBlurDataURL(width, height)
        : undefined
    ),
  };

  if (fill) {
    return (
      <div className="relative overflow-hidden">
        <Image
          {...imageProps}
          fill
          style={{ objectFit: 'cover' }}
        />
        {isLoading && (
          <div className="absolute inset-0 bg-gray-200 animate-pulse" />
        )}
      </div>
    );
  }

  return (
    <div className="relative">
      <Image
        {...imageProps}
        width={width}
        height={height}
      />
      {isLoading && (
        <div 
          className="absolute inset-0 bg-gray-200 animate-pulse"
          style={{ width, height }}
        />
      )}
    </div>
  );
}

// 预加载图片的工具函数
export function preloadImage(src: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const img = new window.Image();
    img.onload = () => resolve();
    img.onerror = reject;
    img.src = src;
  });
}

// 批量预加载图片
export async function preloadImages(srcs: string[]): Promise<void> {
  try {
    await Promise.all(srcs.map(preloadImage));
    console.log('All images preloaded successfully');
  } catch (error) {
    console.warn('Some images failed to preload:', error);
  }
}

// 图片尺寸优化建议
export function getOptimalImageSize(containerWidth: number, devicePixelRatio = 1): number {
  const sizes = [640, 750, 828, 1080, 1200, 1920, 2048, 3840];
  const targetWidth = containerWidth * devicePixelRatio;
  
  return sizes.find(size => size >= targetWidth) || sizes[sizes.length - 1];
}

// 图片格式检测
export function getSupportedImageFormat(): 'webp' | 'avif' | 'jpeg' {
  if (typeof window === 'undefined') return 'jpeg';
  
  const canvas = document.createElement('canvas');
  canvas.width = 1;
  canvas.height = 1;
  
  // 检测AVIF支持
  if (canvas.toDataURL('image/avif').indexOf('data:image/avif') === 0) {
    return 'avif';
  }
  
  // 检测WebP支持
  if (canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0) {
    return 'webp';
  }
  
  return 'jpeg';
}
