import { Category } from '../types';
import {
  getMongoDBCategories,
  getMongoDBCategoryById,
  getMongoDBCategoryBySlug
} from './mongodb-wrapper';

// 默认语言
const DEFAULT_LANGUAGE = 'en';

/**
 * 获取所有类别
 * @param language 语言，默认为英语
 * @returns 类别列表
 */
export async function getAllCategories(language: string = DEFAULT_LANGUAGE): Promise<Category[]> {
  return getMongoDBCategories(language);
}

/**
 * 根据ID获取类别
 * @param id 类别ID
 * @param language 语言，默认为英语
 * @returns 类别对象，如果未找到则返回undefined
 */
export async function getCategoryById(id: string, language: string = DEFAULT_LANGUAGE): Promise<Category | undefined> {
  return getMongoDBCategoryById(id, language);
}

/**
 * 根据Slug获取类别
 * @param slug 类别Slug
 * @param language 语言，默认为英语
 * @returns 类别对象，如果未找到则返回undefined
 */
export async function getCategoryBySlug(slug: string, language: string = DEFAULT_LANGUAGE): Promise<Category | undefined> {
  return getMongoDBCategoryBySlug(slug, language);
}

/**
 * 创建新类别
 * @param category 类别对象
 * @returns 创建的类别对象
 */
export async function createCategory(category: Omit<Category, '_id'>): Promise<Category> {
  // 确保类别有语言字段
  const categoryWithLanguage = {
    ...category,
    language: category.language || DEFAULT_LANGUAGE
  };

  // 在静态导出环境中，这个函数不会被调用
  // 但为了类型安全，我们返回一个模拟的类别对象
  if (process.env.STATIC_EXPORT === 'true') {
    return categoryWithLanguage as Category;
  }

  try {
    // 动态导入 MongoDB 模块
    const { createCategoryInMongoDB } = await import('./mongodb/categories');
    const newCategory = await createCategoryInMongoDB(categoryWithLanguage);
    return newCategory;
  } catch (error) {
    console.error('Error creating category in MongoDB:', error);
    throw error;
  }
}

/**
 * 更新类别
 * @param id 类别ID
 * @param category 更新的类别字段
 * @returns 更新后的类别对象，如果未找到则返回null
 */
export async function updateCategory(id: string, category: Partial<Category>): Promise<Category | null> {
  // 在静态导出环境中，这个函数不会被调用
  if (process.env.STATIC_EXPORT === 'true') {
    return null;
  }

  try {
    // 动态导入 MongoDB 模块
    const { updateCategoryInMongoDB } = await import('./mongodb/categories');
    const updatedCategory = await updateCategoryInMongoDB(id, category);
    return updatedCategory;
  } catch (error) {
    console.error(`Error updating category with id ${id} in MongoDB:`, error);
    throw error;
  }
}

/**
 * 删除类别
 * @param id 类别ID
 * @returns 是否成功删除
 */
export async function deleteCategory(id: string): Promise<boolean> {
  // 在静态导出环境中，这个函数不会被调用
  if (process.env.STATIC_EXPORT === 'true') {
    return false;
  }

  try {
    // 动态导入 MongoDB 模块
    const { deleteCategoryFromMongoDB } = await import('./mongodb/categories');
    const result = await deleteCategoryFromMongoDB(id);
    return result;
  } catch (error) {
    console.error(`Error deleting category with id ${id} from MongoDB:`, error);
    throw error;
  }
}
