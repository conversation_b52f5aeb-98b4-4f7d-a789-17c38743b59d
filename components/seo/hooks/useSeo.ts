'use client';

import { useEffect } from 'react';
import { Locale } from '@/lib/i18n/config';

interface SeoOptions {
  title?: string;
  description?: string;
  keywords?: string[];
  canonical?: string;
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  ogType?: 'website' | 'article';
  twitterTitle?: string;
  twitterDescription?: string;
  twitterImage?: string;
  noindex?: boolean;
  nofollow?: boolean;
  structuredData?: any;
}

/**
 * SEO Hook - 用于在客户端动态管理SEO标签
 * 主要用于补充静态生成时无法处理的动态SEO需求
 */
export function useSeo(options: SeoOptions) {
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const cleanupFunctions: (() => void)[] = [];

    // 更新页面标题
    if (options.title) {
      const originalTitle = document.title;
      document.title = options.title;
      cleanupFunctions.push(() => {
        document.title = originalTitle;
      });
    }

    // 更新meta描述
    if (options.description) {
      updateOrCreateMetaTag('description', options.description);
    }

    // 更新关键词
    if (options.keywords && options.keywords.length > 0) {
      updateOrCreateMetaTag('keywords', options.keywords.join(', '));
    }

    // 更新canonical链接
    if (options.canonical) {
      updateOrCreateLinkTag('canonical', options.canonical);
    }

    // 更新Open Graph标签
    if (options.ogTitle) {
      updateOrCreatePropertyTag('og:title', options.ogTitle);
    }
    if (options.ogDescription) {
      updateOrCreatePropertyTag('og:description', options.ogDescription);
    }
    if (options.ogImage) {
      updateOrCreatePropertyTag('og:image', options.ogImage);
    }
    if (options.ogType) {
      updateOrCreatePropertyTag('og:type', options.ogType);
    }

    // 更新Twitter Card标签
    if (options.twitterTitle) {
      updateOrCreateMetaTag('twitter:title', options.twitterTitle);
    }
    if (options.twitterDescription) {
      updateOrCreateMetaTag('twitter:description', options.twitterDescription);
    }
    if (options.twitterImage) {
      updateOrCreateMetaTag('twitter:image', options.twitterImage);
    }

    // 更新robots标签
    if (options.noindex || options.nofollow) {
      const robotsContent = [
        options.noindex ? 'noindex' : 'index',
        options.nofollow ? 'nofollow' : 'follow'
      ].join(', ');
      updateOrCreateMetaTag('robots', robotsContent);
    }

    // 添加结构化数据
    if (options.structuredData) {
      const scriptId = 'dynamic-structured-data';
      addStructuredData(options.structuredData, scriptId);
      cleanupFunctions.push(() => {
        removeStructuredData(scriptId);
      });
    }

    // 清理函数
    return () => {
      cleanupFunctions.forEach(cleanup => cleanup());
    };
  }, [options]);
}

// 辅助函数：更新或创建meta标签
function updateOrCreateMetaTag(name: string, content: string) {
  let meta = document.querySelector(`meta[name="${name}"]`) as HTMLMetaElement;
  
  if (meta) {
    meta.content = content;
  } else {
    meta = document.createElement('meta');
    meta.name = name;
    meta.content = content;
    document.head.appendChild(meta);
  }
}

// 辅助函数：更新或创建property meta标签
function updateOrCreatePropertyTag(property: string, content: string) {
  let meta = document.querySelector(`meta[property="${property}"]`) as HTMLMetaElement;
  
  if (meta) {
    meta.content = content;
  } else {
    meta = document.createElement('meta');
    meta.setAttribute('property', property);
    meta.content = content;
    document.head.appendChild(meta);
  }
}

// 辅助函数：更新或创建link标签
function updateOrCreateLinkTag(rel: string, href: string) {
  let link = document.querySelector(`link[rel="${rel}"]`) as HTMLLinkElement;
  
  if (link) {
    link.href = href;
  } else {
    link = document.createElement('link');
    link.rel = rel;
    link.href = href;
    document.head.appendChild(link);
  }
}

// 辅助函数：添加结构化数据
function addStructuredData(data: any, id: string) {
  removeStructuredData(id); // 先移除已存在的
  
  const script = document.createElement('script');
  script.type = 'application/ld+json';
  script.id = id;
  script.textContent = JSON.stringify(data);
  document.head.appendChild(script);
}

// 辅助函数：移除结构化数据
function removeStructuredData(id: string) {
  const existingScript = document.getElementById(id);
  if (existingScript) {
    existingScript.remove();
  }
}

/**
 * 页面级SEO Hook - 用于设置页面级别的SEO信息
 */
export function usePageSeo(
  locale: Locale,
  path: string,
  options: SeoOptions
) {
  const siteUrl = 'https://x114.org';
  const canonicalUrl = `${siteUrl}/${locale}${path}`;

  useSeo({
    ...options,
    canonical: options.canonical || canonicalUrl,
    ogTitle: options.ogTitle || options.title,
    ogDescription: options.ogDescription || options.description,
    twitterTitle: options.twitterTitle || options.title,
    twitterDescription: options.twitterDescription || options.description,
  });
}

/**
 * 工具页面SEO Hook
 */
export function useToolSeo(
  locale: Locale,
  tool: {
    name: string;
    description?: string;
    image?: string;
    slug?: string;
    id: string;
    tags?: string[];
  }
) {
  const path = `/tool/${tool.slug || tool.id}`;
  const title = `${tool.name} - AI Tool | X114 AI Tool Hub`;
  const description = tool.description || `Discover ${tool.name}, an innovative AI tool.`;

  usePageSeo(locale, path, {
    title,
    description,
    keywords: [...(tool.tags || []), 'AI tool', tool.name, 'X114'],
    ogImage: tool.image,
    ogType: 'article',
  });
}

/**
 * 类别页面SEO Hook
 */
export function useCategorySeo(
  locale: Locale,
  category: {
    name: string;
    description?: string;
    slug?: string;
    id: string;
  }
) {
  const path = `/category/${category.slug || category.id}`;
  const title = `${category.name} AI Tools | X114 AI Tool Hub`;
  const description = category.description || `Discover the best ${category.name} AI tools.`;

  usePageSeo(locale, path, {
    title,
    description,
    keywords: [category.name, 'AI tools', 'artificial intelligence', 'X114'],
    ogType: 'website',
  });
}
