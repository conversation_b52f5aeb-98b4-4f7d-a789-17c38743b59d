#!/usr/bin/env node

/**
 * SEO验证脚本
 * 检查生成的HTML文件中的SEO配置
 */

const fs = require('fs');
const path = require('path');
const { JSDOM } = require('jsdom');

// 颜色输出
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

// 检查HTML文件的SEO配置
function checkHtmlSeo(filePath, expectedData = {}) {
  if (!fs.existsSync(filePath)) {
    logError(`File not found: ${filePath}`);
    return { score: 0, issues: ['File not found'] };
  }

  const html = fs.readFileSync(filePath, 'utf8');
  const dom = new JSDOM(html);
  const document = dom.window.document;

  const issues = [];
  const passed = [];
  let score = 100;

  // 检查基本meta标签
  const title = document.querySelector('title');
  if (!title || !title.textContent.trim()) {
    issues.push('Missing or empty title tag');
    score -= 15;
  } else {
    passed.push('Title tag present');
    if (title.textContent.length > 60) {
      issues.push('Title too long (> 60 characters)');
      score -= 5;
    }
  }

  const description = document.querySelector('meta[name="description"]');
  if (!description || !description.content.trim()) {
    issues.push('Missing meta description');
    score -= 15;
  } else {
    passed.push('Meta description present');
    if (description.content.length > 160) {
      issues.push('Description too long (> 160 characters)');
      score -= 5;
    }
  }

  // 检查canonical链接
  const canonical = document.querySelector('link[rel="canonical"]');
  if (!canonical) {
    issues.push('Missing canonical link');
    score -= 10;
  } else {
    passed.push('Canonical link present');
  }

  // 检查hreflang标签
  const hreflangTags = document.querySelectorAll('link[rel="alternate"][hreflang]');
  if (hreflangTags.length === 0) {
    issues.push('Missing hreflang tags');
    score -= 10;
  } else {
    passed.push(`Hreflang tags present (${hreflangTags.length})`);
  }

  // 检查Open Graph标签
  const ogTitle = document.querySelector('meta[property="og:title"]');
  const ogDescription = document.querySelector('meta[property="og:description"]');
  const ogImage = document.querySelector('meta[property="og:image"]');
  const ogType = document.querySelector('meta[property="og:type"]');

  if (!ogTitle) {
    issues.push('Missing og:title');
    score -= 8;
  } else {
    passed.push('Open Graph title present');
  }

  if (!ogDescription) {
    issues.push('Missing og:description');
    score -= 8;
  } else {
    passed.push('Open Graph description present');
  }

  if (!ogImage) {
    issues.push('Missing og:image');
    score -= 5;
  } else {
    passed.push('Open Graph image present');
  }

  if (!ogType) {
    issues.push('Missing og:type');
    score -= 3;
  } else {
    passed.push('Open Graph type present');
  }

  // 检查Twitter Card标签
  const twitterCard = document.querySelector('meta[name="twitter:card"]');
  const twitterTitle = document.querySelector('meta[name="twitter:title"]');
  const twitterDescription = document.querySelector('meta[name="twitter:description"]');

  if (!twitterCard) {
    issues.push('Missing twitter:card');
    score -= 5;
  } else {
    passed.push('Twitter card present');
  }

  // 检查结构化数据
  const jsonLdScripts = document.querySelectorAll('script[type="application/ld+json"]');
  if (jsonLdScripts.length === 0) {
    issues.push('Missing structured data (JSON-LD)');
    score -= 10;
  } else {
    passed.push(`Structured data present (${jsonLdScripts.length} scripts)`);
    
    // 验证JSON-LD格式
    jsonLdScripts.forEach((script, index) => {
      try {
        JSON.parse(script.textContent);
        passed.push(`JSON-LD ${index + 1} is valid`);
      } catch (e) {
        issues.push(`Invalid JSON-LD ${index + 1}: ${e.message}`);
        score -= 5;
      }
    });
  }

  // 检查robots标签
  const robots = document.querySelector('meta[name="robots"]');
  if (robots) {
    passed.push('Robots meta tag present');
    if (robots.content.includes('noindex')) {
      issues.push('Page is set to noindex');
      score -= 20;
    }
  }

  return {
    score: Math.max(0, score),
    issues,
    passed,
    title: title?.textContent || '',
    description: description?.content || '',
    canonical: canonical?.href || '',
    hreflangCount: hreflangTags.length,
    jsonLdCount: jsonLdScripts.length
  };
}

// 主要验证函数
function validateSeo() {
  log('\n🔍 Validating SEO Configuration in Generated HTML Files\n', 'bold');

  const outDir = path.join(process.cwd(), 'out');
  if (!fs.existsSync(outDir)) {
    logError('Output directory not found. Please run "npm run build" first.');
    return false;
  }

  const testFiles = [
    { path: 'en/index.html', name: 'English Homepage' },
    { path: 'es/index.html', name: 'Spanish Homepage' },
    { path: 'zh/index.html', name: 'Chinese Homepage' },
    { path: 'en/tools/index.html', name: 'English Tools Page' },
    { path: 'en/categories/index.html', name: 'English Categories Page' },
  ];

  // 查找一些工具页面进行测试
  const toolDirs = fs.readdirSync(path.join(outDir, 'en/tool')).slice(0, 3);
  toolDirs.forEach(toolDir => {
    const toolIndexPath = path.join(outDir, 'en/tool', toolDir, 'index.html');
    if (fs.existsSync(toolIndexPath)) {
      testFiles.push({
        path: `en/tool/${toolDir}/index.html`,
        name: `Tool Page: ${toolDir}`
      });
    }
  });

  let totalScore = 0;
  let totalFiles = 0;
  const results = [];

  testFiles.forEach(file => {
    const filePath = path.join(outDir, file.path);
    log(`\n📄 Checking: ${file.name}`, 'blue');
    
    const result = checkHtmlSeo(filePath);
    results.push({ ...result, name: file.name, path: file.path });
    
    if (result.score >= 90) {
      logSuccess(`Score: ${result.score}/100 - Excellent!`);
    } else if (result.score >= 70) {
      log(`Score: ${result.score}/100 - Good`, 'yellow');
    } else {
      logError(`Score: ${result.score}/100 - Needs improvement`);
    }

    if (result.passed.length > 0) {
      log('✅ Passed checks:', 'green');
      result.passed.forEach(check => log(`   • ${check}`, 'green'));
    }

    if (result.issues.length > 0) {
      log('❌ Issues found:', 'red');
      result.issues.forEach(issue => log(`   • ${issue}`, 'red'));
    }

    totalScore += result.score;
    totalFiles++;
  });

  // 总结报告
  const averageScore = totalFiles > 0 ? Math.round(totalScore / totalFiles) : 0;
  
  log('\n📊 SEO Validation Summary:', 'bold');
  log(`Files checked: ${totalFiles}`);
  log(`Average score: ${averageScore}/100`, averageScore >= 90 ? 'green' : averageScore >= 70 ? 'yellow' : 'red');

  // 生成详细报告
  const reportPath = path.join(process.cwd(), 'seo-validation-report.json');
  const report = {
    timestamp: new Date().toISOString(),
    averageScore,
    totalFiles,
    results
  };
  
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  log(`\n📋 Detailed report saved to: ${reportPath}`, 'blue');

  // 建议
  log('\n💡 SEO Optimization Tips:', 'blue');
  log('1. Ensure all pages have unique, descriptive titles');
  log('2. Write compelling meta descriptions for better CTR');
  log('3. Use structured data to help search engines understand content');
  log('4. Optimize images with proper alt text and sizes');
  log('5. Ensure fast loading times and mobile-friendliness');
  log('6. Regularly monitor with Google Search Console');

  return averageScore >= 70;
}

// 运行验证
if (require.main === module) {
  try {
    const success = validateSeo();
    process.exit(success ? 0 : 1);
  } catch (error) {
    logError(`Validation failed: ${error.message}`);
    process.exit(1);
  }
}

module.exports = { validateSeo, checkHtmlSeo };
