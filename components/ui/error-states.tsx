'use client';

import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>gle, RefreshCw, Home, ArrowLeft, Bug, Wifi, Server } from 'lucide-react';
import { Button } from './button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './card';
import { cn } from '@/lib/utils';

// 错误类型
export type ErrorType =
  | 'network'
  | 'server'
  | 'validation'
  | 'not-found'
  | 'permission'
  | 'generic';

// 错误信息接口
interface ErrorInfo {
  type: ErrorType;
  title: string;
  message: string;
  code?: string;
  details?: string;
}

// 预定义错误信息
const ERROR_MESSAGES: Record<ErrorType, ErrorInfo> = {
  network: {
    type: 'network',
    title: 'Connection Problem',
    message: 'Unable to connect to the server. Please check your internet connection.',
    code: 'NETWORK_ERROR'
  },
  server: {
    type: 'server',
    title: 'Server Error',
    message: 'Something went wrong on our end. Please try again later.',
    code: 'SERVER_ERROR'
  },
  validation: {
    type: 'validation',
    title: 'Invalid Input',
    message: 'Please check your input and try again.',
    code: 'VALIDATION_ERROR'
  },
  'not-found': {
    type: 'not-found',
    title: 'Not Found',
    message: 'The page or resource you are looking for could not be found.',
    code: 'NOT_FOUND'
  },
  permission: {
    type: 'permission',
    title: 'Access Denied',
    message: 'You do not have permission to access this resource.',
    code: 'PERMISSION_DENIED'
  },
  generic: {
    type: 'generic',
    title: 'Something went wrong',
    message: 'An unexpected error occurred. Please try again.',
    code: 'GENERIC_ERROR'
  }
};

// 错误图标映射
const ERROR_ICONS = {
  network: Wifi,
  server: Server,
  validation: AlertTriangle,
  'not-found': AlertTriangle,
  permission: AlertTriangle,
  generic: Bug
};

// 基础错误显示组件
interface ErrorDisplayProps {
  error?: ErrorInfo | string | Error;
  type?: ErrorType;
  title?: string;
  message?: string;
  showIcon?: boolean;
  showCode?: boolean;
  onRetry?: () => void;
  onGoHome?: () => void;
  onGoBack?: () => void;
  className?: string;
  variant?: 'default' | 'minimal' | 'card';
  dictionary?: any;
}

export function ErrorDisplay({
  error,
  type = 'generic',
  title,
  message,
  showIcon = true,
  showCode = false,
  onRetry,
  onGoHome,
  onGoBack,
  className,
  variant = 'default'
}: ErrorDisplayProps) {
  // 解析错误信息
  let errorInfo: ErrorInfo;

  if (typeof error === 'string') {
    errorInfo = { ...ERROR_MESSAGES[type], message: error };
  } else if (error instanceof Error) {
    errorInfo = { ...ERROR_MESSAGES[type], message: error.message };
  } else if (error && typeof error === 'object' && 'type' in error) {
    errorInfo = error as ErrorInfo;
  } else {
    errorInfo = ERROR_MESSAGES[type];
  }

  // 使用传入的 title 和 message 覆盖默认值
  if (title) errorInfo.title = title;
  if (message) errorInfo.message = message;

  const IconComponent = ERROR_ICONS[errorInfo.type];

  const content = (
    <>
      {showIcon && (
        <div className="flex justify-center mb-4">
          <IconComponent className="h-12 w-12 text-destructive" />
        </div>
      )}

      <div className="text-center space-y-2">
        <h3 className="text-lg font-semibold text-foreground">
          {errorInfo.title}
        </h3>
        <p className="text-muted-foreground">
          {errorInfo.message}
        </p>
        {showCode && errorInfo.code && (
          <p className="text-xs text-muted-foreground font-mono">
            Error Code: {errorInfo.code}
          </p>
        )}
      </div>

      <div className="flex flex-col sm:flex-row gap-2 mt-6">
        {onRetry && (
          <Button onClick={onRetry} className="flex-1">
            <RefreshCw className="mr-2 h-4 w-4" />
            Try Again
          </Button>
        )}
        {onGoBack && (
          <Button onClick={onGoBack} variant="outline" className="flex-1">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Go Back
          </Button>
        )}
        {onGoHome && (
          <Button onClick={onGoHome} variant="outline" className="flex-1">
            <Home className="mr-2 h-4 w-4" />
            Home
          </Button>
        )}
      </div>
    </>
  );

  if (variant === 'minimal') {
    return (
      <div className={cn('flex items-center space-x-2 text-destructive', className)}>
        <AlertTriangle className="h-4 w-4" />
        <span className="text-sm">{errorInfo.message}</span>
        {onRetry && (
          <Button variant="ghost" size="sm" onClick={onRetry}>
            <RefreshCw className="h-3 w-3" />
          </Button>
        )}
      </div>
    );
  }

  if (variant === 'card') {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <IconComponent className="h-5 w-5 text-destructive" />
            <span>{errorInfo.title}</span>
          </CardTitle>
          <CardDescription>{errorInfo.message}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex space-x-2">
            {onRetry && (
              <Button onClick={onRetry} size="sm">
                <RefreshCw className="mr-2 h-4 w-4" />
                Retry
              </Button>
            )}
            {onGoBack && (
              <Button onClick={onGoBack} variant="outline" size="sm">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={cn('max-w-md mx-auto p-6', className)}>
      {content}
    </div>
  );
}

// 页面级错误组件
interface PageErrorProps {
  error?: ErrorInfo | string | Error;
  type?: ErrorType;
  onRetry?: () => void;
  className?: string;
}

export function PageError({ error, type = 'generic', onRetry, className }: PageErrorProps) {
  const handleGoHome = () => {
    window.location.href = '/';
  };

  const handleGoBack = () => {
    window.history.back();
  };

  return (
    <div className={cn('min-h-[400px] flex items-center justify-center p-6', className)}>
      <ErrorDisplay
        error={error}
        type={type}
        onRetry={onRetry}
        onGoHome={handleGoHome}
        onGoBack={handleGoBack}
        showCode={process.env.NODE_ENV === 'development'}
      />
    </div>
  );
}

// 内联错误组件
interface InlineErrorProps {
  message: string;
  onRetry?: () => void;
  className?: string;
}

export function InlineError({ message, onRetry, className }: InlineErrorProps) {
  return (
    <ErrorDisplay
      message={message}
      variant="minimal"
      onRetry={onRetry}
      className={className}
    />
  );
}

// 表单字段错误组件
interface FieldErrorProps {
  message?: string;
  className?: string;
}

export function FieldError({ message, className }: FieldErrorProps) {
  if (!message) return null;

  return (
    <p className={cn('text-sm text-destructive mt-1', className)}>
      {message}
    </p>
  );
}
