/**
 * Cloudflare Pages Functions API for tool submission
 *
 * This function handles tool submissions and stores them in Cloudflare D1 database
 */

interface Env {
  DB: D1Database;
  RESEND_API_KEY?: string;
  ADMIN_EMAIL?: string;
}

// Pages Functions 上下文类型
interface PagesContext {
  request: Request;
  env: Env;
  params: any;
  waitUntil: (promise: Promise<any>) => void;
  next: () => Promise<Response>;
  data: any;
}

interface ToolSubmission {
  name: string;
  description: string;
  website: string;
  category: string;
  email: string;
  additionalInfo?: string;
  locale: string;
  submittedAt: string;
}

// 验证提交数据
function validateSubmission(data: any): data is ToolSubmission {
  return (
    typeof data.name === 'string' && data.name.length >= 2 &&
    typeof data.description === 'string' && data.description.length >= 20 &&
    typeof data.website === 'string' && isValidUrl(data.website) &&
    typeof data.category === 'string' && data.category.length >= 1 &&
    typeof data.email === 'string' && isValidEmail(data.email) &&
    typeof data.locale === 'string' &&
    typeof data.submittedAt === 'string'
  );
}

function isValidUrl(url: string): boolean {
  try {
    const parsed = new URL(url);
    return ['http:', 'https:'].includes(parsed.protocol);
  } catch {
    return false;
  }
}

function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// 清理和转义数据
function sanitizeData(data: ToolSubmission): ToolSubmission {
  return {
    name: data.name.trim().slice(0, 100),
    description: data.description.trim().slice(0, 1000),
    website: data.website.trim(),
    category: data.category.trim(),
    email: data.email.trim().toLowerCase(),
    additionalInfo: data.additionalInfo?.trim().slice(0, 500) || '',
    locale: data.locale,
    submittedAt: data.submittedAt,
  };
}

// 发送邮件通知（可选）
async function sendNotificationEmail(env: Env, submission: ToolSubmission): Promise<void> {
  if (!env.RESEND_API_KEY || !env.ADMIN_EMAIL) {
    console.log('Email notification skipped: missing configuration');
    return;
  }

  try {
    const response = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${env.RESEND_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        from: '<EMAIL>',
        to: [env.ADMIN_EMAIL],
        subject: `New Tool Submission: ${submission.name}`,
        html: `
          <h2>New Tool Submission</h2>
          <p><strong>Name:</strong> ${submission.name}</p>
          <p><strong>Description:</strong> ${submission.description}</p>
          <p><strong>Website:</strong> <a href="${submission.website}">${submission.website}</a></p>
          <p><strong>Category:</strong> ${submission.category}</p>
          <p><strong>Contact Email:</strong> ${submission.email}</p>
          <p><strong>Language:</strong> ${submission.locale}</p>
          <p><strong>Submitted At:</strong> ${submission.submittedAt}</p>
          ${submission.additionalInfo ? `<p><strong>Additional Info:</strong> ${submission.additionalInfo}</p>` : ''}
        `,
      }),
    });

    if (!response.ok) {
      console.error('Failed to send email notification:', await response.text());
    }
  } catch (error) {
    console.error('Email notification error:', error);
  }
}

export async function onRequestPost(context: PagesContext): Promise<Response> {
  const { request, env } = context;

  // CORS headers
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type',
  };

  // Handle preflight requests
  if (request.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // 解析请求数据
    const data = await request.json();

    // 验证数据
    if (!validateSubmission(data)) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Invalid submission data'
        }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
            ...corsHeaders
          }
        }
      );
    }

    // 清理数据
    const cleanData = sanitizeData(data);

    // 生成唯一ID
    const submissionId = crypto.randomUUID();

    // 存储到D1数据库
    const stmt = env.DB.prepare(`
      INSERT INTO tool_submissions (
        id, name, description, website, category, email,
        additional_info, locale, submitted_at, status, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    await stmt.bind(
      submissionId,
      cleanData.name,
      cleanData.description,
      cleanData.website,
      cleanData.category,
      cleanData.email,
      cleanData.additionalInfo,
      cleanData.locale,
      cleanData.submittedAt,
      'pending', // 状态：pending, approved, rejected
      new Date().toISOString()
    ).run();

    // 发送邮件通知（异步，不阻塞响应）
    sendNotificationEmail(env, cleanData).catch(console.error);

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Tool submitted successfully',
        submissionId
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      }
    );

  } catch (error) {
    console.error('Submission error:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: 'Internal server error'
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      }
    );
  }
}
