'use client';

import { useEffect, useRef } from 'react';
import { AdFormat } from './core/ad-types';

interface GoogleAdProps {
  adSlot: string;
  adFormat?: AdFormat;
  fullWidthResponsive?: boolean;
  style?: React.CSSProperties;
  className?: string;
}

declare global {
  interface Window {
    adsbygoogle: any[];
  }
}

export default function GoogleAd({
  adSlot,
  adFormat = 'auto',
  fullWidthResponsive = true,
  style = { display: 'block' },
  className = '',
}: GoogleAdProps) {
  const adRef = useRef<HTMLModElement>(null);
  const isAdPushed = useRef(false);

  useEffect(() => {
    const pushAd = () => {
      try {
        if (typeof window !== 'undefined' && adRef.current && !isAdPushed.current) {
          // Initialize adsbygoogle array if it doesn't exist
          window.adsbygoogle = window.adsbygoogle || [];
          // Push the ad configuration
          window.adsbygoogle.push({});
          isAdPushed.current = true;
        }
      } catch (error) {
        console.error('Error loading Google Ad:', error);
      }
    };

    // Try to push ad immediately
    pushAd();

    // If adsbygoogle script is not loaded yet, wait for it
    if (typeof window !== 'undefined' && !window.adsbygoogle) {
      const checkAdSense = setInterval(() => {
        if (window.adsbygoogle) {
          pushAd();
          clearInterval(checkAdSense);
        }
      }, 100);

      return () => clearInterval(checkAdSense);
    }
  }, []);

  // Show placeholder in development mode
  if (process.env.NODE_ENV !== 'production') {
    return (
      <div 
        className={`bg-gray-100 dark:bg-gray-800 border-2 border-dashed border-gray-300 dark:border-gray-600 p-4 text-center text-sm text-gray-500 dark:text-gray-400 ${className}`}
        style={style}
      >
        Google Ad Placeholder (Development Mode)
        <br />
        Slot: {adSlot}
      </div>
    );
  }

  return (
    <ins
      ref={adRef}
      className={`adsbygoogle ${className}`}
      style={style}
      data-ad-client="ca-pub-7932958206248396"
      data-ad-slot={adSlot}
      data-ad-format={adFormat}
      data-full-width-responsive={fullWidthResponsive.toString()}
    />
  );
}