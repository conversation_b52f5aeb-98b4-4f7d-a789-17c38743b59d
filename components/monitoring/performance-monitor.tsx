'use client';

import { usePerformanceMonitoring, PerformanceMetrics, ErrorInfo } from '@/lib/utils/monitoring';
import { useEffect, useState } from 'react';
import { isDevelopment } from '@/lib/utils/environment';
import { initializePerformanceOptimizations } from '@/lib/utils/performance-optimization';

// 默认的指标收集回调
const defaultMetricsHandler = (metrics: PerformanceMetrics) => {
  // 在开发环境中打印指标
  if (isDevelopment()) {
    console.log('Performance metrics collected:', metrics);
  }

  // 在生产环境中，可以将指标发送到分析服务
  if (!isDevelopment()) {
    // 示例：发送到分析服务
    // sendToAnalyticsService(metrics);
  }
};

// 默认的错误处理回调
const defaultErrorHandler = (errorInfo: ErrorInfo) => {
  // 在开发环境中打印错误
  if (isDevelopment()) {
    console.error('Error captured by monitor:', errorInfo);
  }

  // 在生产环境中，可以将错误发送到错误跟踪服务
  if (!isDevelopment()) {
    // 示例：发送到错误跟踪服务
    // sendToErrorTrackingService(errorInfo);
  }
};

interface PerformanceMonitorProps {
  onMetricsCollected?: (metrics: PerformanceMetrics) => void;
  onError?: (errorInfo: ErrorInfo) => void;
  showDebugPanel?: boolean;
}

/**
 * 性能监控组件
 * 用于监控应用性能和错误
 */
export default function PerformanceMonitor({
  onMetricsCollected = defaultMetricsHandler,
  onError = defaultErrorHandler,
  showDebugPanel = false
}: PerformanceMonitorProps) {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({});

  // 使用性能监控 Hook
  usePerformanceMonitoring(
    (collectedMetrics) => {
      setMetrics(collectedMetrics);
      onMetricsCollected(collectedMetrics);
    },
    onError
  );

  // 初始化性能优化
  useEffect(() => {
    initializePerformanceOptimizations();
  }, []);

  // 如果不显示调试面板，则不渲染任何内容
  if (!showDebugPanel) {
    return null;
  }

  // 渲染调试面板
  return (
    <div className="fixed bottom-4 right-4 z-50 bg-white dark:bg-gray-800 p-4 rounded-lg shadow-lg max-w-md max-h-96 overflow-auto">
      <h3 className="text-lg font-semibold mb-2">Performance Metrics</h3>
      <div className="space-y-2">
        {metrics.pageLoadTime !== undefined && (
          <div className="flex justify-between">
            <span className="text-sm">Page Load Time:</span>
            <span className="text-sm font-mono">{metrics.pageLoadTime.toFixed(2)} ms</span>
          </div>
        )}
        {metrics.firstContentfulPaint !== undefined && (
          <div className="flex justify-between">
            <span className="text-sm">First Contentful Paint:</span>
            <span className="text-sm font-mono">{metrics.firstContentfulPaint.toFixed(2)} ms</span>
          </div>
        )}
        {metrics.largestContentfulPaint !== undefined && (
          <div className="flex justify-between">
            <span className="text-sm">Largest Contentful Paint:</span>
            <span className="text-sm font-mono">{metrics.largestContentfulPaint.toFixed(2)} ms</span>
          </div>
        )}
        {metrics.firstInputDelay !== undefined && (
          <div className="flex justify-between">
            <span className="text-sm">First Input Delay:</span>
            <span className="text-sm font-mono">{metrics.firstInputDelay.toFixed(2)} ms</span>
          </div>
        )}
        {metrics.cumulativeLayoutShift !== undefined && (
          <div className="flex justify-between">
            <span className="text-sm">Cumulative Layout Shift:</span>
            <span className="text-sm font-mono">{metrics.cumulativeLayoutShift.toFixed(4)}</span>
          </div>
        )}
        {metrics.timeToFirstByte !== undefined && (
          <div className="flex justify-between">
            <span className="text-sm">Time to First Byte:</span>
            <span className="text-sm font-mono">{metrics.timeToFirstByte.toFixed(2)} ms</span>
          </div>
        )}
        {metrics.domContentLoaded !== undefined && (
          <div className="flex justify-between">
            <span className="text-sm">DOM Content Loaded:</span>
            <span className="text-sm font-mono">{metrics.domContentLoaded.toFixed(2)} ms</span>
          </div>
        )}
        {metrics.loadComplete !== undefined && (
          <div className="flex justify-between">
            <span className="text-sm">Load Complete:</span>
            <span className="text-sm font-mono">{metrics.loadComplete.toFixed(2)} ms</span>
          </div>
        )}
      </div>

      {metrics.resourceLoadTimes && Object.keys(metrics.resourceLoadTimes).length > 0 && (
        <div className="mt-4">
          <h4 className="text-md font-semibold mb-2">Resource Load Times</h4>
          <div className="max-h-40 overflow-y-auto">
            {Object.entries(metrics.resourceLoadTimes)
              .sort(([, a], [, b]) => b - a)
              .slice(0, 10)
              .map(([resource, time]) => (
                <div key={resource} className="flex justify-between text-xs mb-1">
                  <span className="truncate max-w-[200px]" title={resource}>
                    {resource.split('/').pop()}
                  </span>
                  <span className="font-mono">{time.toFixed(2)} ms</span>
                </div>
              ))}
          </div>
        </div>
      )}
    </div>
  );
}
