'use client';

import React, { useState, useEffect } from 'react';
import { Loader2, RefreshCw, AlertCircle, CheckCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from './button';

// 加载状态类型
export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

// 加载状态配置
interface LoadingConfig {
  showSpinner?: boolean;
  showText?: boolean;
  text?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'minimal' | 'overlay';
}

// 基础加载指示器
interface LoadingIndicatorProps extends LoadingConfig {
  className?: string;
}

export function LoadingIndicator({
  showSpinner = true,
  showText = true,
  text = 'Loading...',
  size = 'md',
  variant = 'default',
  className
}: LoadingIndicatorProps) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8'
  };

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg'
  };

  if (variant === 'minimal') {
    return (
      <div className={cn('flex items-center justify-center', className)}>
        {showSpinner && (
          <Loader2 className={cn('animate-spin text-primary', sizeClasses[size])} />
        )}
      </div>
    );
  }

  if (variant === 'overlay') {
    return (
      <div className={cn(
        'absolute inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50',
        className
      )}>
        <div className="flex flex-col items-center space-y-2">
          {showSpinner && (
            <Loader2 className={cn('animate-spin text-primary', sizeClasses[size])} />
          )}
          {showText && (
            <p className={cn('text-muted-foreground', textSizeClasses[size])}>
              {text}
            </p>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={cn('flex items-center space-x-2', className)}>
      {showSpinner && (
        <Loader2 className={cn('animate-spin text-primary', sizeClasses[size])} />
      )}
      {showText && (
        <span className={cn('text-muted-foreground', textSizeClasses[size])}>
          {text}
        </span>
      )}
    </div>
  );
}

// 状态指示器
interface StatusIndicatorProps {
  state: LoadingState;
  loadingText?: string;
  successText?: string;
  errorText?: string;
  onRetry?: () => void;
  className?: string;
}

export function StatusIndicator({
  state,
  loadingText = 'Loading...',
  successText = 'Success!',
  errorText = 'Something went wrong',
  onRetry,
  className
}: StatusIndicatorProps) {
  const [showSuccess, setShowSuccess] = useState(false);

  useEffect(() => {
    if (state === 'success') {
      setShowSuccess(true);
      const timer = setTimeout(() => setShowSuccess(false), 3000);
      return () => clearTimeout(timer);
    }
  }, [state]);

  if (state === 'loading') {
    return (
      <div className={cn('flex items-center space-x-2', className)}>
        <Loader2 className="h-4 w-4 animate-spin text-primary" />
        <span className="text-sm text-muted-foreground">{loadingText}</span>
      </div>
    );
  }

  if (state === 'success' && showSuccess) {
    return (
      <div className={cn('flex items-center space-x-2 text-green-600', className)}>
        <CheckCircle className="h-4 w-4" />
        <span className="text-sm">{successText}</span>
      </div>
    );
  }

  if (state === 'error') {
    return (
      <div className={cn('flex items-center space-x-2', className)}>
        <AlertCircle className="h-4 w-4 text-destructive" />
        <span className="text-sm text-destructive">{errorText}</span>
        {onRetry && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onRetry}
            className="h-auto p-1 text-xs"
          >
            <RefreshCw className="h-3 w-3 mr-1" />
            Retry
          </Button>
        )}
      </div>
    );
  }

  return null;
}

// 页面级加载覆盖层
interface PageLoadingOverlayProps {
  isLoading: boolean;
  text?: string;
  className?: string;
}

export function PageLoadingOverlay({
  isLoading,
  text = 'Loading page...',
  className
}: PageLoadingOverlayProps) {
  if (!isLoading) return null;

  return (
    <div className={cn(
      'fixed inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50',
      className
    )}>
      <div className="flex flex-col items-center space-y-4 p-8 bg-card rounded-lg shadow-lg border">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <p className="text-muted-foreground">{text}</p>
      </div>
    </div>
  );
}

// 按钮加载状态
interface LoadingButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  isLoading?: boolean;
  loadingText?: string;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
}

export function LoadingButton({
  children,
  isLoading = false,
  loadingText,
  disabled,
  className,
  ...props
}: LoadingButtonProps) {
  return (
    <Button
      disabled={disabled || isLoading}
      className={className}
      {...props}
    >
      {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
      {isLoading ? (loadingText || children) : children}
    </Button>
  );
}

// 内容加载包装器
interface ContentLoaderProps {
  isLoading: boolean;
  error?: string | null;
  onRetry?: () => void;
  loadingComponent?: React.ReactNode;
  errorComponent?: React.ReactNode;
  children: React.ReactNode;
  className?: string;
}

export function ContentLoader({
  isLoading,
  error,
  onRetry,
  loadingComponent,
  errorComponent,
  children,
  className
}: ContentLoaderProps) {
  if (isLoading) {
    return (
      <div className={className}>
        {loadingComponent || <LoadingIndicator />}
      </div>
    );
  }

  if (error) {
    return (
      <div className={className}>
        {errorComponent || (
          <div className="flex flex-col items-center space-y-4 p-8 text-center">
            <AlertCircle className="h-12 w-12 text-destructive" />
            <div className="space-y-2">
              <h3 className="text-lg font-semibold">Something went wrong</h3>
              <p className="text-muted-foreground">{error}</p>
            </div>
            {onRetry && (
              <Button onClick={onRetry} variant="outline">
                <RefreshCw className="mr-2 h-4 w-4" />
                Try again
              </Button>
            )}
          </div>
        )}
      </div>
    );
  }

  return <div className={className}>{children}</div>;
}
