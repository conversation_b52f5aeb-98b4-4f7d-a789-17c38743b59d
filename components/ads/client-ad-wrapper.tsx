'use client';

/**
 * 客户端广告包装器
 * 用于在服务端组件中安全地使用广告组件
 */

import dynamic from 'next/dynamic';

// 动态导入广告组件，禁用 SSR
const TopBannerAdClient = dynamic(
  () => import('./ad-placements').then(mod => ({ default: mod.TopBannerAd })),
  { 
    ssr: false,
    loading: () => (
      <div className="w-full h-24 bg-muted/20 animate-pulse rounded flex items-center justify-center">
        <span className="text-muted-foreground text-sm">Loading Ad...</span>
      </div>
    )
  }
);

const ContentRectangleAdClient = dynamic(
  () => import('./ad-placements').then(mod => ({ default: mod.ContentRectangleAd })),
  { 
    ssr: false,
    loading: () => (
      <div className="w-80 h-64 bg-muted/20 animate-pulse rounded flex items-center justify-center mx-auto">
        <span className="text-muted-foreground text-sm">Loading Ad...</span>
      </div>
    )
  }
);

const SidebarAdClient = dynamic(
  () => import('./ad-placements').then(mod => ({ default: mod.SidebarAd })),
  { 
    ssr: false,
    loading: () => (
      <div className="w-full h-96 bg-muted/20 animate-pulse rounded flex items-center justify-center">
        <span className="text-muted-foreground text-sm">Loading Ad...</span>
      </div>
    )
  }
);

const InFeedAdClient = dynamic(
  () => import('./ad-placements').then(mod => ({ default: mod.InFeedAd })),
  { 
    ssr: false,
    loading: () => (
      <div className="w-full h-64 bg-muted/20 animate-pulse rounded flex items-center justify-center">
        <span className="text-muted-foreground text-sm">Loading Ad...</span>
      </div>
    )
  }
);

const InArticleAdClient = dynamic(
  () => import('./ad-placements').then(mod => ({ default: mod.InArticleAd })),
  { 
    ssr: false,
    loading: () => (
      <div className="w-80 h-64 bg-muted/20 animate-pulse rounded flex items-center justify-center mx-auto">
        <span className="text-muted-foreground text-sm">Loading Ad...</span>
      </div>
    )
  }
);

const ToolPageAdLayoutClient = dynamic(
  () => import('./ad-placements').then(mod => ({ default: mod.ToolPageAdLayout })),
  { 
    ssr: false,
    loading: () => <div>Loading...</div>
  }
);

const ListPageAdLayoutClient = dynamic(
  () => import('./ad-placements').then(mod => ({ default: mod.ListPageAdLayout })),
  { 
    ssr: false,
    loading: () => <div>Loading...</div>
  }
);

// 导出客户端包装器组件
export function TopBannerAd(props: { adSlot: string; className?: string; lazy?: boolean }) {
  return <TopBannerAdClient {...props} />;
}

export function ContentRectangleAd(props: { adSlot: string; className?: string; lazy?: boolean }) {
  return <ContentRectangleAdClient {...props} />;
}

export function SidebarAd(props: { adSlot: string; className?: string; lazy?: boolean }) {
  return <SidebarAdClient {...props} />;
}

export function InFeedAd(props: { adSlot: string; className?: string; lazy?: boolean }) {
  return <InFeedAdClient {...props} />;
}

export function InArticleAd(props: { adSlot: string; className?: string; lazy?: boolean }) {
  return <InArticleAdClient {...props} />;
}

export function ToolPageAdLayout(props: {
  children: React.ReactNode;
  sidebarAdSlot?: string;
  contentAdSlot?: string;
  className?: string;
}) {
  return <ToolPageAdLayoutClient {...props} />;
}

export function ListPageAdLayout(props: {
  children: React.ReactNode;
  topAdSlot?: string;
  sidebarAdSlot?: string;
  inFeedAdSlot?: string;
  className?: string;
}) {
  return <ListPageAdLayoutClient {...props} />;
}
