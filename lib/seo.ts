import { Metadata } from 'next';
import { Tool, Category } from './types';
import { Locale } from './i18n/config';

// 基础站点信息
const siteName = 'X114 AI Tool Hub';
const siteUrl = 'https://x114.org';

// SEO图片配置
const seoImages = {
  default: `${siteUrl}/images/screenshot-wide.png`,
  logo: `${siteUrl}/x114-logo-512x512.png`,
  favicon: '/favicon.ico',
  appleTouchIcon: '/x114-logo-192x192.png',
};

// 社交媒体配置
const socialConfig = {
  twitter: {
    site: '@x114ai',
    creator: '@x114ai',
  },
  facebook: {
    appId: process.env.NEXT_PUBLIC_FACEBOOK_APP_ID,
  },
};

// 辅助函数：将语言代码转换为Open Graph locale格式
function getOpenGraphLocale(locale: Locale): string {
  const localeMap: Record<Locale, string> = {
    'en': 'en_US',
    'zh': 'zh_CN',
    'tw': 'zh_TW',
    'ko': 'ko_KR',
    'ja': 'ja_JP',
    'pt': 'pt_PT',
    'es': 'es_ES',
    'de': 'de_DE',
    'fr': 'fr_FR',
    'vi': 'vi_VN',
  };
  return localeMap[locale] || 'en_US';
}

// 默认元数据
export const defaultMetadata: Metadata = {
  title: {
    default: 'Best AI Tools Directory & Reviews - X114 AI Tool Hub',
    template: '%s | X114 AI Tool Hub'
  },
  description: 'X114 is the ultimate directory of AI tools for productivity, design, writing, marketing, and more. Discover, compare, and explore the best AI tools updated daily.',
  keywords: ['AI tools', 'AI tools list', 'AI directory', 'best AI tools', 'free AI tools', 'ChatGPT alternatives', 'AI for writing', 'AI for design', 'AI productivity tools', 'X114'],
  authors: [{ name: 'X114 Team' }],
  creator: 'X114',
  publisher: 'X114',
  formatDetection: {
    email: false,
    telephone: false,
    address: false,
  },
  metadataBase: new URL(siteUrl),
  alternates: {
    canonical: '/',
    languages: {
      'en': '/en',
      'zh': '/zh',
      'tw': '/tw',
      'ko': '/ko',
      'ja': '/ja',
      'pt': '/pt',
      'es': '/es',
      'de': '/de',
      'fr': '/fr',
      'vi': '/vi',
      'x-default': '/en',
    },
  },
  openGraph: {
    type: 'website',
    siteName,
    locale: 'en_US',
    title: 'Best AI Tools Directory & Reviews - X114 AI Tool Hub',
    description: 'Discover, compare, and explore the top AI tools across categories. Updated daily by X114.',
    url: siteUrl,
    images: [
      {
        url: seoImages.default,
        width: 1200,
        height: 630,
        alt: 'X114 - AI Tools Directory',
        type: 'image/png',
      },
      {
        url: seoImages.logo,
        width: 512,
        height: 512,
        alt: 'X114 Logo',
        type: 'image/png',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    site: socialConfig.twitter.site,
    creator: socialConfig.twitter.creator,
    title: 'Best AI Tools Directory & Reviews - X114 AI Tool Hub',
    description: 'Discover the latest AI tools for productivity, creativity, and business. Explore curated AI apps on X114 today.',
    images: [
      {
        url: seoImages.default,
        alt: 'X114 - AI Tools Directory',
      },
    ],
  },
  robots: {
    index: true,
    follow: true,
    nocache: false,
    googleBot: {
      index: true,
      follow: true,
      noimageindex: false,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  icons: {
    icon: [
      { url: '/favicon-64x64.png', sizes: '16x16', type: 'image/png' },
      { url: '/favicon-64x64.png', sizes: '32x32', type: 'image/png' },
      { url: seoImages.favicon },
    ],
    shortcut: '/favicon.ico',
    apple: [
      { url: '/x114-logo-192x192.png', sizes: '180x180', type: 'image/png' },
      { url: seoImages.appleTouchIcon, sizes: '192x192', type: 'image/png' },
    ],
    other: [
      {
        rel: 'mask-icon',
        url: '/x114-logo-192x192.png',
        color: '#3b82f6',
      },
    ],
  },
  manifest: '/site.webmanifest',
  verification: {
    google: process.env.NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION,
    yandex: process.env.NEXT_PUBLIC_YANDEX_VERIFICATION,
    yahoo: process.env.NEXT_PUBLIC_YAHOO_VERIFICATION,
    other: {
      'msvalidate.01': process.env.NEXT_PUBLIC_BING_VERIFICATION || '',
    },
  },
  other: {
    'theme-color': '#3b82f6',
    'color-scheme': 'light dark',
    'mobile-web-app-capable': 'yes',
    'apple-mobile-web-app-capable': 'yes',
    'apple-mobile-web-app-status-bar-style': 'default',
    'apple-mobile-web-app-title': 'X114',
    'application-name': 'X114',
    'msapplication-TileColor': '#3b82f6',
    'msapplication-config': '/browserconfig.xml',
  },
};

// 工具页面元数据
export function generateToolMetadata(tool: Tool, locale: Locale): Metadata {
  const localizedUrl = `/${locale}/tool/${tool.slug || tool.id}`;
  const title = `${tool.name} - AI Tool | X114 AI Tool Hub`;
  const description = tool.description || `Discover ${tool.name}, an innovative AI tool that helps boost your productivity and creativity.`;

  return {
    title: title,
    description: description,
    keywords: [...(tool.tags || []), 'AI tool', tool.name, 'X114', 'artificial intelligence'],
    authors: [{ name: 'X114 Team' }],
    creator: 'X114',
    publisher: 'X114 AI Tool Hub',
    alternates: {
      canonical: localizedUrl,
      languages: {
        'en': `/en/tool/${tool.slug || tool.id}`,
        'zh': `/zh/tool/${tool.slug || tool.id}`,
        'tw': `/tw/tool/${tool.slug || tool.id}`,
        'ko': `/ko/tool/${tool.slug || tool.id}`,
        'ja': `/ja/tool/${tool.slug || tool.id}`,
        'pt': `/pt/tool/${tool.slug || tool.id}`,
        'es': `/es/tool/${tool.slug || tool.id}`,
        'de': `/de/tool/${tool.slug || tool.id}`,
        'fr': `/fr/tool/${tool.slug || tool.id}`,
        'vi': `/vi/tool/${tool.slug || tool.id}`,
        'x-default': `/en/tool/${tool.slug || tool.id}`,
      },
    },
    openGraph: {
      type: 'article',
      siteName,
      locale: getOpenGraphLocale(locale),
      title: title,
      description: description,
      url: `${siteUrl}${localizedUrl}`,
      images: [
        {
          url: tool.image || seoImages.default,
          width: 1200,
          height: 630,
          alt: `${tool.name} - AI Tool Screenshot`,
          type: 'image/png',
        },
      ],
      publishedTime: tool.createdAt ? (typeof tool.createdAt === 'string' ? tool.createdAt : tool.createdAt.toISOString()) : new Date().toISOString(),
      modifiedTime: tool.updatedAt ? (typeof tool.updatedAt === 'string' ? tool.updatedAt : tool.updatedAt.toISOString()) : new Date().toISOString(),
      section: 'AI Tools',
      tags: tool.tags || [],
      authors: ['X114 Team'],
    },
    twitter: {
      card: 'summary_large_image',
      site: socialConfig.twitter.site,
      creator: socialConfig.twitter.creator,
      title: title,
      description: description,
      images: [
        {
          url: tool.image || seoImages.default,
          alt: `${tool.name} - AI Tool Screenshot`,
        },
      ],
    },
  };
}

// 类别页面元数据
export function generateCategoryMetadata(category: Category, locale: Locale): Metadata {
  const localizedUrl = `/${locale}/categories/${category.slug || category.id}`;
  const title = `${category.name} AI Tools | X114 AI Tool Hub`;
  const description = `Discover the best ${category.name} AI tools. ${category.description || `Explore top-rated AI tools in the ${category.name} category.`}`;

  return {
    title: title,
    description: description,
    keywords: [category.name, 'AI tools', 'artificial intelligence', category.name + ' tools', 'X114', 'directory'],
    authors: [{ name: 'X114 Team' }],
    creator: 'X114',
    publisher: 'X114 AI Tool Hub',
    alternates: {
      canonical: localizedUrl,
      languages: {
        'en': `/en/categories/${category.slug || category.id}`,
        'zh': `/zh/categories/${category.slug || category.id}`,
        'tw': `/tw/categories/${category.slug || category.id}`,
        'ko': `/ko/categories/${category.slug || category.id}`,
        'ja': `/ja/categories/${category.slug || category.id}`,
        'pt': `/pt/categories/${category.slug || category.id}`,
        'es': `/es/categories/${category.slug || category.id}`,
        'de': `/de/categories/${category.slug || category.id}`,
        'fr': `/fr/categories/${category.slug || category.id}`,
        'vi': `/vi/categories/${category.slug || category.id}`,
        'x-default': `/en/categories/${category.slug || category.id}`,
      },
    },
    openGraph: {
      type: 'website',
      siteName,
      locale: getOpenGraphLocale(locale),
      title: title,
      description: description,
      url: `${siteUrl}${localizedUrl}`,
      images: [
        {
          url: seoImages.default,
          width: 1200,
          height: 630,
          alt: `${category.name} AI Tools - X114`,
          type: 'image/png',
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      site: socialConfig.twitter.site,
      creator: socialConfig.twitter.creator,
      title: title,
      description: description,
      images: [
        {
          url: seoImages.default,
          alt: `${category.name} AI Tools - X114`,
        },
      ],
    },
  };
}

// 文章页面元数据
export function generateArticleMetadata(article: any, locale: Locale): Metadata {
  const localizedUrl = `/${locale}/article/${article.slug}`;
  const title = `${article.title} | X114 AI Tool Hub`;
  const description = article.summary || article.description || `Read about ${article.title} on X114 AI Tool Hub.`;

  return {
    title: title,
    description: description,
    keywords: [...(article.tags || []), 'AI', 'artificial intelligence', 'technology', 'X114'],
    authors: [{ name: article.author || 'X114 Team' }],
    creator: 'X114',
    publisher: 'X114 AI Tool Hub',
    alternates: {
      canonical: localizedUrl,
      languages: {
        'en': `/en/article/${article.slug}`,
        'zh': `/zh/article/${article.slug}`,
        'tw': `/tw/article/${article.slug}`,
        'ko': `/ko/article/${article.slug}`,
        'ja': `/ja/article/${article.slug}`,
        'pt': `/pt/article/${article.slug}`,
        'es': `/es/article/${article.slug}`,
        'de': `/de/article/${article.slug}`,
        'fr': `/fr/article/${article.slug}`,
        'vi': `/vi/article/${article.slug}`,
        'x-default': `/en/article/${article.slug}`,
      },
    },
    openGraph: {
      type: 'article',
      siteName,
      locale: getOpenGraphLocale(locale),
      title: title,
      description: description,
      url: `${siteUrl}${localizedUrl}`,
      images: [
        {
          url: article.coverImage || seoImages.default,
          width: 1200,
          height: 630,
          alt: article.title,
          type: 'image/png',
        },
      ],
      publishedTime: article.publishedAt?.toISOString() || new Date().toISOString(),
      modifiedTime: article.updatedAt?.toISOString() || new Date().toISOString(),
      section: 'AI & Technology',
      tags: article.tags || [],
      authors: [article.author || 'X114 Team'],
    },
    twitter: {
      card: 'summary_large_image',
      site: socialConfig.twitter.site,
      creator: socialConfig.twitter.creator,
      title: title,
      description: description,
      images: [
        {
          url: article.coverImage || seoImages.default,
          alt: article.title,
        },
      ],
    },
  };
}

// 生成结构化数据
export function generateToolJsonLd(tool: Tool) {
  const baseJsonLd: any = {
    '@context': 'https://schema.org',
    '@type': 'SoftwareApplication',
    name: tool.name,
    description: tool.description || `${tool.name} is an innovative AI tool that helps boost productivity and creativity.`,
    image: tool.image || seoImages.default,
    url: tool.website || tool.url || `https://x114.org/tool/${tool.slug}`,
    applicationCategory: 'AI Application',
    operatingSystem: 'Web',
    datePublished: tool.createdAt ? (typeof tool.createdAt === 'string' ? tool.createdAt : tool.createdAt.toISOString()) : new Date().toISOString(),
    dateModified: tool.updatedAt ? (typeof tool.updatedAt === 'string' ? tool.updatedAt : tool.updatedAt.toISOString()) : new Date().toISOString(),
    publisher: {
      '@type': 'Organization',
      name: 'X114 AI Tool Hub',
      url: 'https://x114.org',
      logo: seoImages.logo
    },
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': `https://x114.org/tool/${tool.slug}`
    }
  };

  // 添加价格信息（默认为免费）
  baseJsonLd.offers = {
    '@type': 'Offer',
    price: '0',
    priceCurrency: 'USD',
    availability: 'https://schema.org/OnlineOnly',
    priceSpecification: {
      '@type': 'PriceSpecification',
      price: '0',
      priceCurrency: 'USD'
    }
  };

  // 添加评分信息（如果有）
  if (tool.rating && tool.rating > 0) {
    baseJsonLd.aggregateRating = {
      '@type': 'AggregateRating',
      ratingValue: tool.rating,
      ratingCount: tool.reviewCount || 1,
      bestRating: '5',
      worstRating: '1',
    };
  }

  // 添加标签作为关键词
  if (tool.tags && tool.tags.length > 0) {
    baseJsonLd.keywords = tool.tags.join(', ');
  }

  // 添加功能特性（基于描述生成）
  if (tool.description) {
    baseJsonLd.featureList = [
      'AI-powered functionality',
      'User-friendly interface',
      'Web-based application'
    ];
  }

  return baseJsonLd;
}

// 生成网站结构化数据
export function generateWebsiteJsonLd() {
  return {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: siteName,
    url: siteUrl,
    potentialAction: {
      '@type': 'SearchAction',
      target: `${siteUrl}/search?q={search_term_string}`,
      'query-input': 'required name=search_term_string',
    },
  };
}

// 生成组织结构化数据
export function generateOrganizationJsonLd() {
  return {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: 'X114',
    url: siteUrl,
    logo: `${siteUrl}/x114-logo-192x192.png`,
    sameAs: [
      'https://twitter.com/x114ai',
      'https://facebook.com/x114ai'
    ]
  };
}
