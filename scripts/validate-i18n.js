#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 读取语言包文件
const dictionariesPath = path.join(__dirname, '../lib/i18n/dictionaries');
const supportedLanguages = ['en', 'zh', 'tw', 'ko', 'ja', 'pt', 'es', 'de', 'fr', 'vi'];
const languagePaths = {};

supportedLanguages.forEach(lang => {
  languagePaths[lang] = path.join(dictionariesPath, `${lang}.json`);
});

function loadDictionary(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    console.error(`Error loading ${filePath}:`, error.message);
    return null;
  }
}

function getAllKeys(obj, prefix = '') {
  const keys = [];
  for (const key in obj) {
    const fullKey = prefix ? `${prefix}.${key}` : key;
    if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
      keys.push(...getAllKeys(obj[key], fullKey));
    } else {
      keys.push(fullKey);
    }
  }
  return keys;
}

function validateDictionaries() {
  console.log('🔍 验证语言包一致性...\n');

  // 加载所有语言包
  const dictionaries = {};
  const allKeys = {};

  for (const lang of supportedLanguages) {
    const dictionary = loadDictionary(languagePaths[lang]);
    if (!dictionary) {
      console.error(`❌ 无法加载 ${lang} 语言包文件`);
      process.exit(1);
    }
    dictionaries[lang] = dictionary;
    allKeys[lang] = getAllKeys(dictionary).sort();
  }

  // 以英文为基准
  const baseKeys = allKeys['en'];

  console.log(`📊 统计信息:`);
  supportedLanguages.forEach(lang => {
    const langName = {
      'en': '英文',
      'zh': '中文简体',
      'tw': '中文繁体',
      'ko': '韩语',
      'ja': '日语',
      'pt': '葡萄牙语',
      'es': '西班牙语',
      'de': '德语',
      'fr': '法语',
      'vi': '越南语'
    }[lang] || lang;
    console.log(`   ${langName}: ${allKeys[lang].length} 个键`);
  });
  console.log();

  let hasErrors = false;

  // 检查每种语言与英文的差异
  supportedLanguages.forEach(lang => {
    if (lang === 'en') return; // 跳过英文自身

    const langName = {
      'zh': '中文简体',
      'tw': '中文繁体',
      'ko': '韩语',
      'ja': '日语',
      'pt': '葡萄牙语',
      'es': '西班牙语',
      'de': '德语',
      'fr': '法语',
      'vi': '越南语'
    }[lang] || lang;

    const missingKeys = baseKeys.filter(key => !allKeys[lang].includes(key));
    const extraKeys = allKeys[lang].filter(key => !baseKeys.includes(key));

    if (missingKeys.length > 0) {
      console.log(`❌ ${langName}包缺失的键:`);
      missingKeys.forEach(key => console.log(`   - ${key}`));
      console.log();
      hasErrors = true;
    }

    if (extraKeys.length > 0) {
      console.log(`⚠️  ${langName}包多余的键:`);
      extraKeys.forEach(key => console.log(`   - ${key}`));
      console.log();
    }
  });

  if (!hasErrors) {
    console.log('✅ 所有语言包结构一致！');
    console.log('✅ 语言包验证通过！');
  } else {
    console.log('❌ 发现语言包不一致问题，请修复后重试。');
    process.exit(1);
  }
}

validateDictionaries();
