/**
 * 广告组件导出索引
 * 统一导出所有广告相关组件和工具
 */

// 核心组件
export { default as GoogleAd } from './google-ad';
export { default as ResponsiveAd, ResponsiveBannerAd, ResponsiveRectangleAd, ResponsiveSidebarAd } from './responsive-ad';

// 容器组件
export { 
  default as AdContainer,
  SidebarAdContainer,
  ContentAdContainer,
  HeaderAdContainer,
  FooterAdContainer,
  BetweenItemsAdContainer,
  FloatingAdContainer
} from './ad-container';

// 预定义广告位组件
export {
  TopBannerAd,
  ContentRectangleAd,
  SidebarAd,
  BottomBannerAd,
  InFeedAd,
  InArticleAd,
  ToolPageAdLayout,
  ListPageAdLayout
} from './ad-placements';

// 类型定义
export type {
  AdFormat,
  AdSize,
  AdPosition,
  AdDimensions,
  BaseAdProps,
  AdContainerProps,
  ResponsiveAdProps,
  AdPlacementConfig
} from './core/ad-types';

// 工具函数
export {
  getAdDimensions,
  generateAdStyle,
  getAdContainerClasses,
  shouldShowAd,
  generateAdPlaceholder,
  setupLazyLoading,
  getResponsiveAdSize
} from './core/ad-utils';

// 配置
export { AD_SIZES, AD_PLACEMENTS } from './core/ad-types';

// 性能监控
export { default as AdPerformanceMonitor, setupAdErrorHandling, setupAdVisibilityTracking } from './ad-performance-monitor';
