import { Tool } from './types';
import { getStaticTools } from './db/static-data';

/**
 * 简单的搜索函数，根据查询字符串搜索工具
 *
 * 此函数在客户端和服务器端都可以使用，用于基本的工具搜索功能。
 * 它会在工具的名称、描述、类别和标签中查找匹配项。
 *
 * @param query 搜索查询字符串
 * @param language 语言代码，默认为英语 ('en')
 * @returns 匹配的工具数组
 */
export function searchTools(query: string, language: string = 'en'): Tool[] {
  // 如果查询为空，返回空数组
  if (!query || query.trim() === '') {
    return [];
  }

  // 标准化查询字符串：转为小写并去除首尾空格
  const normalizedQuery = query.toLowerCase().trim();

  // 获取指定语言的工具数据
  const tools = getStaticTools(language);

  // 过滤匹配的工具
  return tools.filter(tool => {
    // 在名称、描述、类别和标签中搜索
    return (
      tool.name.toLowerCase().includes(normalizedQuery) ||
      (tool.description || '').toLowerCase().includes(normalizedQuery) ||
      (tool.tags || []).some(tag => tag.toLowerCase().includes(normalizedQuery))
    );
  });
}

/**
 * 高级搜索函数，支持多个字段和权重
 *
 * 此函数提供更精确的搜索结果，通过对不同字段的匹配分配不同的权重。
 * 它会在工具的名称、描述、标签和特性中查找匹配项，并根据匹配的重要性给予不同的分数。
 *
 * 权重分配：
 * - 名称部分匹配：10分
 * - 名称完全匹配：额外5分
 * - 每个匹配的标签：3分
 * - 描述匹配：2分
 * - 每个匹配的特性：1分
 *
 * @param query 搜索查询字符串
 * @param language 语言代码，默认为英语 ('en')
 * @returns 按相关性排序的匹配工具数组
 */
export function advancedSearchTools(query: string, language: string = 'en'): Tool[] {
  // 如果查询为空，返回空数组
  if (!query || query.trim() === '') {
    return [];
  }

  // 标准化查询字符串：转为小写并去除首尾空格
  const normalizedQuery = query.toLowerCase().trim();

  // 获取指定语言的工具数据
  const tools = getStaticTools(language);

  // 用于存储搜索结果和分数
  const searchResults: { tool: Tool; score: number }[] = [];

  // 为每个工具计算相关性分数
  for (const tool of tools) {
    let score = 0;

    // 名称匹配权重最高
    if (tool.name.toLowerCase().includes(normalizedQuery)) {
      score += 10;
      // 完全匹配给予额外分数
      if (tool.name.toLowerCase() === normalizedQuery) {
        score += 5;
      }
    }



    // 标签匹配
    const matchingTags = (tool.tags || []).filter((tag: string) =>
      tag.toLowerCase().includes(normalizedQuery)
    );
    score += matchingTags.length * 3;

    // 描述匹配
    if ((tool.description || '').toLowerCase().includes(normalizedQuery)) {
      score += 2;
    }

    // 特性匹配
    const matchingFeatures = tool.features ? tool.features.filter((feature: string) =>
      feature.toLowerCase().includes(normalizedQuery)
    ) : [];
    score += matchingFeatures.length;

    // 如果有任何匹配，添加到结果中
    if (score > 0) {
      searchResults.push({ tool, score });
    }
  }

  // 按分数排序并返回工具列表
  return searchResults
    .sort((a, b) => b.score - a.score)
    .map(result => result.tool);
}
