import { getTools } from '@/lib/db/tools';
import ToolsSection from './tools-section';

/**
 * 最新工具区块组件
 * 展示最新添加的工具
 */
export default async function NewToolsSection({ dictionary }: { dictionary: any }) {
  // 获取当前语言
  const locale = dictionary.locale || 'en';

  // 从数据库获取最新工具
  const newTools = await getTools({ sort: 'newest', limit: 4, isNew: true }, locale);

  return (
    <ToolsSection
      title={dictionary.home.new.title}
      viewAllText={dictionary.home.new.viewAll}
      viewAllLink={`/${locale}/tools?sort=newest`}
      tools={newTools}
      locale={locale}
      dictionary={dictionary}
    />
  );
}