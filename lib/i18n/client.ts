'use client';

/**
 * 客户端国际化工具
 * 提供客户端渲染时使用的国际化功能
 */

import { useParams, usePathname, useRouter } from 'next/navigation';
import { Locale, defaultLocale, locales } from './config';
import { formatNumber, formatDate, formatCurrency, formatRelativeTime, localizeUrl } from './utils';

/**
 * 获取当前语言的 Hook
 * @returns 当前语言
 */
export function useLocale(): Locale {
  // 获取路由参数和路径
  const params = useParams();
  const pathname = usePathname();

  // 从路由参数中获取语言
  const localeFromParams = params?.locale as Locale;

  // 从路径中提取语言代码
  const segments = pathname.split('/');
  const localeSegment = segments[1];

  // 确定使用哪个语言代码
  if (localeFromParams && locales.includes(localeFromParams)) {
    return localeFromParams;
  } else if (localeSegment && locales.includes(localeSegment as Locale)) {
    return localeSegment as Locale;
  } else {
    return defaultLocale;
  }
}

/**
 * 切换语言的 Hook
 * @returns 切换语言的函数
 */
export function useChangeLocale() {
  const router = useRouter();
  const pathname = usePathname();

  /**
   * 切换语言
   * @param newLocale 目标语言
   */
  return (newLocale: Locale) => {
    const segments = pathname.split('/');

    // 检查当前第一个段是否是语言代码
    if (locales.includes(segments[1] as Locale)) {
      segments[1] = newLocale;
    } else {
      // 将语言代码插入为第一个段
      segments.splice(1, 0, newLocale);
    }

    // 导航到新路径
    router.push(segments.join('/'));
  };
}

/**
 * 获取本地化 URL 的 Hook
 * @returns 本地化 URL 的函数
 */
export function useLocalizeUrl() {
  const currentLocale = useLocale();

  /**
   * 本地化 URL
   * @param path 原始路径
   * @param locale 目标语言，默认为当前语言
   * @returns 本地化后的 URL
   */
  return (path: string, locale: Locale = currentLocale) => {
    return localizeUrl(path, locale);
  };
}

/**
 * 导出格式化工具函数，用于客户端渲染
 */
export { formatNumber, formatDate, formatCurrency, formatRelativeTime };