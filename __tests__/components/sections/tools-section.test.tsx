import React from 'react';
import { render, screen } from '@testing-library/react';
import ToolsSection from '@/components/sections/tools-section';

// Mock the dependencies
jest.mock('@/components/ui/section-header', () => {
  return function MockSectionHeader({ title, viewAllText, viewAllLink }: any) {
    return (
      <div data-testid="section-header">
        <h2>{title}</h2>
        {viewAllText && viewAllLink && (
          <a href={viewAllLink}>{viewAllText}</a>
        )}
      </div>
    );
  };
});

jest.mock('@/components/tools/tool-card', () => {
  return function MockToolCard({ tool, locale, dictionary, showTimeAgo, showRating, showViewDetails, showTooltips }: any) {
    return (
      <div data-testid="tool-card">
        <h3>{tool.name}</h3>
        <p>{tool.description}</p>
        <div>Locale: {locale}</div>
        <div>Dictionary: {dictionary ? 'provided' : 'not provided'}</div>
        <div>ShowTimeAgo: {showTimeAgo ? 'true' : 'false'}</div>
        <div>ShowRating: {showRating ? 'true' : 'false'}</div>
        <div>ShowViewDetails: {showViewDetails ? 'true' : 'false'}</div>
        <div>ShowTooltips: {showTooltips ? 'true' : 'false'}</div>
      </div>
    );
  };
});

// Mock tool data
const mockTools = [
  {
    id: 'tool-1',
    slug: 'tool-1',
    name: 'Tool 1',
    description: 'Description 1',
    image: '/images/tool-1.jpg',
    url: 'https://tool-1.com',
    rating: 4.5,
    reviewCount: 100,
    tags: ['ai', 'test'],
    featured: true,
    trending: false,
    isNew: false,
    createdAt: '2023-01-01',
    language: 'en'
  },
  {
    id: 'tool-2',
    slug: 'tool-2',
    name: 'Tool 2',
    description: 'Description 2',
    image: '/images/tool-2.jpg',
    url: 'https://tool-2.com',
    rating: 4.8,
    reviewCount: 200,
    tags: ['ai', 'premium'],
    featured: false,
    trending: true,
    isNew: false,
    createdAt: '2023-02-01',
    language: 'en'
  }
];

describe('ToolsSection', () => {
  it('renders the section header with title', () => {
    render(
      <ToolsSection
        title="Test Section"
        tools={mockTools}
        locale="en"
      />
    );

    expect(screen.getByText('Test Section')).toBeInTheDocument();
  });

  it('renders the section header with view all link', () => {
    render(
      <ToolsSection
        title="Test Section"
        viewAllText="View All"
        viewAllLink="/view-all"
        tools={mockTools}
        locale="en"
      />
    );

    expect(screen.getByText('View All')).toBeInTheDocument();
  });

  it('renders the correct number of tool cards', () => {
    render(
      <ToolsSection
        title="Test Section"
        tools={mockTools}
        locale="en"
      />
    );

    const toolCards = screen.getAllByTestId('tool-card');
    expect(toolCards).toHaveLength(2);
  });

  it('passes the correct props to tool cards', () => {
    render(
      <ToolsSection
        title="Test Section"
        tools={mockTools}
        locale="en"
        showTimeAgo={true}
        showRating={false}
        showViewDetails={true}
        showTooltips={false}
      />
    );

    expect(screen.getAllByText('ShowTimeAgo: true')).toHaveLength(2);
    expect(screen.getAllByText('ShowRating: false')).toHaveLength(2);
    expect(screen.getAllByText('ShowViewDetails: true')).toHaveLength(2);
    expect(screen.getAllByText('ShowTooltips: false')).toHaveLength(2);
  });

  it('renders tool names correctly', () => {
    render(
      <ToolsSection
        title="Test Section"
        tools={mockTools}
        locale="en"
      />
    );

    expect(screen.getByText('Tool 1')).toBeInTheDocument();
    expect(screen.getByText('Tool 2')).toBeInTheDocument();
  });

  it('renders tool descriptions correctly', () => {
    render(
      <ToolsSection
        title="Test Section"
        tools={mockTools}
        locale="en"
      />
    );

    expect(screen.getByText('Description 1')).toBeInTheDocument();
    expect(screen.getByText('Description 2')).toBeInTheDocument();
  });

  it('applies custom className', () => {
    const { container } = render(
      <ToolsSection
        title="Test Section"
        tools={mockTools}
        locale="en"
        className="custom-class"
      />
    );

    const sectionElement = container.firstChild;
    expect(sectionElement).toHaveClass('custom-class');
  });

  it('applies custom grid className', () => {
    const { container } = render(
      <ToolsSection
        title="Test Section"
        tools={mockTools}
        locale="en"
        gridClassName="custom-grid"
      />
    );

    const gridElement = container.querySelector('.custom-grid');
    expect(gridElement).toBeInTheDocument();
  });

  it('returns null when tools array is empty', () => {
    const { container } = render(
      <ToolsSection
        title="Test Section"
        tools={[]}
        locale="en"
      />
    );

    expect(container.firstChild).toBeNull();
  });

  it('returns null when tools is undefined', () => {
    const { container } = render(
      <ToolsSection
        title="Test Section"
        tools={undefined as any}
        locale="en"
      />
    );

    expect(container.firstChild).toBeNull();
  });
});
