export interface Tool {
  id: string;
  slug: string;
  name: string;
  description: string;
  what_is?: string;
  how_to_use?: string;
  image?: string;
  website?: string;
  url?: string; // 网站链接的别名
  website_logo?: string;
  groups?: Group[];
  tags?: string[];
  features?: string[];
  rating?: number;
  reviewCount?: number;
  monthlyVisits?: number; // 月访问量
  featured?: boolean;
  trending?: boolean;
  isNew?: boolean;
  createdAt?: Date | string;
  updatedAt?: Date | string;
  language?: string; // 语言标识，如 'en', 'zh', 'es'
}

export interface Group {
  id: string;
  slug: string;
  name: string;
}

export interface User {
  id: string;
  name: string;
  email: string;
  image?: string;
  savedTools: string[];
  reviews: Review[];
  submissions: Tool[];
  createdAt: Date;
  updatedAt: Date;
  language: string; // 用户首选语言
}

export interface Review {
  id: string;
  toolId: string;
  userId: string;
  rating: number;
  title: string;
  content: string;
  likes: number;
  createdAt: Date;
  updatedAt: Date;
  language: string; // 评论语言
}

export interface Category {
  id: string;
  slug: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  toolCount: number;
  language: string; // 语言标识，如 'en', 'zh', 'es'
}

export interface FilterOptions {
  category?: string;
  featured?: boolean;
  trending?: boolean;
  isNew?: boolean;
  minRating?: number;
  tags?: string[];
  sort?: 'newest' | 'popular' | 'rating';
  language?: string; // 按语言筛选
}

export interface Article {
  _id?: string; // MongoDB ObjectId
  id?: string; // 可选的字符串ID，用于兼容
  title: string;
  slug: string;
  summary: string;
  content: string;
  ends_content?: string;
  language: string; // 文章语言
  published: number; // 发布时间戳
  status: number; // 文章状态：1=草稿, 2=已发布等
  created_at: number; // 创建时间戳
  updated_at: number; // 更新时间戳
  modules?: ArticleModule[]; // 文章模块/工具列表

  // 以下字段用于前端显示，可能需要从其他数据计算得出
  author?: string;
  coverImage?: string;
  tags?: string[];
  category?: string;
  featured?: boolean;
  publishedAt?: Date; // 从published时间戳转换而来
  createdAt?: Date; // 从created_at时间戳转换而来
  updatedAt?: Date; // 从updated_at时间戳转换而来
  relatedTools?: string[]; // 相关工具ID
}

export interface ArticleModule {
  link?: string;
  content?: string;
  website?: string;
  handle?: string;
  name?: string;
  image?: string;
}
