/**
 * 这个脚本用于在构建前从 MongoDB 获取数据，并将其保存为静态数据
 * 在静态导出时使用
 */

require('./base');
const { MongoClient } = require('mongodb');
const fs = require('fs');
const path = require('path');

// MongoDB 连接信息
const MONGO_DB = process.env.MONGODB_DB;
const MONGO_URI = process.env.MONGODB_URI;

// 支持的语言
const SUPPORTED_LANGUAGES = process.env.SUPPORTED_LANGUAGES.split(',');

// 数据类型配置
const DATA_TYPES = {
  TOOLS: {
    collection: 'tools',
    filename: 'tools',
    indexes: [
      { key: { language: 1 } },
      { key: { id: 1 } },
      { key: { category: 1 } },
      { key: { tags: 1 } }
    ],
    query: (language) => ({ language })
  },
  CATEGORIES: {
    collection: 'categories',
    filename: 'categories',
    indexes: [
      { key: { language: 1 } },
      { key: { id: 1 } }
    ],
    query: (language) => ({ language })
  },
  ARTICLES: {
    collection: 'articles',
    filename: 'articles',
    indexes: [
      { key: { language: 1 } },
      { key: { id: 1 } },
      { key: { slug: 1 } },
      { key: { tags: 1 } },
      { key: { category: 1 } },
      { key: { published: 1 } },
      { key: { publishedAt: -1 } }
    ],
    query: (language) => ({ language, status: 2 })
  }
};

// 静态数据存储路径
const STATIC_DATA_DIR = path.join(__dirname, '..', 'lib', 'static-data');

// 确保静态数据目录存在
if (!fs.existsSync(STATIC_DATA_DIR)) {
  fs.mkdirSync(STATIC_DATA_DIR, { recursive: true });
}

// 按语言和对象类型拆分的静态数据文件
const getStaticDataFilePath = (type, language) => {
  return path.join(STATIC_DATA_DIR, `${type}-${language}.json`);
};

// 处理日期对象，确保它们可以被序列化为 JSON
function processDateFields(obj) {
  if (!obj) return obj;

  const result = { ...obj };
  const dateFields = ['createdAt', 'updatedAt', 'publishedAt', 'lastModified'];

  for (const field of dateFields) {
    if (result[field] instanceof Date) {
      // 将日期转换为 ISO 字符串
      result[field] = result[field].toISOString();
    } else if (result[field] && typeof result[field] === 'string') {
      // 尝试将字符串解析为日期
      try {
        const date = new Date(result[field]);
        if (!isNaN(date.getTime())) {
          result[field] = date.toISOString();
        }
      } catch (error) {
        console.warn(`Failed to parse date field ${field}:`, error);
      }
    }
  }

  return result;
}

/**
 * 从 MongoDB 获取指定类型的数据并保存为静态文件
 * @param {Object} db - MongoDB 数据库实例
 * @param {string} dataType - 数据类型配置的键名
 * @param {string} language - 语言代码
 * @param {boolean} force - 是否强制重新生成数据
 * @param {boolean} verbose - 是否显示详细输出
 * @returns {Promise<number>} - 获取的数据条数
 */
async function fetchAndSaveDataByType(db, dataType, language, force = false, verbose = false) {
  const config = DATA_TYPES[dataType];
  const collection = db.collection(config.collection);
  const filePath = path.join(STATIC_DATA_DIR, `${config.filename}-${language}.json`);

  // 检查文件是否已存在
  if (!force && fs.existsSync(filePath)) {
    try {
      // 读取现有文件
      const existingData = JSON.parse(fs.readFileSync(filePath, 'utf-8'));

      if (verbose) {
        console.log(`File ${filePath} already exists with ${existingData.length} items. Use --force to regenerate.`);
      } else {
        console.log(`Using existing ${config.collection} data for language ${language} (${existingData.length} items)`);
      }

      return existingData.length;
    } catch (error) {
      console.warn(`Error reading existing file ${filePath}, will regenerate: ${error.message}`);
    }
  }

  // 创建索引以提高查询性能
  for (const index of config.indexes) {
    await collection.createIndex(index.key);
  }

  // 构建查询条件
  const query = config.query(language);

  if (verbose) {
    console.log(`Fetching ${config.collection} for language ${language} with query:`, query);
  }

  // 获取数据
  const data = await collection.find(query).toArray();

  // 处理日期对象，确保它们可以被序列化为 JSON
  const processedData = data.map(item => processDateFields(item));

  // 保存到按语言和类型拆分的文件
  fs.writeFileSync(filePath, JSON.stringify(processedData, null, 2));

  if (verbose) {
    console.log(`Fetched ${data.length} ${config.collection} for language ${language}, saved to ${filePath}`);
    console.log(`File size: ${(fs.statSync(filePath).size / 1024).toFixed(2)} KB`);
  } else {
    console.log(`Fetched ${data.length} ${config.collection} for language ${language}`);
  }

  return data.length;
}

// 从 MongoDB 获取数据
async function fetchDataFromMongoDB(force = false, verbose = false) {
  console.log('Connecting to MongoDB...');

  if (!verbose) {
    console.log(`MongoDB URI: ${MONGO_URI.replace(/:[^:@]*@/, ':****@')}`); // 隐藏密码
    console.log(`Database: ${MONGO_DB}`);
  }

  const client = new MongoClient(MONGO_URI);

  try {
    await client.connect();
    console.log('Connected to MongoDB');

    const db = client.db(MONGO_DB);
    const dataTypes = Object.keys(DATA_TYPES);
    const stats = {};

    // 为每种语言获取数据
    for (const language of SUPPORTED_LANGUAGES) {
      console.log(`\nFetching data for language: ${language}`);
      stats[language] = {};

      // 获取每种类型的数据
      for (const dataType of dataTypes) {
        stats[language][dataType] = await fetchAndSaveDataByType(db, dataType, language, force, verbose);
      }
    }

    console.log('\nStatic data files generated successfully!');
    console.log('\nSummary:');

    for (const language of SUPPORTED_LANGUAGES) {
      console.log(`\n${language.toUpperCase()}:`);
      for (const dataType of dataTypes) {
        console.log(`  - ${DATA_TYPES[dataType].collection}: ${stats[language][dataType]} items`);
      }
    }

    return true;
  } catch (error) {
    console.error('Error fetching data from MongoDB:', error);

    if (verbose && error.stack) {
      console.error('Error details:', error.stack);
    }

    throw error;
  } finally {
    await client.close();
    console.log('MongoDB connection closed');
  }
}

/**
 * 生成静态数据模块的头部代码
 * @returns {string} 模块头部代码
 */
function generateModuleHeader() {
  return `/**
 * 这个文件是自动生成的，用于在静态导出时加载和处理静态数据
 * 数据存储在单独的JSON文件中，按语言和对象类型拆分
 */

// 支持的语言
const SUPPORTED_LANGUAGES = ${JSON.stringify(SUPPORTED_LANGUAGES)};

// 缓存数据
const dataCache = {
  ${Object.keys(DATA_TYPES).map(type => `${DATA_TYPES[type].filename}: {}`).join(',\n  ')}
};

// 处理日期字符串，将其转换回 Date 对象
function processDate(dateStr) {
  if (typeof dateStr === 'string') {
    return new Date(dateStr);
  }
  return dateStr;
}

// 处理对象中的所有日期字段
function processDateFields(obj) {
  if (!obj) return obj;

  const result = { ...obj };
  const dateFields = ['createdAt', 'updatedAt', 'publishedAt', 'lastModified'];

  for (const field of dateFields) {
    if (result[field]) {
      result[field] = processDate(result[field]);
    }
  }

  return result;
}

// 加载数据文件
function loadDataFile(type, language) {
  // 如果缓存中已有数据，则直接返回
  if (dataCache[type][language]) {
    return dataCache[type][language];
  }

  try {
    // 动态导入数据文件
    const data = require(\`./\${type}-\${language}.json\`);

    // 缓存数据
    dataCache[type][language] = data;

    return data;
  } catch (error) {
    console.warn(\`Failed to load \${type} data for language \${language}: \${error.message}\`);
    return [];
  }
}`;
}

/**
 * 生成数据访问函数
 * @returns {string} 数据访问函数代码
 */
function generateDataAccessFunctions() {
  let code = '';

  // 生成获取所有数据的函数
  Object.keys(DATA_TYPES).forEach(type => {
    const config = DATA_TYPES[type];
    const funcName = `getStatic${config.filename.charAt(0).toUpperCase() + config.filename.slice(1)}`;

    code += `
// ${config.collection}数据
export function ${funcName}(language = 'en') {
  const data = loadDataFile('${config.filename}', language);
  return data.map(item => processDateFields(item));
}`;
  });

  // 生成根据ID获取数据的函数
  Object.keys(DATA_TYPES).forEach(type => {
    const config = DATA_TYPES[type];
    const singularName = config.filename.endsWith('s') ? config.filename.slice(0, -1) : config.filename;
    const funcName = `getStatic${singularName.charAt(0).toUpperCase() + singularName.slice(1)}ById`;

    code += `
// 根据 ID 获取${config.collection}
export function ${funcName}(id, language = 'en') {
  const data = loadDataFile('${config.filename}', language);
  const item = data.find(item => item.id === id);
  return item ? processDateFields(item) : undefined;
}`;
  });

  // 为文章添加根据Slug获取的函数
  if (DATA_TYPES.ARTICLES) {
    code += `
// 根据 Slug 获取文章
export function getStaticArticleBySlug(slug, language = 'en') {
  const articles = loadDataFile('articles', language);
  const article = articles.find(article => article.slug === slug);
  return article ? processDateFields(article) : undefined;
}`;
  }

  // 添加获取支持的语言函数
  code += `
// 获取所有支持的语言
export function getSupportedLanguages() {
  return SUPPORTED_LANGUAGES;
}`;

  return code;
}

// 创建静态数据模块
function createStaticDataModule() {
  const staticDataModulePath = path.join(STATIC_DATA_DIR, 'index.js');

  const moduleContent = [
    generateModuleHeader(),
    generateDataAccessFunctions()
  ].join('\n');

  fs.writeFileSync(staticDataModulePath, moduleContent);
  console.log(`Static data module created at ${staticDataModulePath}`);
}

/**
 * 列出生成的静态数据文件
 */
function listGeneratedFiles() {
  console.log('Static data files:');

  const files = fs.readdirSync(STATIC_DATA_DIR);
  const jsonFiles = files.filter(file => file.endsWith('.json'));
  const otherFiles = files.filter(file => !file.endsWith('.json'));

  // 按语言和类型分组显示JSON文件
  const groupedFiles = {};

  jsonFiles.forEach(file => {
    const match = file.match(/^([a-z]+)-([a-z]+)\.json$/);
    if (match) {
      const [_, type, language] = match;
      if (!groupedFiles[language]) {
        groupedFiles[language] = {};
      }

      const filePath = path.join(STATIC_DATA_DIR, file);
      const stats = fs.statSync(filePath);
      const size = (stats.size / 1024).toFixed(2);

      groupedFiles[language][type] = `${size} KB`;
    }
  });

  // 显示分组的文件
  Object.keys(groupedFiles).sort().forEach(language => {
    console.log(`\n${language.toUpperCase()}:`);
    Object.keys(groupedFiles[language]).sort().forEach(type => {
      console.log(`  - ${type}: ${groupedFiles[language][type]}`);
    });
  });

  // 显示其他文件
  if (otherFiles.length > 0) {
    console.log('\nOther files:');
    otherFiles.forEach(file => {
      const filePath = path.join(STATIC_DATA_DIR, file);
      const stats = fs.statSync(filePath);
      console.log(`  - ${file} (${(stats.size / 1024).toFixed(2)} KB)`);
    });
  }
}

// 解析命令行参数
function parseArgs() {
  const args = process.argv.slice(2);
  return {
    verbose: args.includes('--verbose') || args.includes('-v'),
    help: args.includes('--help') || args.includes('-h'),
    force: args.includes('--force') || args.includes('-f')
  };
}

// 显示帮助信息
function showHelp() {
  console.log(`
Usage: node scripts/fetch-static-data.js [options]

Options:
  --verbose, -v  显示详细输出
  --force, -f    强制重新生成所有数据，即使文件已存在
  --help, -h     显示帮助信息

Examples:
  node scripts/fetch-static-data.js
  node scripts/fetch-static-data.js --verbose
  node scripts/fetch-static-data.js --force
  `);
}

// 主函数
async function main() {
  // 解析命令行参数
  const args = parseArgs();

  // 如果请求帮助，显示帮助信息并退出
  if (args.help) {
    showHelp();
    return;
  }

  const verbose = args.verbose;
  const force = args.force;

  try {
    console.log('Fetching static data for build...');
    console.log(`MongoDB Database: ${MONGO_DB}`);
    console.log(`Supported Languages: ${SUPPORTED_LANGUAGES.join(', ')}`);
    console.log(`Data Types: ${Object.keys(DATA_TYPES).map(type => DATA_TYPES[type].collection).join(', ')}`);

    if (verbose) {
      console.log('\nDetailed configuration:');
      console.log(`MongoDB URI: ${MONGO_URI.replace(/:[^:@]*@/, ':****@')}`); // 隐藏密码
      console.log(`Static Data Directory: ${STATIC_DATA_DIR}`);
      console.log(`Force regenerate: ${force ? 'Yes' : 'No'}`);
      console.log('\nData Type Configurations:');
      Object.keys(DATA_TYPES).forEach(type => {
        const config = DATA_TYPES[type];
        console.log(`\n${type}:`);
        console.log(`  Collection: ${config.collection}`);
        console.log(`  Filename: ${config.filename}`);
        console.log(`  Indexes: ${JSON.stringify(config.indexes)}`);
        console.log(`  Query: ${config.query.toString()}`);
      });
    }

    // 获取数据
    await fetchDataFromMongoDB(force, verbose);

    // 创建静态数据模块
    createStaticDataModule();

    console.log('\nStatic data fetched and module created successfully');

    // 列出生成的文件
    listGeneratedFiles();

    console.log('\nStatic data generation completed successfully!');
  } catch (error) {
    console.error('Error during static data generation:');
    console.error(error.message || error);

    if (verbose && error.stack) {
      console.error('\nStack trace:');
      console.error(error.stack);
    }

    console.error('\nPlease check your MongoDB connection and try again.');
    process.exit(1);
  }
}

// 检查是否安装了 dotenv 模块
try {
  require.resolve('dotenv');
} catch (error) {
  console.error('Error: dotenv module is required but not installed.');
  console.error('Please install it using: npm install dotenv');
  process.exit(1);
}

// 执行主函数
main();
