# Google Ads Setup Guide for X114

## Current Issues Fixed

### 1. Script Loading Strategy
- **Problem**: AdSense script was only loaded in production
- **Solution**: Now loads in all environments with `beforeInteractive` strategy for better initialization

### 2. Ad Unit Implementation
- **Problem**: <PERSON>rip<PERSON> was loaded but no ad units were placed on pages
- **Solution**: Added GoogleAd component usage examples on homepage

### 3. Initialization Timing
- **Problem**: Ads weren't being pushed to adsbygoogle array properly
- **Solution**: Improved component with proper timing and error handling

## How to Use Google Ads

### Step 1: Get Your Ad Unit IDs
1. Go to your Google AdSense dashboard
2. Create ad units for different placements
3. Copy the `data-ad-slot` IDs for each unit

### Step 2: Replace Placeholder Ad Slots
In the homepage (`app/[locale]/page.tsx`), replace these placeholder IDs:
```typescript
// Replace "1234567890" with your actual banner ad slot ID
<GoogleAd adSlot="1234567890" />

// Replace "0987654321" with your actual rectangle ad slot ID  
<GoogleAd adSlot="0987654321" />
```

### Step 3: Add Ads to Other Pages
You can add ads to any page by importing and using the GoogleAd component:

```typescript
import GoogleAd from '@/components/ads/google-ad';

// In your component JSX:
<GoogleAd 
  adSlot="YOUR_AD_SLOT_ID"
  adFormat="auto" // or "rectangle", "vertical", "horizontal"
  style={{ display: 'block', width: '100%', height: '90px' }}
/>
```

## Ad Formats Available

### 1. Banner Ad (728x90)
```typescript
<GoogleAd 
  adSlot="YOUR_BANNER_SLOT_ID"
  adFormat="auto"
  style={{ display: 'block', width: '100%', maxWidth: '728px', height: '90px' }}
/>
```

### 2. Rectangle Ad (300x250)
```typescript
<GoogleAd 
  adSlot="YOUR_RECTANGLE_SLOT_ID"
  adFormat="rectangle"
  style={{ display: 'block', width: '300px', height: '250px' }}
/>
```

### 3. Responsive Ad
```typescript
<GoogleAd 
  adSlot="YOUR_RESPONSIVE_SLOT_ID"
  adFormat="auto"
  fullWidthResponsive={true}
  style={{ display: 'block' }}
/>
```

## Testing Your Ads

### Development Mode
- Ads show as placeholders with slot IDs
- This prevents invalid clicks during development

### Production Mode
- Real ads will display
- Make sure to test on your live site

## Troubleshooting

### Ads Not Showing
1. **Check Ad Slot IDs**: Make sure you're using correct slot IDs from AdSense
2. **AdSense Approval**: Ensure your site is approved for AdSense
3. **Content Policy**: Make sure your content complies with AdSense policies
4. **Ad Blocker**: Test without ad blockers enabled

### Console Errors
Check browser console for:
- `adsbygoogle.js` loading errors
- Invalid ad slot warnings
- Policy violation messages

### Performance
- Ads load asynchronously and won't block page rendering
- Script uses `beforeInteractive` strategy for optimal loading

## Best Practices

### 1. Ad Placement
- Place ads where they're visible but not intrusive
- Don't place too many ads close together
- Consider user experience

### 2. Responsive Design
- Use responsive ad formats for mobile compatibility
- Test ads on different screen sizes

### 3. Performance
- Monitor Core Web Vitals impact
- Use lazy loading for below-fold ads if needed

## Next Steps

1. Replace placeholder ad slot IDs with your real ones
2. Test ads on your staging/production environment
3. Monitor ad performance in AdSense dashboard
4. Optimize ad placements based on performance data

## Common Ad Slot Examples

```typescript
// Header banner
<GoogleAd adSlot="1111111111" adFormat="auto" />

// Sidebar rectangle  
<GoogleAd adSlot="2222222222" adFormat="rectangle" />

// In-content responsive
<GoogleAd adSlot="3333333333" adFormat="auto" fullWidthResponsive={true} />

// Footer banner
<GoogleAd adSlot="4444444444" adFormat="horizontal" />
```

Remember to replace all placeholder IDs with your actual AdSense ad unit IDs!