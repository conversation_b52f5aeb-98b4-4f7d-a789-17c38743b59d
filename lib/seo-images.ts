// SEO图片优化配置

const siteUrl = 'https://x114.org';

// 图片尺寸配置
export const seoImageSizes = {
  og: { width: 1200, height: 630 },
  twitter: { width: 1200, height: 630 },
  logo: { width: 512, height: 512 },
  favicon: { width: 32, height: 32 },
  appleTouchIcon: { width: 180, height: 180 },
};

// 默认SEO图片
export const defaultSeoImages = {
  og: `${siteUrl}/images/screenshot-wide.png`,
  twitter: `${siteUrl}/images/screenshot-wide.png`,
  logo: `${siteUrl}/x114-logo-512x512.png`,
  favicon: `${siteUrl}/favicon.ico`,
  appleTouchIcon: `${siteUrl}/x114-logo-192x192.png`,
};

// 生成工具特定的OG图片URL
export function generateToolOgImage(tool: {
  name: string;
  description?: string;
  image?: string;
  category?: string;
}): string {
  // 如果工具有自己的图片，使用它
  if (tool.image && tool.image.startsWith('http')) {
    return tool.image;
  }
  
  // 否则生成动态OG图片URL
  const params = new URLSearchParams({
    title: tool.name,
    description: tool.description || '',
    category: tool.category || 'AI Tool',
    logo: defaultSeoImages.logo,
  });
  
  return `${siteUrl}/api/og/tool?${params.toString()}`;
}

// 生成分类特定的OG图片URL
export function generateCategoryOgImage(category: {
  name: string;
  description?: string;
  icon?: string;
  color?: string;
}): string {
  const params = new URLSearchParams({
    title: `${category.name} AI Tools`,
    description: category.description || `Discover the best ${category.name} AI tools`,
    icon: category.icon || '🤖',
    color: category.color || '#3b82f6',
  });
  
  return `${siteUrl}/api/og/category?${params.toString()}`;
}

// 生成文章特定的OG图片URL
export function generateArticleOgImage(article: {
  title: string;
  summary?: string;
  coverImage?: string;
  author?: string;
}): string {
  // 如果文章有封面图，使用它
  if (article.coverImage && article.coverImage.startsWith('http')) {
    return article.coverImage;
  }
  
  const params = new URLSearchParams({
    title: article.title,
    summary: article.summary || '',
    author: article.author || 'X114 Team',
    type: 'article',
  });
  
  return `${siteUrl}/api/og/article?${params.toString()}`;
}

// 验证图片URL
export function validateImageUrl(url: string): boolean {
  try {
    const parsedUrl = new URL(url);
    return ['http:', 'https:'].includes(parsedUrl.protocol);
  } catch {
    return false;
  }
}

// 获取优化的图片URL
export function getOptimizedImageUrl(
  originalUrl: string,
  options: {
    width?: number;
    height?: number;
    quality?: number;
    format?: 'webp' | 'jpeg' | 'png';
  } = {}
): string {
  if (!validateImageUrl(originalUrl)) {
    return defaultSeoImages.og;
  }
  
  // 如果是外部图片，直接返回
  if (!originalUrl.startsWith(siteUrl)) {
    return originalUrl;
  }
  
  // 构建优化参数
  const params = new URLSearchParams();
  if (options.width) params.set('w', options.width.toString());
  if (options.height) params.set('h', options.height.toString());
  if (options.quality) params.set('q', options.quality.toString());
  if (options.format) params.set('f', options.format);
  
  const separator = originalUrl.includes('?') ? '&' : '?';
  return `${originalUrl}${separator}${params.toString()}`;
}

// 生成响应式图片srcset
export function generateImageSrcSet(
  baseUrl: string,
  sizes: number[] = [400, 800, 1200, 1600]
): string {
  return sizes
    .map(size => `${getOptimizedImageUrl(baseUrl, { width: size })} ${size}w`)
    .join(', ');
}

// 图片alt文本生成
export function generateImageAlt(context: {
  type: 'tool' | 'category' | 'article' | 'logo' | 'og';
  name: string;
  description?: string;
}): string {
  switch (context.type) {
    case 'tool':
      return `${context.name} - AI Tool Screenshot`;
    case 'category':
      return `${context.name} AI Tools Category`;
    case 'article':
      return `${context.name} - Article Cover Image`;
    case 'logo':
      return `${context.name} Logo`;
    case 'og':
      return `${context.name} - Social Media Preview`;
    default:
      return context.name;
  }
}

// 检查图片是否需要优化
export function needsOptimization(url: string): boolean {
  if (!validateImageUrl(url)) return false;
  
  // 检查是否是大图片
  const largeImageExtensions = ['.png', '.jpg', '.jpeg', '.webp'];
  const hasLargeExtension = largeImageExtensions.some(ext => 
    url.toLowerCase().includes(ext)
  );
  
  return hasLargeExtension && url.startsWith(siteUrl);
}

// 生成完整的图片元数据
export function generateImageMetadata(
  url: string,
  context: {
    type: 'tool' | 'category' | 'article' | 'logo' | 'og';
    name: string;
    description?: string;
    width?: number;
    height?: number;
  }
) {
  const optimizedUrl = needsOptimization(url) 
    ? getOptimizedImageUrl(url, { 
        width: context.width || seoImageSizes.og.width,
        height: context.height || seoImageSizes.og.height,
        quality: 85,
        format: 'webp'
      })
    : url;
    
  return {
    url: optimizedUrl,
    alt: generateImageAlt(context),
    width: context.width || seoImageSizes.og.width,
    height: context.height || seoImageSizes.og.height,
    type: 'image/webp',
    srcSet: generateImageSrcSet(url),
  };
}

// 预加载关键图片
export function generatePreloadLinks(images: string[]): Array<{
  rel: string;
  href: string;
  as: string;
  type?: string;
}> {
  return images.map(url => ({
    rel: 'preload',
    href: getOptimizedImageUrl(url, { width: 400, format: 'webp' }),
    as: 'image',
    type: 'image/webp',
  }));
}
