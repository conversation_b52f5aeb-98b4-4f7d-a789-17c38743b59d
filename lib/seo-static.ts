import { Metadata } from 'next';
import { Locale } from './i18n/config';

// 静态导出专用的SEO配置
// 这个文件专门为静态导出优化，避免依赖服务器端功能

const siteUrl = 'https://x114.org';
const siteName = 'X114 AI Tool Hub';

// 静态页面的SEO配置
export const staticSeoConfig = {
  siteUrl,
  siteName,
  defaultTitle: 'Best AI Tools Directory - X114 AI Tool Hub',
  defaultDescription: 'Discover the best AI tools for productivity, design, writing, marketing, and more. Updated daily by X114.',
  defaultKeywords: [
    'AI tools',
    'artificial intelligence',
    'AI directory',
    'best AI tools',
    'free AI tools',
    'AI apps',
    'machine learning tools',
    'productivity tools',
    'AI software',
    'X114'
  ],
  social: {
    twitter: '@x114ai',
    facebook: 'x114ai',
  },
  images: {
    default: `${siteUrl}/images/snapshot-wide.png`,
    logo: `${siteUrl}/x114-logo-512x512.png`,
    favicon: '/favicon.ico',
  },
};

// 为静态页面生成基础元数据
export function generateStaticMetadata(
  title?: string,
  description?: string,
  path: string = '',
  locale: Locale = 'en'
): Metadata {
  const fullTitle = title ? `${title} | ${staticSeoConfig.siteName}` : staticSeoConfig.defaultTitle;
  const fullDescription = description || staticSeoConfig.defaultDescription;
  const canonicalUrl = `${staticSeoConfig.siteUrl}/${locale}${path}`;

  return {
    title: fullTitle,
    description: fullDescription,
    keywords: staticSeoConfig.defaultKeywords,
    authors: [{ name: 'X114 Team' }],
    creator: 'X114',
    publisher: staticSeoConfig.siteName,
    metadataBase: new URL(staticSeoConfig.siteUrl),
    alternates: {
      canonical: canonicalUrl,
      languages: {
        'en': `/en${path}`,
        'zh': `/zh${path}`,
        'tw': `/tw${path}`,
        'ko': `/ko${path}`,
        'ja': `/ja${path}`,
        'pt': `/pt${path}`,
        'es': `/es${path}`,
        'de': `/de${path}`,
        'fr': `/fr${path}`,
        'vi': `/vi${path}`,
        'x-default': `/en${path}`,
      },
    },
    openGraph: {
      type: 'website',
      siteName: staticSeoConfig.siteName,
      locale: getOpenGraphLocale(locale),
      title: fullTitle,
      description: fullDescription,
      url: canonicalUrl,
      images: [
        {
          url: staticSeoConfig.images.default,
          width: 1200,
          height: 630,
          alt: fullTitle,
          type: 'image/png',
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      site: staticSeoConfig.social.twitter,
      creator: staticSeoConfig.social.twitter,
      title: fullTitle,
      description: fullDescription,
      images: [
        {
          url: staticSeoConfig.images.default,
          alt: fullTitle,
        },
      ],
    },
    robots: {
      index: true,
      follow: true,
      nocache: false,
      googleBot: {
        index: true,
        follow: true,
        noimageindex: false,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    icons: {
      icon: [
        { url: '/favicon-64x64.png', sizes: '16x16', type: 'image/png' },
        { url: '/favicon-64x64.png', sizes: '32x32', type: 'image/png' },
        { url: staticSeoConfig.images.favicon },
      ],
      shortcut: '/favicon.ico',
      apple: [
        { url: '/x114-logo-192x192.png', sizes: '180x180', type: 'image/png' },
        { url: staticSeoConfig.images.logo, sizes: '512x512', type: 'image/png' },
      ],
    },
    manifest: '/site.webmanifest',
    other: {
      'theme-color': '#3b82f6',
      'color-scheme': 'light dark',
      'mobile-web-app-capable': 'yes',
      'apple-mobile-web-app-capable': 'yes',
      'apple-mobile-web-app-status-bar-style': 'default',
      'apple-mobile-web-app-title': 'X114',
      'application-name': 'X114',
      'msapplication-TileColor': '#3b82f6',
    },
  };
}

// 生成静态页面的结构化数据
export function generateStaticJsonLd(type: 'website' | 'organization' | 'breadcrumb', data?: any) {
  const baseJsonLd = {
    '@context': 'https://schema.org',
  };

  switch (type) {
    case 'website':
      return {
        ...baseJsonLd,
        '@type': 'WebSite',
        name: staticSeoConfig.siteName,
        url: staticSeoConfig.siteUrl,
        description: staticSeoConfig.defaultDescription,
        potentialAction: {
          '@type': 'SearchAction',
          target: `${staticSeoConfig.siteUrl}/search?q={search_term_string}`,
          'query-input': 'required name=search_term_string',
        },
        publisher: {
          '@type': 'Organization',
          name: 'X114',
          url: staticSeoConfig.siteUrl,
          logo: staticSeoConfig.images.logo,
        },
      };

    case 'organization':
      return {
        ...baseJsonLd,
        '@type': 'Organization',
        name: 'X114',
        url: staticSeoConfig.siteUrl,
        logo: staticSeoConfig.images.logo,
        description: 'Leading directory of AI tools and artificial intelligence applications',
        sameAs: [
          `https://twitter.com/${staticSeoConfig.social.twitter.replace('@', '')}`,
          `https://facebook.com/${staticSeoConfig.social.facebook}`,
        ],
        contactPoint: {
          '@type': 'ContactPoint',
          contactType: 'customer service',
          url: `${staticSeoConfig.siteUrl}/contact`,
        },
      };

    case 'breadcrumb':
      if (!data || !data.items) return null;
      return {
        ...baseJsonLd,
        '@type': 'BreadcrumbList',
        itemListElement: data.items.map((item: any, index: number) => ({
          '@type': 'ListItem',
          position: index + 1,
          name: item.name,
          item: item.url,
        })),
      };

    default:
      return null;
  }
}

// 辅助函数
function getOpenGraphLocale(locale: Locale): string {
  const localeMap: Record<Locale, string> = {
    'en': 'en_US',
    'zh': 'zh_CN',
    'tw': 'zh_TW',
    'ko': 'ko_KR',
    'ja': 'ja_JP',
    'pt': 'pt_PT',
    'es': 'es_ES',
    'de': 'de_DE',
    'fr': 'fr_FR',
    'vi': 'vi_VN',
  };
  return localeMap[locale] || 'en_US';
}

// 生成面包屑导航数据
export function generateBreadcrumbData(items: Array<{ name: string; url: string }>) {
  return {
    items: items.map(item => ({
      name: item.name,
      url: `${staticSeoConfig.siteUrl}${item.url}`,
    })),
  };
}

// 验证SEO配置
export function validateSeoConfig() {
  const errors: string[] = [];

  if (!staticSeoConfig.siteUrl) {
    errors.push('Site URL is required');
  }

  if (!staticSeoConfig.siteName) {
    errors.push('Site name is required');
  }

  if (!staticSeoConfig.defaultTitle) {
    errors.push('Default title is required');
  }

  if (!staticSeoConfig.defaultDescription) {
    errors.push('Default description is required');
  }

  if (staticSeoConfig.defaultDescription.length > 160) {
    errors.push('Default description should be under 160 characters');
  }

  if (staticSeoConfig.defaultTitle.length > 60) {
    errors.push('Default title should be under 60 characters');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}
