import { Metadata } from 'next';
import { getDictionary } from '@/lib/i18n/get-dictionary';
import { Locale, i18n } from '@/lib/i18n/config';
import { Container } from '@/components/ui/container';
import { TopBannerAd, SidebarAd } from '@/components/ads/client-ad-wrapper';

// 为静态导出配置
export const dynamic = 'force-static';
export const revalidate = 3600; // 每小时重新验证一次

// 为静态导出生成所有支持的语言路径
export function generateStaticParams() {
  return i18n.locales.map((locale) => ({ locale }));
}

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: Locale }>;
}): Promise<Metadata> {
  const locale = (await params).locale;
  const dictionary = await getDictionary(locale);

  return {
    title: `${dictionary.terms?.title || 'Terms of Service'} | X114 AI Tool Hub`,
    description: dictionary.terms?.description || 'By using our services, you agree to comply with the following terms and conditions.',
    keywords: ['terms of service', 'terms and conditions', 'user agreement', 'X114'],
  };
}

export default async function TermsPage({
  params,
}: {
  params: Promise<{ locale: Locale }>;
}) {
  // 在 Next.js 15+ 中，params 是异步的，需要使用 await
  const locale = (await params).locale;
  const dictionary = await getDictionary(locale);

  const terms = dictionary.terms;

  if (!terms) {
    return (
      <Container className="py-24">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl font-bold mb-8">Terms of Service</h1>
          <p>Terms of service content is not available in this language.</p>
        </div>
      </Container>
    );
  }

  return (
    <Container className="py-24">
      {/* 顶部横幅广告 */}
      <TopBannerAd adSlot="1620640133" />

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* 主要内容区域 */}
        <div className="lg:col-span-3">
          <div className="mb-8">
            <h1 className="text-3xl font-bold mb-4">{terms.title}</h1>
            <p className="text-lg text-muted-foreground mb-2">{terms.description}</p>
            <p className="text-sm text-muted-foreground">{terms.lastUpdated}</p>
          </div>

          <div className="space-y-8">
          {/* 条款接受 */}
          <section>
            <h2 className="text-2xl font-semibold mb-4">{terms.sections.acceptance.title}</h2>
            <p className="text-muted-foreground leading-relaxed">{terms.sections.acceptance.content}</p>
          </section>

          {/* 服务描述 */}
          <section>
            <h2 className="text-2xl font-semibold mb-4">{terms.sections.services.title}</h2>
            <p className="text-muted-foreground leading-relaxed">{terms.sections.services.content}</p>
          </section>

          {/* 用户行为 */}
          <section>
            <h2 className="text-2xl font-semibold mb-4">{terms.sections.userConduct.title}</h2>
            <p className="text-muted-foreground leading-relaxed">{terms.sections.userConduct.content}</p>
          </section>

          {/* 知识产权 */}
          <section>
            <h2 className="text-2xl font-semibold mb-4">{terms.sections.intellectualProperty.title}</h2>
            <p className="text-muted-foreground leading-relaxed">{terms.sections.intellectualProperty.content}</p>
          </section>

          {/* 免责声明 */}
          <section>
            <h2 className="text-2xl font-semibold mb-4">{terms.sections.disclaimer.title}</h2>
            <p className="text-muted-foreground leading-relaxed">{terms.sections.disclaimer.content}</p>
          </section>

          {/* 责任限制 */}
          <section>
            <h2 className="text-2xl font-semibold mb-4">{terms.sections.limitation.title}</h2>
            <p className="text-muted-foreground leading-relaxed">{terms.sections.limitation.content}</p>
          </section>

          {/* 服务终止 */}
          <section>
            <h2 className="text-2xl font-semibold mb-4">{terms.sections.termination.title}</h2>
            <p className="text-muted-foreground leading-relaxed">{terms.sections.termination.content}</p>
          </section>

          {/* 条款变更 */}
          <section>
            <h2 className="text-2xl font-semibold mb-4">{terms.sections.changes.title}</h2>
            <p className="text-muted-foreground leading-relaxed">{terms.sections.changes.content}</p>
          </section>

          {/* 联系我们 */}
          <section>
            <h2 className="text-2xl font-semibold mb-4">{terms.sections.contact.title}</h2>
            <p className="text-muted-foreground leading-relaxed">{terms.sections.contact.content}</p>
          </section>
          </div>
        </div>

        {/* 侧边栏 */}
        <div className="lg:col-span-1">
          <SidebarAd adSlot="7890123456" />
        </div>
      </div>
    </Container>
  );
}
