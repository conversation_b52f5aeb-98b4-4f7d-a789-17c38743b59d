'use client';

import { Locale, locales } from '@/lib/i18n/config';
import { useEffect } from 'react';

interface HreflangTagsProps {
  path: string;
  locale: Locale;
  baseUrl: string;
  alternateLanguages?: Record<string, string>;
}

/**
 * 组件用于添加hreflang标签到页面头部
 * 这些标签帮助搜索引擎理解页面的语言版本关系
 */
export default function HreflangTags({ path, locale, baseUrl, alternateLanguages }: HreflangTagsProps) {
  // 使用 useEffect 处理所有 hreflang 标签
  useEffect(() => {
    // 清理函数，移除所有hreflang标签
    const cleanup = () => {
      document.querySelectorAll('link[rel="alternate"][hreflang]').forEach(el => el.remove());
    };

    // 先清理现有标签
    cleanup();

    // 使用自定义的语言映射或默认的locales
    const languagesToProcess = alternateLanguages ||
      Object.fromEntries(locales.map(lang => [lang, `${baseUrl}/${lang}${path}`]));

    // 为每种语言添加 hreflang 标签
    Object.entries(languagesToProcess).forEach(([lang, url]) => {
      const link = document.createElement('link');
      link.rel = 'alternate';
      link.href = url;
      link.setAttribute('hreflang', lang);
      document.head.appendChild(link);
    });

    // 添加 x-default 标签（默认语言版本）
    const defaultLink = document.createElement('link');
    defaultLink.rel = 'alternate';
    defaultLink.href = `${baseUrl}/en${path}`;
    defaultLink.setAttribute('hreflang', 'x-default');
    document.head.appendChild(defaultLink);

    return cleanup;
  }, [baseUrl, path]);

  // 这个组件不渲染任何内容
  return null;
}
