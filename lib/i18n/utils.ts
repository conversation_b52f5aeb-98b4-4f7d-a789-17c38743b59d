import { Locale, defaultLocale, locales } from './config';

/**
 * 从路径中提取语言代码
 * @param pathname 路径
 * @returns 语言代码
 */
export function getLocaleFromPathname(pathname: string): Locale {
  const segments = pathname.split('/');
  const localeSegment = segments[1];

  if (locales.includes(localeSegment as Locale)) {
    return localeSegment as Locale;
  }

  return defaultLocale;
}

/**
 * 将路径转换为特定语言的路径
 * @param path 原始路径
 * @param locale 目标语言
 * @returns 转换后的路径
 */
export function localizeUrl(path: string, locale: Locale): string {
  // 如果路径已经包含语言代码，则替换它
  const pathWithoutLocale = removeLocaleFromPath(path);
  return `/${locale}${pathWithoutLocale}`;
}

/**
 * 从路径中移除语言代码
 * @param path 原始路径
 * @returns 移除语言代码后的路径
 */
export function removeLocaleFromPath(path: string): string {
  // 如果路径以 / 开头，则移除它
  const normalizedPath = path.startsWith('/') ? path.substring(1) : path;

  // 检查第一个段是否是语言代码
  const segments = normalizedPath.split('/');
  if (segments.length > 0 && locales.includes(segments[0] as Locale)) {
    return '/' + segments.slice(1).join('/');
  }

  // 如果路径不包含语言代码，则直接返回
  return path.startsWith('/') ? path : `/${path}`;
}

/**
 * 将语言代码映射到 Intl 格式
 * @param locale 语言代码
 * @returns Intl 格式的语言代码
 */
export function mapLocaleToIntl(locale: Locale): string {
  const mapping: Record<Locale, string> = {
    en: 'en-US',
    zh: 'zh-CN',
    tw: 'zh-TW',
    ko: 'ko-KR',
    ja: 'ja-JP',
    pt: 'pt-PT',
    es: 'es-ES',
    de: 'de-DE',
    fr: 'fr-FR',
    vi: 'vi-VN',
  };

  return mapping[locale];
}

/**
 * 格式化数字
 * @param locale 语言代码
 * @param number 数字
 * @param options 格式化选项
 * @returns 格式化后的数字
 */
export function formatNumber(
  locale: Locale,
  number: number,
  options?: Intl.NumberFormatOptions
): string {
  return new Intl.NumberFormat(mapLocaleToIntl(locale), options).format(number);
}

/**
 * 格式化日期
 * @param locale 语言代码
 * @param date 日期
 * @param options 格式化选项
 * @returns 格式化后的日期
 */
export function formatDate(
  locale: Locale,
  date: Date,
  options?: Intl.DateTimeFormatOptions
): string {
  return new Intl.DateTimeFormat(mapLocaleToIntl(locale), options).format(date);
}

/**
 * 格式化货币
 * @param locale 语言代码
 * @param amount 金额
 * @param currency 货币代码
 * @returns 格式化后的货币
 */
export function formatCurrency(
  locale: Locale,
  amount: number,
  currency: string = 'USD'
): string {
  return new Intl.NumberFormat(mapLocaleToIntl(locale), {
    style: 'currency',
    currency,
  }).format(amount);
}

/**
 * 格式化相对时间
 * @param locale 语言代码
 * @param date 日期
 * @returns 格式化后的相对时间
 */
export function formatRelativeTime(locale: Locale, date: Date): string {
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  // 使用 Intl.RelativeTimeFormat 格式化相对时间
  const rtf = new Intl.RelativeTimeFormat(mapLocaleToIntl(locale), { numeric: 'auto' });

  if (diffInSeconds < 60) {
    return rtf.format(-diffInSeconds, 'second');
  }

  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) {
    return rtf.format(-diffInMinutes, 'minute');
  }

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) {
    return rtf.format(-diffInHours, 'hour');
  }

  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 30) {
    return rtf.format(-diffInDays, 'day');
  }

  const diffInMonths = Math.floor(diffInDays / 30);
  if (diffInMonths < 12) {
    return rtf.format(-diffInMonths, 'month');
  }

  const diffInYears = Math.floor(diffInMonths / 12);
  return rtf.format(-diffInYears, 'year');
}
