-- 创建工具提交表
CREATE TABLE IF NOT EXISTS tool_submissions (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT NOT NULL,
  website TEXT NOT NULL,
  category TEXT NOT NULL,
  email TEXT NOT NULL,
  additional_info TEXT,
  locale TEXT NOT NULL DEFAULT 'en',
  submitted_at TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
  created_at TEXT NOT NULL DEFAULT (datetime('now')),
  updated_at TEXT DEFAULT (datetime('now')),
  reviewed_by TEXT,
  review_notes TEXT,
  approved_at TEXT,
  rejected_at TEXT
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_tool_submissions_status ON tool_submissions(status);
CREATE INDEX IF NOT EXISTS idx_tool_submissions_created_at ON tool_submissions(created_at);
CREATE INDEX IF NOT EXISTS idx_tool_submissions_email ON tool_submissions(email);
CREATE INDEX IF NOT EXISTS idx_tool_submissions_locale ON tool_submissions(locale);
CREATE INDEX IF NOT EXISTS idx_tool_submissions_category ON tool_submissions(category);

-- 创建触发器自动更新 updated_at 字段
CREATE TRIGGER IF NOT EXISTS update_tool_submissions_updated_at
  AFTER UPDATE ON tool_submissions
  FOR EACH ROW
BEGIN
  UPDATE tool_submissions SET updated_at = datetime('now') WHERE id = NEW.id;
END;

-- 插入一些示例数据（可选，用于测试）
-- INSERT INTO tool_submissions (
--   id, name, description, website, category, email, 
--   additional_info, locale, submitted_at, status
-- ) VALUES (
--   'example-1',
--   'AI Assistant Pro',
--   'A powerful AI assistant that helps with various tasks including writing, coding, and analysis.',
--   'https://aiassistantpro.com',
--   'productivity',
--   '<EMAIL>',
--   'This tool has been featured in TechCrunch and has over 100k users.',
--   'en',
--   '2024-01-15T10:30:00Z',
--   'approved'
-- );
