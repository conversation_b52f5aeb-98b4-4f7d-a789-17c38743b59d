import { Metadata } from 'next';
import { getAllTools } from '@/lib/db/tools';
import { getAllCategories } from '@/lib/db/categories';
import { getDictionary } from '@/lib/i18n/get-dictionary';
import { Locale, i18n } from '@/lib/i18n/config';
import ClientToolsPage from '@/components/tools/client-tools-page';
import { generateStaticMetadata } from '@/lib/seo-static';
import ServerJsonLd from '@/components/seo/server-json-ld';

// 为静态导出配置
export const dynamic = 'force-static';
export const revalidate = 3600; // 每小时重新验证一次

// 为静态导出生成所有支持的语言路径
export function generateStaticParams() {
  return i18n.locales.map((locale) => ({ locale }));
}

export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: Locale }>;
}): Promise<Metadata> {
  const locale = (await params).locale;
  const dictionary = await getDictionary(locale);

  return generateStaticMetadata(
    dictionary.seo?.toolsTitle || 'AI Tools Directory',
    dictionary.seo?.toolsDescription || 'Browse our comprehensive directory of AI tools for productivity, design, writing, marketing, and more. Find the perfect solution for your needs.',
    '/tools',
    locale
  );
}

export default async function ToolsPage({
  params,
}: {
  params: Promise<{ locale: Locale }>;
}) {
  // 在 Next.js 15+ 中，params 是异步的，需要使用 await
  const locale = (await params).locale;
  const dictionary = await getDictionary(locale);
  const categories = await getAllCategories(locale);

  // 获取所有工具数据，用于客户端筛选和分页
  const tools = await getAllTools(locale);

  // 生成工具页面的结构化数据
  const toolsJsonLd = {
    '@context': 'https://schema.org',
    '@type': 'CollectionPage',
    name: dictionary.seo?.toolsTitle || 'AI Tools Directory',
    description: dictionary.seo?.toolsDescription || 'Browse our comprehensive directory of AI tools',
    url: `https://x114.org/${locale}/tools`,
    mainEntity: {
      '@type': 'ItemList',
      name: 'AI Tools',
      description: 'Complete directory of AI tools and applications',
      numberOfItems: tools.length,
      itemListElement: tools.slice(0, 20).map((tool, index) => ({ // 只包含前20个工具以避免过大的JSON-LD
        '@type': 'ListItem',
        position: index + 1,
        item: {
          '@type': 'SoftwareApplication',
          '@id': `https://x114.org/${locale}/tool/${tool.slug}`,
          name: tool.name,
          description: tool.description,
          url: `https://x114.org/${locale}/tool/${tool.slug}`,
          applicationCategory: 'AI Application',
          operatingSystem: 'Web',
          ...(tool.image && { image: tool.image }),
        }
      }))
    },
    breadcrumb: {
      '@type': 'BreadcrumbList',
      itemListElement: [
        {
          '@type': 'ListItem',
          position: 1,
          name: 'Home',
          item: `https://x114.org/${locale}`
        },
        {
          '@type': 'ListItem',
          position: 2,
          name: 'Tools',
          item: `https://x114.org/${locale}/tools`
        }
      ]
    }
  };

  return (
    <>
      {/* 结构化数据 */}
      <ServerJsonLd data={toolsJsonLd} id="tools-jsonld" />
      <ClientToolsPage
        tools={tools}
        categories={categories}
        dictionary={dictionary}
        locale={locale}
      />
    </>
  );
}