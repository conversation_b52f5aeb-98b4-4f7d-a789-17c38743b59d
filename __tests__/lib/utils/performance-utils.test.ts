import { renderHook, act } from '@testing-library/react';
import { debounce, throttle, useDebouncedValue } from '@/lib/utils/performance-utils';

// Mock timers
jest.useFakeTimers();

describe('Performance Utils', () => {
  describe('debounce', () => {
    it('should debounce function calls', () => {
      const mockFn = jest.fn();
      const debouncedFn = debounce(mockFn, 500);

      // Call the debounced function multiple times
      debouncedFn();
      debouncedFn();
      debouncedFn();

      // Function should not be called yet
      expect(mockFn).not.toHaveBeenCalled();

      // Fast-forward time
      jest.advanceTimersByTime(500);

      // Function should be called once
      expect(mockFn).toHaveBeenCalledTimes(1);
    });

    it('should reset timer on subsequent calls', () => {
      const mockFn = jest.fn();
      const debouncedFn = debounce(mockFn, 500);

      // Call the debounced function
      debouncedFn();

      // Fast-forward time partially
      jest.advanceTimersByTime(300);

      // Call the debounced function again
      debouncedFn();

      // Fast-forward time partially again
      jest.advanceTimersByTime(300);

      // Function should not be called yet
      expect(mockFn).not.toHaveBeenCalled();

      // Fast-forward remaining time
      jest.advanceTimersByTime(200);

      // Function should be called once
      expect(mockFn).toHaveBeenCalledTimes(1);
    });

    it('should pass arguments to the original function', () => {
      const mockFn = jest.fn();
      const debouncedFn = debounce(mockFn, 500);

      // Call the debounced function with arguments
      debouncedFn('test', 123);

      // Fast-forward time
      jest.advanceTimersByTime(500);

      // Function should be called with the arguments
      expect(mockFn).toHaveBeenCalledWith('test', 123);
    });
  });

  describe('throttle', () => {
    it('should throttle function calls', () => {
      const mockFn = jest.fn();
      const throttledFn = throttle(mockFn, 500);

      // Call the throttled function
      throttledFn();

      // Function should be called immediately
      expect(mockFn).toHaveBeenCalledTimes(1);

      // Call the throttled function again
      throttledFn();
      throttledFn();

      // Function should not be called again yet
      expect(mockFn).toHaveBeenCalledTimes(1);

      // Fast-forward time
      jest.advanceTimersByTime(500);

      // Function should be called again
      expect(mockFn).toHaveBeenCalledTimes(2);
    });

    it('should pass arguments to the original function', () => {
      const mockFn = jest.fn();
      const throttledFn = throttle(mockFn, 500);

      // Call the throttled function with arguments
      throttledFn('test', 123);

      // Function should be called with the arguments
      expect(mockFn).toHaveBeenCalledWith('test', 123);
    });
  });

  describe('useDebouncedValue', () => {
    it('should debounce value changes', () => {
      const { result, rerender } = renderHook(
        ({ value }) => useDebouncedValue(value, 500),
        { initialProps: { value: 'initial' } }
      );

      // Initial value should be set immediately
      expect(result.current).toBe('initial');

      // Change the value
      rerender({ value: 'changed' });

      // Debounced value should not change yet
      expect(result.current).toBe('initial');

      // Fast-forward time
      act(() => {
        jest.advanceTimersByTime(500);
      });

      // Debounced value should be updated
      expect(result.current).toBe('changed');
    });

    it('should cancel previous timer on value change', () => {
      const { result, rerender } = renderHook(
        ({ value }) => useDebouncedValue(value, 500),
        { initialProps: { value: 'initial' } }
      );

      // Change the value
      rerender({ value: 'changed1' });

      // Fast-forward time partially
      act(() => {
        jest.advanceTimersByTime(300);
      });

      // Change the value again
      rerender({ value: 'changed2' });

      // Fast-forward time partially again
      act(() => {
        jest.advanceTimersByTime(300);
      });

      // Debounced value should not be updated to 'changed1'
      expect(result.current).not.toBe('changed1');

      // Fast-forward remaining time
      act(() => {
        jest.advanceTimersByTime(200);
      });

      // Debounced value should be updated to 'changed2'
      expect(result.current).toBe('changed2');
    });
  });
});
