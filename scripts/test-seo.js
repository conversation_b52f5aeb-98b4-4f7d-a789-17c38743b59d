#!/usr/bin/env node

/**
 * SEO测试脚本
 * 用于验证网站的SEO配置是否正确
 */

const fs = require('fs');
const path = require('path');

// 颜色输出
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

// 检查文件是否存在
function checkFileExists(filePath, description) {
  const fullPath = path.join(process.cwd(), filePath);
  if (fs.existsSync(fullPath)) {
    logSuccess(`${description} exists: ${filePath}`);
    return true;
  } else {
    logError(`${description} missing: ${filePath}`);
    return false;
  }
}

// 检查文件内容
function checkFileContent(filePath, patterns, description) {
  const fullPath = path.join(process.cwd(), filePath);
  if (!fs.existsSync(fullPath)) {
    logError(`${description} file not found: ${filePath}`);
    return false;
  }

  const content = fs.readFileSync(fullPath, 'utf8');
  let allPassed = true;

  patterns.forEach(({ pattern, name, required = true }) => {
    const regex = new RegExp(pattern, 'i');
    if (regex.test(content)) {
      logSuccess(`${name} found in ${filePath}`);
    } else {
      if (required) {
        logError(`${name} missing in ${filePath}`);
        allPassed = false;
      } else {
        logWarning(`${name} not found in ${filePath} (optional)`);
      }
    }
  });

  return allPassed;
}

// 主要测试函数
function runSeoTests() {
  log('\n🔍 Starting SEO Configuration Tests\n', 'bold');

  let totalTests = 0;
  let passedTests = 0;

  // 测试1: 检查SEO相关文件是否存在
  log('📁 Checking SEO Files...', 'blue');
  const seoFiles = [
    { path: 'lib/seo.ts', desc: 'Main SEO configuration' },
    { path: 'lib/seo-static.ts', desc: 'Static SEO configuration' },
    { path: 'lib/seo-checker.ts', desc: 'SEO checker utility' },
    { path: 'components/seo/enhanced-seo.tsx', desc: 'Enhanced SEO component' },
    { path: 'components/seo/page-seo.tsx', desc: 'Page SEO component' },
    { path: 'components/seo/canonical-link.tsx', desc: 'Canonical link component' },
    { path: 'components/seo/hreflang-tags.tsx', desc: 'Hreflang tags component' },
    { path: 'app/sitemap.ts', desc: 'Sitemap generator' },
    { path: 'app/robots.ts', desc: 'Robots.txt generator' },
    { path: 'public/browserconfig.xml', desc: 'Browser configuration' },
  ];

  seoFiles.forEach(file => {
    totalTests++;
    if (checkFileExists(file.path, file.desc)) {
      passedTests++;
    }
  });

  // 测试2: 检查SEO配置内容
  log('\n📝 Checking SEO Configuration Content...', 'blue');

  const seoConfigPatterns = [
    { pattern: 'defaultMetadata.*Metadata', name: 'Default metadata export', required: true },
    { pattern: 'generateToolMetadata', name: 'Tool metadata generator', required: true },
    { pattern: 'generateCategoryMetadata', name: 'Category metadata generator', required: true },
    { pattern: 'openGraph', name: 'Open Graph configuration', required: true },
    { pattern: 'twitter', name: 'Twitter card configuration', required: true },
    { pattern: 'canonical:', name: 'Canonical URL configuration', required: true },
    { pattern: 'hreflang', name: 'Hreflang configuration', required: false },
  ];

  totalTests++;
  if (checkFileContent('lib/seo.ts', seoConfigPatterns, 'SEO configuration')) {
    passedTests++;
  }

  // 测试3: 检查sitemap配置
  log('\n🗺️  Checking Sitemap Configuration...', 'blue');

  const sitemapPatterns = [
    { pattern: 'MetadataRoute\\.Sitemap', name: 'Sitemap type import', required: true },
    { pattern: 'changeFrequency', name: 'Change frequency configuration', required: true },
    { pattern: 'priority', name: 'Priority configuration', required: true },
    { pattern: 'lastModified', name: 'Last modified dates', required: true },
    { pattern: 'locales', name: 'Multi-language support', required: true },
  ];

  totalTests++;
  if (checkFileContent('app/sitemap.ts', sitemapPatterns, 'Sitemap')) {
    passedTests++;
  }

  // 测试4: 检查robots.txt配置
  log('\n🤖 Checking Robots.txt Configuration...', 'blue');

  const robotsPatterns = [
    { pattern: 'userAgent', name: 'User agent rules', required: true },
    { pattern: 'allow.*/', name: 'Allow rules', required: true },
    { pattern: 'disallow', name: 'Disallow rules', required: true },
    { pattern: 'sitemap', name: 'Sitemap reference', required: true },
    { pattern: 'Googlebot', name: 'Google-specific rules', required: false },
  ];

  totalTests++;
  if (checkFileContent('app/robots.ts', robotsPatterns, 'Robots.txt')) {
    passedTests++;
  }

  // 测试5: 检查组件导出
  log('\n📦 Checking Component Exports...', 'blue');

  const componentExportPatterns = [
    { pattern: 'export.*CanonicalLink', name: 'CanonicalLink export', required: true },
    { pattern: 'export.*HreflangTags', name: 'HreflangTags export', required: true },
    { pattern: 'export.*DynamicMetadata', name: 'DynamicMetadata export', required: true },
    { pattern: 'export.*StructuredData', name: 'StructuredData export', required: true },
    { pattern: 'export.*EnhancedSeo', name: 'EnhancedSeo export', required: true },
  ];

  totalTests++;
  if (checkFileContent('components/seo/index.ts', componentExportPatterns, 'Component exports')) {
    passedTests++;
  }

  // 测试6: 检查必要的图标文件
  log('\n🖼️  Checking Icon Files...', 'blue');

  const iconFiles = [
    { path: 'public/favicon.ico', desc: 'Favicon' },
    { path: 'public/x114-logo-192x192.png', desc: 'App icon 192x192' },
    { path: 'public/x114-logo-512x512.png', desc: 'App icon 512x512' },
    { path: 'public/site.webmanifest', desc: 'Web manifest' },
  ];

  iconFiles.forEach(file => {
    totalTests++;
    if (checkFileExists(file.path, file.desc)) {
      passedTests++;
    }
  });

  // 输出测试结果
  log('\n📊 Test Results:', 'bold');
  log(`Total tests: ${totalTests}`);
  log(`Passed: ${passedTests}`, passedTests === totalTests ? 'green' : 'yellow');
  log(`Failed: ${totalTests - passedTests}`, totalTests - passedTests === 0 ? 'green' : 'red');

  const successRate = Math.round((passedTests / totalTests) * 100);
  log(`Success rate: ${successRate}%`, successRate >= 90 ? 'green' : successRate >= 70 ? 'yellow' : 'red');

  if (successRate >= 90) {
    log('\n🎉 Excellent! Your SEO configuration is well set up.', 'green');
  } else if (successRate >= 70) {
    log('\n👍 Good! Your SEO configuration is mostly complete, but there are some improvements to make.', 'yellow');
  } else {
    log('\n⚠️  Your SEO configuration needs attention. Please review the failed tests above.', 'red');
  }

  // 提供改进建议
  log('\n💡 SEO Improvement Tips:', 'blue');
  log('1. Ensure all meta tags are properly configured');
  log('2. Add structured data (JSON-LD) to all important pages');
  log('3. Optimize images with proper alt text and sizes');
  log('4. Use descriptive URLs and proper heading hierarchy');
  log('5. Regularly monitor your site with Google Search Console');
  log('6. Test your pages with Google\'s Rich Results Test');
  log('7. Ensure your site is mobile-friendly and fast-loading');

  return successRate >= 70;
}

// 运行测试
if (require.main === module) {
  const success = runSeoTests();
  process.exit(success ? 0 : 1);
}

module.exports = { runSeoTests };
