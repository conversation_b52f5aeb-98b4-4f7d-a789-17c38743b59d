'use client';

import JsonLd from './json-ld';
import { Tool, Category, Article } from '@/lib/types';
import { Locale } from '@/lib/i18n/config';

// 网站基本信息
const siteUrl = 'https://x114.org';
const siteName = 'X114 AI Tool Hub';

interface StructuredDataProps {
  type: 'website' | 'tool' | 'category' | 'article' | 'organization';
  data?: Tool | Category | Article;
  locale?: Locale;
}

/**
 * 通用结构化数据组件
 * 根据类型生成不同的结构化数据
 */
export default function StructuredData({ type, data, locale = 'en' }: StructuredDataProps) {
  let jsonLdData: Record<string, any> | null = null;

  switch (type) {
    case 'website':
      jsonLdData = generateWebsiteJsonLd();
      break;
    case 'organization':
      jsonLdData = generateOrganizationJsonLd();
      break;
    case 'tool':
      if (data && 'pricing' in data) {
        jsonLdData = generateToolJsonLd(data as Tool);
      }
      break;
    case 'category':
      if (data && !('pricing' in data) && !('content' in data)) {
        jsonLdData = generateCategoryJsonLd(data as Category);
      }
      break;
    case 'article':
      if (data && 'content' in data) {
        jsonLdData = generateArticleJsonLd(data as Article);
      }
      break;
  }

  if (!jsonLdData) return null;

  return <JsonLd data={jsonLdData} />;
}

// 生成工具结构化数据
function generateToolJsonLd(tool: Tool) {
  return {
    '@context': 'https://schema.org',
    '@type': 'SoftwareApplication',
    name: tool.name,
    description: tool.description,
    image: tool.image,
    url: tool.website,
    applicationCategory: '',
    operatingSystem: 'Web',
    offers: {
      '@type': 'Offer',
      price: undefined,
      priceCurrency: 'USD',
      availability: 'https://schema.org/OnlineOnly',
    },
    aggregateRating: {
      '@type': 'AggregateRating',
      ratingValue: tool.rating || 0,
      ratingCount: tool.reviewCount || 0,
      bestRating: '5',
      worstRating: '1',
    },
    datePublished: tool.createdAt ? (typeof tool.createdAt === 'string' ? tool.createdAt : tool.createdAt.toISOString()) : new Date().toISOString(),
    dateModified: tool.updatedAt ? (typeof tool.updatedAt === 'string' ? tool.updatedAt : tool.updatedAt.toISOString()) : new Date().toISOString(),
    review: {
      '@type': 'Review',
      reviewRating: {
        '@type': 'Rating',
        ratingValue: tool.rating || 0,
        bestRating: '5',
        worstRating: '1'
      },
      author: {
        '@type': 'Organization',
        name: 'X114'
      },
      publisher: {
        '@type': 'Organization',
        name: 'X114 AI Tool Hub',
        sameAs: 'https://x114.org'
      }
    }
  };
}

// 生成类别结构化数据
function generateCategoryJsonLd(category: Category) {
  return {
    '@context': 'https://schema.org',
    '@type': 'CollectionPage',
    name: category.name,
    description: category.description || `AI tools in the ${category.name} category`,
    url: `${siteUrl}/categories/${category.id}`,
    isPartOf: {
      '@type': 'WebSite',
      name: siteName,
      url: siteUrl
    }
  };
}

// 生成文章结构化数据
function generateArticleJsonLd(article: Article) {
  return {
    '@context': 'https://schema.org',
    '@type': 'Article',
    headline: article.title,
    description: article.summary, // 使用 summary 而不是 description
    image: article.coverImage || '', // 使用 coverImage 而不是 image
    datePublished: article.publishedAt?.toISOString() || new Date().toISOString(),
    dateModified: article.updatedAt?.toISOString() || new Date().toISOString(),
    author: {
      '@type': 'Organization',
      name: article.author || 'X114'
    },
    publisher: {
      '@type': 'Organization',
      name: siteName,
      logo: {
        '@type': 'ImageObject',
        url: `${siteUrl}/x114-logo-192x192.png`
      }
    },
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': `${siteUrl}/articles/${article.slug}`
    }
  };
}

// 生成网站结构化数据
function generateWebsiteJsonLd() {
  return {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: siteName,
    url: siteUrl,
    potentialAction: {
      '@type': 'SearchAction',
      target: `${siteUrl}/search?q={search_term_string}`,
      'query-input': 'required name=search_term_string',
    },
  };
}

// 生成组织结构化数据
function generateOrganizationJsonLd() {
  return {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: 'X114',
    url: siteUrl,
    logo: `${siteUrl}/x114-logo-192x192.png`,
    sameAs: [
      'https://twitter.com/x114ai',
      'https://facebook.com/x114ai'
    ]
  };
}
