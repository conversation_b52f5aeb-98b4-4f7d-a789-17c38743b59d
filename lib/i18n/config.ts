/**
 * 国际化配置
 * 定义支持的语言和相关配置
 */

// 支持的语言类型
export type Locale = 'en' | 'zh' | 'tw' | 'ko' | 'ja' | 'pt' | 'es' | 'de' | 'fr' | 'vi';

// 默认语言
export const defaultLocale: Locale = 'en';

// 支持的语言列表
export const locales: Locale[] = ['en', 'zh', 'tw', 'ko', 'ja', 'pt', 'es', 'de', 'fr', 'vi'];

// 语言名称映射
export const localeNames: Record<Locale, string> = {
  en: 'English',
  zh: '中文简体',
  tw: '中文繁體',
  ko: '한국어',
  ja: '日本語',
  pt: 'Português',
  es: 'Español',
  de: 'Deutsch',
  fr: 'Français',
  vi: 'Tiếng Việt',
};

// 语言方向映射（用于 RTL 支持）
export const localeDirections: Record<Locale, 'ltr' | 'rtl'> = {
  en: 'ltr',
  zh: 'ltr',
  tw: 'ltr',
  ko: 'ltr',
  ja: 'ltr',
  pt: 'ltr',
  es: 'ltr',
  de: 'ltr',
  fr: 'ltr',
  vi: 'ltr',
};

// 语言区域映射（用于格式化）
export const localeRegions: Record<Locale, string> = {
  en: 'US',
  zh: 'CN',
  tw: 'TW',
  ko: 'KR',
  ja: 'JP',
  pt: 'PT',
  es: 'ES',
  de: 'DE',
  fr: 'FR',
  vi: 'VN',
};

// 完整的国际化配置
export const i18n = {
  defaultLocale,
  locales,
  localeNames,
  localeDirections,
  localeRegions,
};