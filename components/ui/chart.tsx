'use client';

import * as React from 'react';

import { cn } from '@/lib/utils';

// 简化版的 Chart 组件，用于静态导出
export type ChartConfig = {
  [k in string]: {
    label?: React.ReactNode;
    icon?: React.ComponentType;
    color?: string;
  };
};

type ChartContextProps = {
  config: ChartConfig;
};

const ChartContext = React.createContext<ChartContextProps | null>(null);

function useChart() {
  const context = React.useContext(ChartContext);

  if (!context) {
    throw new Error('useChart must be used within a <ChartContainer />');
  }

  return context;
}

const ChartContainer = React.forwardRef<
  HTMLDivElement,
  React.ComponentProps<'div'> & {
    config: ChartConfig;
    children: React.ReactNode;
  }
>(({ id, className, children, config, ...props }, ref) => {
  const uniqueId = React.useId();
  const chartId = `chart-${id || uniqueId.replace(/:/g, '')}`;

  return (
    <ChartContext.Provider value={{ config }}>
      <div
        data-chart={chartId}
        ref={ref}
        className={cn(
          "flex aspect-video justify-center text-xs",
          className
        )}
        {...props}
      >
        <div className="w-full h-full flex items-center justify-center">
          <p className="text-muted-foreground">Chart visualization disabled in static export</p>
        </div>
      </div>
    </ChartContext.Provider>
  );
});
ChartContainer.displayName = 'Chart';

// 简化版的 ChartStyle 组件
const ChartStyle = ({ id, config }: { id: string; config: ChartConfig }) => {
  return null;
};

// 简化版的 ChartTooltip 组件
const ChartTooltip = ({ children }: { children?: React.ReactNode }) => {
  return null;
};

// 简化版的 ChartTooltipContent 组件
const ChartTooltipContent = React.forwardRef<
  HTMLDivElement,
  React.ComponentProps<'div'> & {
    hideLabel?: boolean;
    hideIndicator?: boolean;
    indicator?: 'line' | 'dot' | 'dashed';
    nameKey?: string;
    labelKey?: string;
    [key: string]: any;
  }
>(({ className }, ref) => {
  return null;
});
ChartTooltipContent.displayName = 'ChartTooltip';

// 简化版的 ChartLegend 组件
const ChartLegend = ({ children }: { children?: React.ReactNode }) => {
  return null;
};

// 简化版的 ChartLegendContent 组件
const ChartLegendContent = React.forwardRef<
  HTMLDivElement,
  React.ComponentProps<'div'> & {
    hideIcon?: boolean;
    nameKey?: string;
    [key: string]: any;
  }
>(({ className }, ref) => {
  return null;
});
ChartLegendContent.displayName = 'ChartLegend';

export {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent,
  ChartStyle,
};
