// 高级SEO分析工具

export interface SeoAnalysisResult {
  score: number;
  grade: 'A+' | 'A' | 'B+' | 'B' | 'C+' | 'C' | 'D' | 'F';
  issues: SeoIssue[];
  recommendations: SeoRecommendation[];
  metrics: SeoMetrics;
  competitorAnalysis?: CompetitorAnalysis;
}

export interface SeoIssue {
  type: 'critical' | 'warning' | 'info';
  category: 'technical' | 'content' | 'performance' | 'accessibility' | 'mobile';
  message: string;
  impact: 'high' | 'medium' | 'low';
  fix: string;
  priority: number;
}

export interface SeoRecommendation {
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  effort: 'low' | 'medium' | 'high';
  category: string;
  actionItems: string[];
}

export interface SeoMetrics {
  titleLength: number;
  descriptionLength: number;
  headingStructure: HeadingAnalysis;
  imageOptimization: ImageAnalysis;
  linkAnalysis: LinkAnalysis;
  contentAnalysis: ContentAnalysis;
  technicalSeo: TechnicalSeoAnalysis;
}

export interface HeadingAnalysis {
  h1Count: number;
  h2Count: number;
  h3Count: number;
  hasProperHierarchy: boolean;
  missingHeadings: string[];
}

export interface ImageAnalysis {
  totalImages: number;
  imagesWithAlt: number;
  imagesWithoutAlt: number;
  largeImages: number;
  optimizedImages: number;
}

export interface LinkAnalysis {
  internalLinks: number;
  externalLinks: number;
  brokenLinks: number;
  noFollowLinks: number;
}

export interface ContentAnalysis {
  wordCount: number;
  readabilityScore: number;
  keywordDensity: Record<string, number>;
  duplicateContent: boolean;
  contentQuality: 'excellent' | 'good' | 'fair' | 'poor';
}

export interface TechnicalSeoAnalysis {
  hasCanonical: boolean;
  hasHreflang: boolean;
  hasStructuredData: boolean;
  hasRobotsMeta: boolean;
  hasSitemap: boolean;
  mobileOptimized: boolean;
  pageSpeed: number;
  coreWebVitals: CoreWebVitals;
}

export interface CoreWebVitals {
  lcp: number; // Largest Contentful Paint
  fid: number; // First Input Delay
  cls: number; // Cumulative Layout Shift
}

export interface CompetitorAnalysis {
  competitors: string[];
  averageScore: number;
  strengths: string[];
  opportunities: string[];
}

export class SeoAnalyzer {
  private issues: SeoIssue[] = [];
  private recommendations: SeoRecommendation[] = [];

  // 分析页面SEO
  async analyzePage(pageData: {
    url: string;
    title?: string;
    description?: string;
    content?: string;
    html?: string;
    metadata?: any;
  }): Promise<SeoAnalysisResult> {
    this.issues = [];
    this.recommendations = [];

    // 分析各个方面
    const metrics = await this.analyzeMetrics(pageData);
    await this.analyzeTechnicalSeo(pageData);
    await this.analyzeContent(pageData);
    await this.analyzePerformance(pageData);
    await this.analyzeAccessibility(pageData);

    // 计算总分
    const score = this.calculateScore();
    const grade = this.getGrade(score);

    // 生成建议
    this.generateRecommendations();

    return {
      score,
      grade,
      issues: this.issues,
      recommendations: this.recommendations,
      metrics,
    };
  }

  // 分析指标
  private async analyzeMetrics(pageData: any): Promise<SeoMetrics> {
    const titleLength = pageData.title?.length || 0;
    const descriptionLength = pageData.description?.length || 0;

    // 标题分析
    if (titleLength === 0) {
      this.addIssue('critical', 'content', 'Missing page title', 'high', 'Add a descriptive page title', 10);
    } else if (titleLength < 30) {
      this.addIssue('warning', 'content', 'Title too short', 'medium', 'Expand title to 30-60 characters', 7);
    } else if (titleLength > 60) {
      this.addIssue('warning', 'content', 'Title too long', 'medium', 'Shorten title to under 60 characters', 6);
    }

    // 描述分析
    if (descriptionLength === 0) {
      this.addIssue('critical', 'content', 'Missing meta description', 'high', 'Add a compelling meta description', 9);
    } else if (descriptionLength < 120) {
      this.addIssue('warning', 'content', 'Description too short', 'medium', 'Expand description to 120-160 characters', 5);
    } else if (descriptionLength > 160) {
      this.addIssue('warning', 'content', 'Description too long', 'medium', 'Shorten description to under 160 characters', 5);
    }

    return {
      titleLength,
      descriptionLength,
      headingStructure: this.analyzeHeadings(pageData.html),
      imageOptimization: this.analyzeImages(pageData.html),
      linkAnalysis: this.analyzeLinks(pageData.html),
      contentAnalysis: this.analyzeContentQuality(pageData.content),
      technicalSeo: await this.analyzeTechnicalAspects(pageData),
    };
  }

  // 分析标题结构
  private analyzeHeadings(html?: string): HeadingAnalysis {
    if (!html) {
      return {
        h1Count: 0,
        h2Count: 0,
        h3Count: 0,
        hasProperHierarchy: false,
        missingHeadings: ['H1', 'H2'],
      };
    }

    const h1Count = (html.match(/<h1[^>]*>/gi) || []).length;
    const h2Count = (html.match(/<h2[^>]*>/gi) || []).length;
    const h3Count = (html.match(/<h3[^>]*>/gi) || []).length;

    const missingHeadings: string[] = [];
    if (h1Count === 0) missingHeadings.push('H1');
    if (h1Count > 1) {
      this.addIssue('warning', 'content', 'Multiple H1 tags found', 'medium', 'Use only one H1 tag per page', 6);
    }
    if (h2Count === 0 && h3Count > 0) missingHeadings.push('H2');

    return {
      h1Count,
      h2Count,
      h3Count,
      hasProperHierarchy: h1Count === 1 && (h2Count > 0 || h3Count === 0),
      missingHeadings,
    };
  }

  // 分析图片
  private analyzeImages(html?: string): ImageAnalysis {
    if (!html) {
      return {
        totalImages: 0,
        imagesWithAlt: 0,
        imagesWithoutAlt: 0,
        largeImages: 0,
        optimizedImages: 0,
      };
    }

    const images = html.match(/<img[^>]*>/gi) || [];
    const totalImages = images.length;
    let imagesWithAlt = 0;
    let imagesWithoutAlt = 0;

    images.forEach(img => {
      if (img.includes('alt=')) {
        imagesWithAlt++;
      } else {
        imagesWithoutAlt++;
      }
    });

    if (imagesWithoutAlt > 0) {
      this.addIssue('warning', 'accessibility', `${imagesWithoutAlt} images missing alt text`, 'medium', 'Add descriptive alt text to all images', 7);
    }

    return {
      totalImages,
      imagesWithAlt,
      imagesWithoutAlt,
      largeImages: 0, // 需要实际检查图片大小
      optimizedImages: 0, // 需要实际检查图片优化
    };
  }

  // 分析链接
  private analyzeLinks(html?: string): LinkAnalysis {
    if (!html) {
      return {
        internalLinks: 0,
        externalLinks: 0,
        brokenLinks: 0,
        noFollowLinks: 0,
      };
    }

    const links = html.match(/<a[^>]*href=[^>]*>/gi) || [];
    let internalLinks = 0;
    let externalLinks = 0;
    let noFollowLinks = 0;

    links.forEach(link => {
      if (link.includes('href="http') && !link.includes('x114.org')) {
        externalLinks++;
      } else {
        internalLinks++;
      }
      
      if (link.includes('rel="nofollow"')) {
        noFollowLinks++;
      }
    });

    return {
      internalLinks,
      externalLinks,
      brokenLinks: 0, // 需要实际检查链接状态
      noFollowLinks,
    };
  }

  // 分析内容质量
  private analyzeContentQuality(content?: string): ContentAnalysis {
    if (!content) {
      return {
        wordCount: 0,
        readabilityScore: 0,
        keywordDensity: {},
        duplicateContent: false,
        contentQuality: 'poor',
      };
    }

    const wordCount = content.split(/\s+/).length;
    
    if (wordCount < 300) {
      this.addIssue('warning', 'content', 'Content too short', 'medium', 'Add more valuable content (aim for 300+ words)', 6);
    }

    return {
      wordCount,
      readabilityScore: this.calculateReadabilityScore(content),
      keywordDensity: this.calculateKeywordDensity(content),
      duplicateContent: false,
      contentQuality: wordCount > 500 ? 'good' : wordCount > 300 ? 'fair' : 'poor',
    };
  }

  // 分析技术SEO
  private async analyzeTechnicalAspects(pageData: any): Promise<TechnicalSeoAnalysis> {
    const hasCanonical = pageData.html?.includes('rel="canonical"') || false;
    const hasHreflang = pageData.html?.includes('hreflang=') || false;
    const hasStructuredData = pageData.html?.includes('application/ld+json') || false;
    const hasRobotsMeta = pageData.html?.includes('name="robots"') || false;

    if (!hasCanonical) {
      this.addIssue('warning', 'technical', 'Missing canonical URL', 'medium', 'Add canonical link tag', 7);
    }
    if (!hasStructuredData) {
      this.addIssue('warning', 'technical', 'Missing structured data', 'medium', 'Add JSON-LD structured data', 6);
    }

    return {
      hasCanonical,
      hasHreflang,
      hasStructuredData,
      hasRobotsMeta,
      hasSitemap: true, // 假设有sitemap
      mobileOptimized: true, // 需要实际检查
      pageSpeed: 85, // 需要实际测量
      coreWebVitals: {
        lcp: 2.5,
        fid: 100,
        cls: 0.1,
      },
    };
  }

  // 计算可读性分数
  private calculateReadabilityScore(content: string): number {
    // 简化的可读性计算
    const sentences = content.split(/[.!?]+/).length;
    const words = content.split(/\s+/).length;
    const avgWordsPerSentence = words / sentences;
    
    // 基于平均句子长度的简单评分
    if (avgWordsPerSentence < 15) return 90;
    if (avgWordsPerSentence < 20) return 80;
    if (avgWordsPerSentence < 25) return 70;
    return 60;
  }

  // 计算关键词密度
  private calculateKeywordDensity(content: string): Record<string, number> {
    const words = content.toLowerCase().split(/\s+/);
    const wordCount: Record<string, number> = {};
    
    words.forEach(word => {
      word = word.replace(/[^\w]/g, '');
      if (word.length > 3) {
        wordCount[word] = (wordCount[word] || 0) + 1;
      }
    });

    const totalWords = words.length;
    const density: Record<string, number> = {};
    
    Object.entries(wordCount).forEach(([word, count]) => {
      density[word] = (count / totalWords) * 100;
    });

    return density;
  }

  // 添加问题
  private addIssue(
    type: SeoIssue['type'],
    category: SeoIssue['category'],
    message: string,
    impact: SeoIssue['impact'],
    fix: string,
    priority: number
  ) {
    this.issues.push({
      type,
      category,
      message,
      impact,
      fix,
      priority,
    });
  }

  // 计算总分
  private calculateScore(): number {
    let score = 100;
    
    this.issues.forEach(issue => {
      switch (issue.type) {
        case 'critical':
          score -= issue.priority;
          break;
        case 'warning':
          score -= issue.priority * 0.7;
          break;
        case 'info':
          score -= issue.priority * 0.3;
          break;
      }
    });

    return Math.max(0, Math.round(score));
  }

  // 获取等级
  private getGrade(score: number): SeoAnalysisResult['grade'] {
    if (score >= 95) return 'A+';
    if (score >= 90) return 'A';
    if (score >= 85) return 'B+';
    if (score >= 80) return 'B';
    if (score >= 75) return 'C+';
    if (score >= 70) return 'C';
    if (score >= 60) return 'D';
    return 'F';
  }

  // 生成建议
  private generateRecommendations() {
    // 基于问题生成建议
    const criticalIssues = this.issues.filter(i => i.type === 'critical');
    const warningIssues = this.issues.filter(i => i.type === 'warning');

    if (criticalIssues.length > 0) {
      this.recommendations.push({
        title: 'Fix Critical SEO Issues',
        description: 'Address critical issues that significantly impact SEO performance',
        impact: 'high',
        effort: 'low',
        category: 'Priority',
        actionItems: criticalIssues.map(i => i.fix),
      });
    }

    if (warningIssues.length > 0) {
      this.recommendations.push({
        title: 'Improve SEO Warnings',
        description: 'Address warning issues to further optimize SEO performance',
        impact: 'medium',
        effort: 'medium',
        category: 'Optimization',
        actionItems: warningIssues.slice(0, 5).map(i => i.fix),
      });
    }
  }

  // 分析技术SEO
  private async analyzeTechnicalSeo(pageData: any) {
    // 实现技术SEO分析
  }

  // 分析内容
  private async analyzeContent(pageData: any) {
    // 实现内容分析
  }

  // 分析性能
  private async analyzePerformance(pageData: any) {
    // 实现性能分析
  }

  // 分析可访问性
  private async analyzeAccessibility(pageData: any) {
    // 实现可访问性分析
  }
}
