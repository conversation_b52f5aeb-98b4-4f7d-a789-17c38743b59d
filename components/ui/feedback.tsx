'use client';

import React, { useState, useEffect } from 'react';
import { Check, Copy, Heart, Star, ThumbsUp, ThumbsDown, Share2, Bookmark } from 'lucide-react';
import { Button } from './button';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

// 复制到剪贴板组件
interface CopyToClipboardProps {
  text: string;
  children?: React.ReactNode;
  className?: string;
  showFeedback?: boolean;
  feedbackText?: string;
}

export function CopyToClipboard({
  text,
  children,
  className,
  showFeedback = true,
  feedbackText = 'Copied!'
}: CopyToClipboardProps) {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      
      if (showFeedback) {
        toast.success(feedbackText);
      }

      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy text: ', err);
      toast.error('Failed to copy');
    }
  };

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={handleCopy}
      className={cn('h-auto p-2', className)}
    >
      {copied ? (
        <Check className="h-4 w-4 text-green-600" />
      ) : (
        <Copy className="h-4 w-4" />
      )}
      {children}
    </Button>
  );
}

// 点赞/收藏组件
interface LikeButtonProps {
  isLiked?: boolean;
  count?: number;
  onToggle?: (liked: boolean) => void;
  variant?: 'like' | 'heart' | 'star' | 'bookmark';
  size?: 'sm' | 'md' | 'lg';
  showCount?: boolean;
  className?: string;
}

export function LikeButton({
  isLiked = false,
  count = 0,
  onToggle,
  variant = 'like',
  size = 'md',
  showCount = true,
  className
}: LikeButtonProps) {
  const [liked, setLiked] = useState(isLiked);
  const [currentCount, setCurrentCount] = useState(count);

  const icons = {
    like: ThumbsUp,
    heart: Heart,
    star: Star,
    bookmark: Bookmark
  };

  const sizes = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6'
  };

  const IconComponent = icons[variant];

  const handleToggle = () => {
    const newLiked = !liked;
    setLiked(newLiked);
    setCurrentCount(prev => newLiked ? prev + 1 : prev - 1);
    
    onToggle?.(newLiked);

    // 提供触觉反馈（如果支持）
    if ('vibrate' in navigator) {
      navigator.vibrate(50);
    }
  };

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={handleToggle}
      className={cn(
        'flex items-center space-x-1 transition-all duration-200',
        liked && 'text-red-500',
        className
      )}
    >
      <IconComponent 
        className={cn(
          sizes[size],
          'transition-all duration-200',
          liked && variant === 'heart' && 'fill-current',
          liked && variant === 'star' && 'fill-current text-yellow-500',
          liked && variant === 'bookmark' && 'fill-current text-blue-500'
        )} 
      />
      {showCount && (
        <span className="text-sm font-medium">
          {currentCount}
        </span>
      )}
    </Button>
  );
}

// 评分组件
interface RatingProps {
  value?: number;
  max?: number;
  onChange?: (rating: number) => void;
  readonly?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function Rating({
  value = 0,
  max = 5,
  onChange,
  readonly = false,
  size = 'md',
  className
}: RatingProps) {
  const [rating, setRating] = useState(value);
  const [hoverRating, setHoverRating] = useState(0);

  const sizes = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6'
  };

  const handleClick = (newRating: number) => {
    if (readonly) return;
    
    setRating(newRating);
    onChange?.(newRating);
    
    // 提供触觉反馈
    if ('vibrate' in navigator) {
      navigator.vibrate(30);
    }
  };

  return (
    <div className={cn('flex items-center space-x-1', className)}>
      {Array.from({ length: max }, (_, i) => {
        const starValue = i + 1;
        const isFilled = starValue <= (hoverRating || rating);
        
        return (
          <button
            key={i}
            type="button"
            onClick={() => handleClick(starValue)}
            onMouseEnter={() => !readonly && setHoverRating(starValue)}
            onMouseLeave={() => !readonly && setHoverRating(0)}
            disabled={readonly}
            className={cn(
              'transition-all duration-200',
              !readonly && 'hover:scale-110 cursor-pointer',
              readonly && 'cursor-default'
            )}
          >
            <Star
              className={cn(
                sizes[size],
                'transition-colors duration-200',
                isFilled ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'
              )}
            />
          </button>
        );
      })}
    </div>
  );
}

// 分享按钮
interface ShareButtonProps {
  url?: string;
  title?: string;
  text?: string;
  className?: string;
}

export function ShareButton({
  url = typeof window !== 'undefined' ? window.location.href : '',
  title = 'Check this out!',
  text = '',
  className
}: ShareButtonProps) {
  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title,
          text,
          url
        });
      } catch (err) {
        console.error('Error sharing:', err);
      }
    } else {
      // 回退到复制链接
      try {
        await navigator.clipboard.writeText(url);
        toast.success('Link copied to clipboard!');
      } catch (err) {
        console.error('Failed to copy link:', err);
        toast.error('Failed to copy link');
      }
    }
  };

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={handleShare}
      className={cn('flex items-center space-x-1', className)}
    >
      <Share2 className="h-4 w-4" />
      <span>Share</span>
    </Button>
  );
}

// 反馈按钮组
interface FeedbackButtonsProps {
  onPositive?: () => void;
  onNegative?: () => void;
  positiveText?: string;
  negativeText?: string;
  className?: string;
}

export function FeedbackButtons({
  onPositive,
  onNegative,
  positiveText = 'Helpful',
  negativeText = 'Not helpful',
  className
}: FeedbackButtonsProps) {
  const [feedback, setFeedback] = useState<'positive' | 'negative' | null>(null);

  const handlePositive = () => {
    setFeedback('positive');
    onPositive?.();
    toast.success('Thanks for your feedback!');
  };

  const handleNegative = () => {
    setFeedback('negative');
    onNegative?.();
    toast.success('Thanks for your feedback!');
  };

  return (
    <div className={cn('flex items-center space-x-2', className)}>
      <span className="text-sm text-muted-foreground">Was this helpful?</span>
      <Button
        variant={feedback === 'positive' ? 'default' : 'ghost'}
        size="sm"
        onClick={handlePositive}
        disabled={feedback !== null}
        className="flex items-center space-x-1"
      >
        <ThumbsUp className="h-4 w-4" />
        <span>{positiveText}</span>
      </Button>
      <Button
        variant={feedback === 'negative' ? 'destructive' : 'ghost'}
        size="sm"
        onClick={handleNegative}
        disabled={feedback !== null}
        className="flex items-center space-x-1"
      >
        <ThumbsDown className="h-4 w-4" />
        <span>{negativeText}</span>
      </Button>
    </div>
  );
}

// 进度指示器
interface ProgressFeedbackProps {
  value: number;
  max?: number;
  showPercentage?: boolean;
  showAnimation?: boolean;
  className?: string;
}

export function ProgressFeedback({
  value,
  max = 100,
  showPercentage = true,
  showAnimation = true,
  className
}: ProgressFeedbackProps) {
  const percentage = Math.round((value / max) * 100);

  return (
    <div className={cn('space-y-2', className)}>
      <div className="flex justify-between items-center">
        <span className="text-sm text-muted-foreground">Progress</span>
        {showPercentage && (
          <span className="text-sm font-medium">{percentage}%</span>
        )}
      </div>
      <div className="w-full bg-muted rounded-full h-2">
        <div
          className={cn(
            'bg-primary h-2 rounded-full transition-all duration-500',
            showAnimation && 'ease-out'
          )}
          style={{ width: `${percentage}%` }}
        />
      </div>
    </div>
  );
}
