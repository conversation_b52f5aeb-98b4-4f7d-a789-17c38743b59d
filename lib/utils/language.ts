import { locales, defaultLocale, localeNames } from '@/lib/i18n/config';

/**
 * 支持的语言列表
 */
export const SUPPORTED_LANGUAGES = locales;

/**
 * 默认语言
 */
export const DEFAULT_LANGUAGE = defaultLocale;

/**
 * 语言名称映射
 */
export const LANGUAGE_NAMES: Record<string, string> = localeNames;

/**
 * 检查语言是否受支持
 * @param language 语言代码
 * @returns 是否受支持
 */
export function isLanguageSupported(language: string): boolean {
  return SUPPORTED_LANGUAGES.includes(language as any);
}

/**
 * 获取有效的语言代码
 * @param language 语言代码
 * @returns 有效的语言代码，如果不受支持则返回默认语言
 */
export function getValidLanguage(language?: string): string {
  if (!language || !isLanguageSupported(language)) {
    return DEFAULT_LANGUAGE;
  }
  return language;
}

/**
 * 从请求中获取语言
 * @param req 请求对象
 * @returns 语言代码
 */
export function getLanguageFromRequest(req: { url: string; headers: { get: (name: string) => string | null } }): string {
  // 从 URL 参数中获取语言
  const url = new URL(req.url);
  const langParam = url.searchParams.get('lang');

  if (langParam && isLanguageSupported(langParam)) {
    return langParam;
  }

  // 从 Accept-Language 头中获取语言
  const acceptLanguage = req.headers.get('Accept-Language');
  if (acceptLanguage) {
    // 解析 Accept-Language 头
    const languages = acceptLanguage.split(',')
      .map((lang: string) => {
        const [code, q = '1'] = lang.trim().split(';q=');
        return { code: code.split('-')[0], q: parseFloat(q) };
      })
      .sort((a: { q: number }, b: { q: number }) => b.q - a.q);

    // 查找第一个受支持的语言
    const supportedLang = languages.find((lang: { code: string }) => isLanguageSupported(lang.code));
    if (supportedLang) {
      return supportedLang.code;
    }
  }

  // 默认返回英语
  return DEFAULT_LANGUAGE;
}

/**
 * 获取语言的本地化名称
 * @param language 语言代码
 * @returns 语言的本地化名称
 */
export function getLanguageName(language: string): string {
  return LANGUAGE_NAMES[language] || LANGUAGE_NAMES[DEFAULT_LANGUAGE];
}

/**
 * 获取当前语言的切换链接
 * @param currentUrl 当前 URL
 * @param targetLanguage 目标语言
 * @returns 切换语言的 URL
 */
export function getLanguageSwitchUrl(currentUrl: string, targetLanguage: string): string {
  const url = new URL(currentUrl);
  url.searchParams.set('lang', targetLanguage);
  return url.toString();
}
