'use client';

import { useEffect, useState, useCallback, useRef } from 'react';

/**
 * 防抖函数
 * @param fn 要防抖的函数
 * @param delay 延迟时间（毫秒）
 * @returns 防抖后的函数
 */
export function debounce<T extends (...args: any[]) => any>(
  fn: T,
  delay: number = 300
): (...args: Parameters<T>) => void {
  let timer: NodeJS.Timeout | null = null;

  return function(...args: Parameters<T>) {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      fn(...args);
    }, delay);
  };
}

/**
 * 节流函数
 * @param fn 要节流的函数
 * @param limit 限制时间（毫秒）
 * @returns 节流后的函数
 */
export function throttle<T extends (...args: any[]) => any>(
  fn: T,
  limit: number = 300
): (...args: Parameters<T>) => void {
  let inThrottle: boolean = false;
  let lastFunc: NodeJS.Timeout | null = null;
  let lastRan: number = 0;

  return function(...args: Parameters<T>) {
    if (!inThrottle) {
      fn(...args);
      lastRan = Date.now();
      inThrottle = true;
    } else {
      if (lastFunc) clearTimeout(lastFunc);
      lastFunc = setTimeout(() => {
        if (Date.now() - lastRan >= limit) {
          fn(...args);
          lastRan = Date.now();
        }
      }, limit - (Date.now() - lastRan));
    }
  };
}

/**
 * 防抖 Hook
 * @param fn 要防抖的函数
 * @param delay 延迟时间（毫秒）
 * @param deps 依赖项
 * @returns 防抖后的函数
 */
export function useDebounce<T extends (...args: any[]) => any>(
  fn: T,
  delay: number = 300,
  deps: any[] = []
): (...args: Parameters<T>) => void {
  const callback = useCallback(fn, deps);

  // 使用内联函数而不是直接传递 debounce 的结果
  return useCallback(() => {
    const debouncedFn = debounce((...args: Parameters<T>) => {
      callback(...args);
    }, delay);
    return debouncedFn;
  }, [callback, delay])();
}

/**
 * 节流 Hook
 * @param fn 要节流的函数
 * @param limit 限制时间（毫秒）
 * @param deps 依赖项
 * @returns 节流后的函数
 */
export function useThrottle<T extends (...args: any[]) => any>(
  fn: T,
  limit: number = 300,
  deps: any[] = []
): (...args: Parameters<T>) => void {
  const callback = useCallback(fn, deps);

  // 使用内联函数而不是直接传递 throttle 的结果
  return useCallback(() => {
    const throttledFn = throttle((...args: Parameters<T>) => {
      callback(...args);
    }, limit);
    return throttledFn;
  }, [callback, limit])();
}

/**
 * 防抖值 Hook
 * @param value 要防抖的值
 * @param delay 延迟时间（毫秒）
 * @returns 防抖后的值
 */
export function useDebouncedValue<T>(value: T, delay: number = 300): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(timer);
    };
  }, [value, delay]);

  return debouncedValue;
}

/**
 * 懒加载 Hook
 * @param threshold 阈值
 * @returns [ref, isInView] ref 是要观察的元素的引用，isInView 表示元素是否在视口中
 */
export function useLazyLoad(threshold: number = 0.1): [React.RefObject<HTMLElement>, boolean] {
  const ref = useRef<HTMLElement>(null);
  const [isInView, setIsInView] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsInView(entry.isIntersecting);
      },
      { threshold }
    );

    const currentRef = ref.current;
    if (currentRef) {
      observer.observe(currentRef);
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, [threshold]);

  return [ref, isInView];
}

/**
 * 测量性能 Hook
 * @param componentName 组件名称
 */
export function usePerformanceMeasure(componentName: string): void {
  useEffect(() => {
    const startTime = performance.now();

    return () => {
      const endTime = performance.now();
      console.log(`[Performance] ${componentName} rendered in ${endTime - startTime}ms`);
    };
  }, [componentName]);
}

/**
 * 缓存数据 Hook
 * @param key 缓存键
 * @param initialData 初始数据
 * @param expirationTime 过期时间（毫秒）
 * @returns [data, setData] data 是缓存的数据，setData 是设置数据的函数
 */
export function useCachedData<T>(
  key: string,
  initialData: T,
  expirationTime: number = 60 * 60 * 1000 // 默认 1 小时
): [T, (data: T) => void] {
  const [data, setData] = useState<T>(() => {
    if (typeof window === 'undefined') return initialData;

    try {
      const item = window.localStorage.getItem(`cache_${key}`);
      if (!item) return initialData;

      const { value, timestamp } = JSON.parse(item);

      // 检查是否过期
      if (Date.now() - timestamp > expirationTime) {
        window.localStorage.removeItem(`cache_${key}`);
        return initialData;
      }

      return value as T;
    } catch (error) {
      console.error('Error reading from localStorage:', error);
      return initialData;
    }
  });

  const setCachedData = useCallback((value: T) => {
    setData(value);

    try {
      window.localStorage.setItem(
        `cache_${key}`,
        JSON.stringify({
          value,
          timestamp: Date.now()
        })
      );
    } catch (error) {
      console.error('Error writing to localStorage:', error);
    }
  }, [key]);

  return [data, setCachedData];
}
