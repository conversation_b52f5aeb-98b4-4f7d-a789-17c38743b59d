import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import ClientToolsPage from '@/components/tools/client-tools-page';
import { Tool, Category } from '@/lib/types';

// Mock Next.js router
const mockPush = jest.fn();
const mockSearchParams = new Map();

jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
  }),
  useSearchParams: () => ({
    get: (key: string) => mockSearchParams.get(key) || null,
    toString: () => '',
  }),
}));

// Mock data
const mockTools: Tool[] = [
  {
    id: '1',
    name: 'ChatGPT',
    description: 'AI-powered conversational assistant',
    tags: ['AI', 'Chat', 'Assistant'],
    rating: 4.8,
    monthlyVisits: 1000000,
    createdAt: '2023-01-01',
    slug: 'chatgpt',
    url: 'https://chat.openai.com',
    language: 'en',
    groups: [{ id: '1', slug: 'ai-assistant', name: 'AI Assistant' }]
  },
  {
    id: '2',
    name: 'Midjourney',
    description: 'AI image generation tool',
    tags: ['AI', 'Image', 'Art'],
    rating: 4.6,
    monthlyVisits: 500000,
    createdAt: '2023-02-01',
    slug: 'midjourney',
    url: 'https://midjourney.com',
    language: 'en',
    groups: [{ id: '2', slug: 'image-generation', name: 'Image Generation' }]
  }
];

const mockCategories: Category[] = [
  {
    id: '1',
    name: 'AI Assistant',
    slug: 'ai-assistant',
    description: 'AI-powered assistants',
    icon: '🤖',
    color: '#3B82F6',
    toolCount: 1,
    language: 'en'
  },
  {
    id: '2',
    name: 'Image Generation',
    slug: 'image-generation',
    description: 'AI image generation tools',
    icon: '🎨',
    color: '#8B5CF6',
    toolCount: 1,
    language: 'en'
  }
];

const mockDictionary = {
  tools: {
    title: 'AI Tools Directory',
    description: 'Discover and compare the best AI tools',
    filters: 'Filters',
    categories: 'Categories',
    allCategories: 'All Categories',
    sortBy: 'Sort By',
    highestRated: 'Highest Rated',
    mostPopular: 'Most Popular',
    newestFirst: 'Newest First',
    resetFilters: 'Reset Filters',
    noToolsFound: 'No tools found',
    noToolsFoundDescription: 'Try adjusting your filters or search query.',
    totalTools: '{total} tools total',
    foundTools: 'Found {found} of {total} tools',
    clearFilters: 'Clear Filters'
  },
  common: {
    searchPlaceholder: 'Search AI tools...'
  }
};

describe('ClientToolsPage', () => {
  const defaultProps = {
    tools: mockTools,
    categories: mockCategories,
    dictionary: mockDictionary,
    locale: 'en'
  };

  it('renders the page title and description', () => {
    render(<ClientToolsPage {...defaultProps} />);

    expect(screen.getByText('AI Tools Directory')).toBeInTheDocument();
    expect(screen.getByText('Discover and compare the best AI tools')).toBeInTheDocument();
  });

  it('displays all tools by default', () => {
    render(<ClientToolsPage {...defaultProps} />);

    expect(screen.getByText('ChatGPT')).toBeInTheDocument();
    expect(screen.getByText('Midjourney')).toBeInTheDocument();
    expect(screen.getByText('2 tools total')).toBeInTheDocument();
  });

  it('filters tools by search query', async () => {
    render(<ClientToolsPage {...defaultProps} />);

    const searchInput = screen.getByPlaceholderText('Search AI tools...');

    // 模拟用户输入
    fireEvent.change(searchInput, { target: { value: 'ChatGPT' } });

    // 等待筛选结果更新 - 直接检查结果而不是输入值
    await waitFor(() => {
      expect(screen.getByText('Found 1 of 2 tools')).toBeInTheDocument();
    }, { timeout: 3000 });

    // 验证只显示匹配的工具
    expect(screen.getByText('ChatGPT')).toBeInTheDocument();
    expect(screen.queryByText('Midjourney')).not.toBeInTheDocument();
  });

  it('filters tools by category', async () => {
    render(<ClientToolsPage {...defaultProps} />);

    const aiAssistantCategory = screen.getByText('AI Assistant');
    fireEvent.click(aiAssistantCategory);

    await waitFor(() => {
      expect(screen.getByText('ChatGPT')).toBeInTheDocument();
      expect(screen.queryByText('Midjourney')).not.toBeInTheDocument();
      expect(screen.getByText('Found 1 of 2 tools')).toBeInTheDocument();
    });
  });

  it('sorts tools correctly', async () => {
    render(<ClientToolsPage {...defaultProps} />);

    // Test sorting by rating (default should show ChatGPT first as it has higher rating)
    const toolCards = screen.getAllByText(/ChatGPT|Midjourney/);
    expect(toolCards[0]).toHaveTextContent('ChatGPT');
  });

  it('resets filters when reset button is clicked', async () => {
    render(<ClientToolsPage {...defaultProps} />);

    // Apply a search filter
    const searchInput = screen.getByPlaceholderText('Search AI tools...');
    fireEvent.change(searchInput, { target: { value: 'ChatGPT' } });

    await waitFor(() => {
      expect(screen.getByText('Found 1 of 2 tools')).toBeInTheDocument();
    }, { timeout: 3000 });

    // Reset filters
    const clearButton = screen.getByText('Clear Filters');
    fireEvent.click(clearButton);

    await waitFor(() => {
      expect(screen.getByText('2 tools total')).toBeInTheDocument();
    }, { timeout: 3000 });
  });

  it('shows no results message when no tools match filters', async () => {
    render(<ClientToolsPage {...defaultProps} />);

    const searchInput = screen.getByPlaceholderText('Search AI tools...');
    fireEvent.change(searchInput, { target: { value: 'nonexistent tool' } });

    await waitFor(() => {
      expect(screen.getByText('No tools found')).toBeInTheDocument();
      expect(screen.getByText('Try adjusting your filters or search query.')).toBeInTheDocument();
    }, { timeout: 3000 });
  });
});
