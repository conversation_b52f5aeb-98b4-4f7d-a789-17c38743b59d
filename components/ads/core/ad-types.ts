/**
 * 广告组件类型定义
 * 定义所有广告相关的类型和接口
 */

export type AdFormat = 'auto' | 'rectangle' | 'vertical' | 'horizontal' | 'banner' | 'leaderboard' | 'skyscraper';

export type AdSize = 
  | 'banner'        // 728x90
  | 'leaderboard'   // 728x90
  | 'rectangle'     // 300x250
  | 'square'        // 250x250
  | 'skyscraper'    // 160x600
  | 'mobile-banner' // 320x50
  | 'large-mobile'  // 320x100
  | 'responsive'    // 自适应
  | 'custom';       // 自定义尺寸

export type AdPosition = 
  | 'header'
  | 'sidebar'
  | 'content-top'
  | 'content-middle'
  | 'content-bottom'
  | 'footer'
  | 'between-items'
  | 'floating';

export interface AdDimensions {
  width: number | string;
  height: number | string;
}

export interface BaseAdProps {
  adSlot: string;
  adFormat?: AdFormat;
  fullWidthResponsive?: boolean;
  style?: React.CSSProperties;
  className?: string;
  position?: AdPosition;
  lazy?: boolean;
  testMode?: boolean;
}

export interface AdContainerProps {
  children: React.ReactNode;
  position?: AdPosition;
  className?: string;
  spacing?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  centered?: boolean;
  maxWidth?: string;
}

export interface ResponsiveAdProps extends BaseAdProps {
  size: AdSize;
  mobileSize?: AdSize;
  tabletSize?: AdSize;
  desktopSize?: AdSize;
}

export interface AdPlacementConfig {
  slot: string;
  format: AdFormat;
  size: AdSize;
  position: AdPosition;
  responsive?: boolean;
  conditions?: {
    minWidth?: number;
    maxWidth?: number;
    pageType?: string[];
    excludePages?: string[];
  };
}

// 预定义的广告尺寸配置
export const AD_SIZES: Record<AdSize, AdDimensions> = {
  'banner': { width: 728, height: 90 },
  'leaderboard': { width: 728, height: 90 },
  'rectangle': { width: 300, height: 250 },
  'square': { width: 250, height: 250 },
  'skyscraper': { width: 160, height: 600 },
  'mobile-banner': { width: 320, height: 50 },
  'large-mobile': { width: 320, height: 100 },
  'responsive': { width: '100%', height: 'auto' },
  'custom': { width: 'auto', height: 'auto' }
};

// 广告位配置
export const AD_PLACEMENTS: Record<string, AdPlacementConfig> = {
  'home-banner': {
    slot: '1620640133',
    format: 'auto',
    size: 'banner',
    position: 'content-top',
    responsive: true
  },
  'home-rectangle': {
    slot: '4626949563',
    format: 'rectangle',
    size: 'rectangle',
    position: 'content-middle',
    responsive: true
  },
  'sidebar-skyscraper': {
    slot: '7890123456', // 需要替换为实际的广告位ID
    format: 'vertical',
    size: 'skyscraper',
    position: 'sidebar',
    responsive: false
  },
  'content-rectangle': {
    slot: '2345678901', // 需要替换为实际的广告位ID
    format: 'rectangle',
    size: 'rectangle',
    position: 'content-middle',
    responsive: true
  },
  'mobile-banner': {
    slot: '3456789012', // 需要替换为实际的广告位ID
    format: 'horizontal',
    size: 'mobile-banner',
    position: 'content-top',
    responsive: true,
    conditions: {
      maxWidth: 768
    }
  }
};
