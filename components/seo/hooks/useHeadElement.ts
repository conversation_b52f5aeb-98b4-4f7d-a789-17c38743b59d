'use client';

import { useEffect } from 'react';

type ElementType = 'meta' | 'link' | 'script';
type ElementAttributes = Record<string, string>;

interface UseHeadElementOptions {
  type: ElementType;
  attributes: ElementAttributes;
  content?: string;
  selector?: string;
  cleanup?: boolean;
}

/**
 * 通用的 Hook，用于管理 head 元素
 * @param options 配置选项
 * @returns void
 */
export function useHeadElement(options: UseHeadElementOptions): void {
  const { type, attributes, content, selector, cleanup = true } = options;

  useEffect(() => {
    // 如果提供了选择器，尝试查找现有元素
    let element: HTMLElement | null = null;
    
    if (selector) {
      element = document.querySelector(selector);
    }

    // 如果元素不存在，创建新元素
    if (!element) {
      element = document.createElement(type);
      
      // 设置属性
      Object.entries(attributes).forEach(([key, value]) => {
        if (key === 'innerHTML') {
          element!.innerHTML = value;
        } else {
          element!.setAttribute(key, value);
        }
      });
      
      // 设置内容（如果有）
      if (content !== undefined) {
        if (type === 'script') {
          (element as HTMLScriptElement).text = content;
        } else {
          element.textContent = content;
        }
      }
      
      // 添加到 head
      document.head.appendChild(element);
    } else {
      // 更新现有元素的属性
      Object.entries(attributes).forEach(([key, value]) => {
        if (key === 'innerHTML') {
          element!.innerHTML = value;
        } else {
          element!.setAttribute(key, value);
        }
      });
      
      // 更新内容（如果有）
      if (content !== undefined) {
        if (type === 'script') {
          (element as HTMLScriptElement).text = content;
        } else {
          element.textContent = content;
        }
      }
    }
    
    // 清理函数
    return () => {
      if (cleanup && element && selector) {
        // 如果需要清理并且元素存在，移除元素
        element.remove();
      }
    };
  }, [type, attributes, content, selector, cleanup]);
}

/**
 * 更新或创建 meta 标签
 * @param name meta 标签的 name 或 property 属性
 * @param content meta 标签的内容
 * @param isProperty 是否使用 property 属性而不是 name
 */
export function useMetaTag(name: string, content: string, isProperty: boolean = false): void {
  const attributeType = isProperty || name.startsWith('og:') ? 'property' : 'name';
  const selector = `meta[${attributeType}="${name}"]`;
  
  useHeadElement({
    type: 'meta',
    attributes: { [attributeType]: name, content },
    selector
  });
}

/**
 * 更新或创建 link 标签
 * @param rel link 标签的 rel 属性
 * @param href link 标签的 href 属性
 * @param additionalAttributes 其他属性
 */
export function useLinkTag(
  rel: string,
  href: string,
  additionalAttributes: Record<string, string> = {}
): void {
  const attributes = { rel, href, ...additionalAttributes };
  const selector = `link[rel="${rel}"]${additionalAttributes.hreflang ? `[hreflang="${additionalAttributes.hreflang}"]` : ''}`;
  
  useHeadElement({
    type: 'link',
    attributes,
    selector
  });
}

/**
 * 更新页面标题
 * @param title 页面标题
 */
export function usePageTitle(title: string): void {
  useEffect(() => {
    const originalTitle = document.title;
    document.title = title;
    
    return () => {
      document.title = originalTitle;
    };
  }, [title]);
}
