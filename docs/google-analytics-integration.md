# Google Analytics 集成文档

## 概述

本项目已成功集成 Google Analytics (GA4)，用于跟踪网站访问数据和用户行为分析。

## 集成详情

### Google Analytics ID
- **Measurement ID**: `G-PR6E12G0V0`

### 实现方式

Google Analytics 代码已添加到项目的根布局文件 (`app/layout.tsx`) 中，使用 Next.js 的 `Script` 组件进行优化加载。

### 关键特性

1. **环境控制**: 只在生产环境中加载 Google Analytics 脚本
2. **性能优化**: 使用 `afterInteractive` 策略延迟加载脚本
3. **标准配置**: 包含完整的 gtag 配置和 dataLayer 初始化

### 代码实现

```tsx
{/* Google Analytics - 只在生产环境加载 */}
{process.env.NODE_ENV === 'production' && (
  <>
    <Script
      src="https://www.googletagmanager.com/gtag/js?id=G-PR6E12G0V0"
      strategy="afterInteractive"
    />
    <Script id="google-analytics" strategy="afterInteractive">
      {`
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'G-PR6E12G0V0');
      `}
    </Script>
  </>
)}
```

## 测试

项目包含完整的测试套件来验证 Google Analytics 集成：

- ✅ 生产环境中正确加载脚本
- ✅ 开发环境中不加载脚本
- ✅ 正确的配置参数

运行测试：
```bash
npm test -- __tests__/google-analytics.test.tsx
```

## 部署注意事项

1. **环境变量**: 确保生产环境中 `NODE_ENV=production`
2. **域名验证**: 在 Google Analytics 控制台中验证网站域名
3. **数据流配置**: 确认 GA4 数据流配置正确

## 静态导出兼容性

✅ **完全支持静态导出部署**

- Google Analytics 代码在构建时被注入到静态 HTML 文件中
- 脚本在客户端浏览器中执行，不依赖服务器端功能
- 适用于 Cloudflare Pages、Netlify、Vercel 等静态托管平台
- 经过测试验证，静态导出的 HTML 文件包含完整的 GA 代码

### 验证方法
```bash
# 检查静态导出文件中的 Google Analytics 代码
grep -o "googletagmanager\|G-PR6E12G0V0" out/en/index.html
```

## 数据收集

Google Analytics 将自动收集以下数据：
- 页面浏览量
- 用户会话
- 流量来源
- 设备信息
- 地理位置数据

## 隐私合规

- 脚本仅在生产环境加载
- 遵循 Google Analytics 的数据处理协议
- 建议在隐私政策中说明数据收集情况

## 维护

- 定期检查 Google Analytics 控制台中的数据
- 监控脚本加载性能
- 根据需要调整跟踪配置
