#!/usr/bin/env node

/**
 * 测试 RSC 路径映射
 * 验证重写规则是否正确
 */

const fs = require('fs');
const path = require('path');

function testPathMapping() {
  console.log('Testing RSC path mappings...\n');
  
  const testCases = [
    {
      request: '/en.txt',
      expected: '/en/index.txt',
      description: 'Root English RSC'
    },
    {
      request: '/zh.txt',
      expected: '/zh/index.txt',
      description: 'Root Chinese RSC'
    },
    {
      request: '/en/category/coding-development.txt',
      expected: '/en/category/coding-development/index.txt',
      description: 'Category page RSC'
    },
    {
      request: '/en/tool/force-com.txt',
      expected: '/en/tool/force-com/index.txt',
      description: 'Tool page RSC'
    },
    {
      request: '/en/tools.txt',
      expected: '/en/tools/index.txt',
      description: 'Tools listing RSC'
    }
  ];
  
  const outDir = path.join(process.cwd(), 'out');
  
  testCases.forEach(testCase => {
    const expectedFile = path.join(outDir, testCase.expected.slice(1));
    const exists = fs.existsSync(expectedFile);
    
    console.log(`${testCase.description}:`);
    console.log(`  Request: ${testCase.request}`);
    console.log(`  Maps to: ${testCase.expected}`);
    console.log(`  File exists: ${exists ? '✅' : '❌'}`);
    
    if (exists) {
      const stats = fs.statSync(expectedFile);
      console.log(`  File size: ${stats.size} bytes`);
    }
    console.log('');
  });
}

testPathMapping();