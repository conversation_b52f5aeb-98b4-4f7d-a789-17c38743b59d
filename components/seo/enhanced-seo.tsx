'use client';

import { Locale } from '@/lib/i18n/config';
import { Tool, Category } from '@/lib/types';
import CanonicalLink from './canonical-link';
import DynamicMetadata from './dynamic-metadata';
import HreflangTags from './hreflang-tags';
import StructuredData from './structured-data';
import { useMetaTag } from './hooks/useHeadElement';

// 网站基本信息
const siteUrl = 'https://x114.org';

interface EnhancedSeoProps {
  locale: Locale;
  title: string;
  description: string;
  path: string;
  keywords?: string[];
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  ogType?: 'website' | 'article';
  twitterTitle?: string;
  twitterDescription?: string;
  twitterImage?: string;
  noindex?: boolean;
  nofollow?: boolean;
  structuredDataType?: 'website' | 'tool' | 'category' | 'article' | 'organization';
  structuredData?: Tool | Category | any;
  publishedTime?: string;
  modifiedTime?: string;
  author?: string;
  section?: string;
  tags?: string[];
  alternateLanguages?: Record<string, string>;
}

/**
 * 增强的SEO组件，提供完整的SEO优化功能
 * 符合Google SEO最佳实践和规范
 */
export default function EnhancedSeo({
  locale,
  title,
  description,
  path,
  keywords = [],
  ogTitle,
  ogDescription,
  ogImage,
  ogType = 'website',
  twitterTitle,
  twitterDescription,
  twitterImage,
  noindex = false,
  nofollow = false,
  structuredDataType,
  structuredData,
  publishedTime,
  modifiedTime,
  author,
  section,
  tags = [],
  alternateLanguages,
}: EnhancedSeoProps) {
  // 规范链接
  const canonicalUrl = `${siteUrl}/${locale}${path}`;

  // 处理robots指令
  const robotsContent = [
    noindex ? 'noindex' : 'index',
    nofollow ? 'nofollow' : 'follow',
  ].join(', ');

  // Open Graph 元数据
  useMetaTag('og:title', ogTitle || title, true);
  useMetaTag('og:description', ogDescription || description, true);
  useMetaTag('og:type', ogType, true);
  useMetaTag('og:url', canonicalUrl, true);
  useMetaTag('og:site_name', 'X114 AI Tool Hub', true);
  useMetaTag('og:locale', getOpenGraphLocale(locale), true);

  if (ogImage) {
    useMetaTag('og:image', ogImage, true);
    useMetaTag('og:image:width', '1200', true);
    useMetaTag('og:image:height', '630', true);
    useMetaTag('og:image:alt', title, true);
    useMetaTag('og:image:type', 'image/png', true);
  }

  // 文章特定的Open Graph元数据
  if (ogType === 'article') {
    if (publishedTime) {
      useMetaTag('article:published_time', publishedTime, true);
    }
    if (modifiedTime) {
      useMetaTag('article:modified_time', modifiedTime, true);
    }
    if (author) {
      useMetaTag('article:author', author, true);
    }
    if (section) {
      useMetaTag('article:section', section, true);
    }
    tags.forEach(tag => {
      useMetaTag('article:tag', tag, true);
    });
  }

  // Twitter Card 元数据
  useMetaTag('twitter:card', 'summary_large_image');
  useMetaTag('twitter:site', '@x114ai');
  useMetaTag('twitter:creator', '@x114ai');
  useMetaTag('twitter:title', twitterTitle || title);
  useMetaTag('twitter:description', twitterDescription || description);

  if (twitterImage || ogImage) {
    useMetaTag('twitter:image', twitterImage || ogImage || '');
    useMetaTag('twitter:image:alt', title);
  }

  // 其他重要的SEO元标签
  useMetaTag('robots', robotsContent);

  // 语言和地区相关
  useMetaTag('language', locale);
  useMetaTag('content-language', locale);

  // 移动端优化
  useMetaTag('mobile-web-app-capable', 'yes');
  useMetaTag('apple-mobile-web-app-capable', 'yes');
  useMetaTag('apple-mobile-web-app-status-bar-style', 'default');
  useMetaTag('apple-mobile-web-app-title', 'X114');

  // 主题颜色
  useMetaTag('theme-color', '#3b82f6');
  useMetaTag('msapplication-TileColor', '#3b82f6');

  return (
    <>
      {/* 规范链接 */}
      <CanonicalLink url={canonicalUrl} />

      {/* 语言版本链接 */}
      <HreflangTags
        path={path}
        locale={locale}
        baseUrl={siteUrl}
        alternateLanguages={alternateLanguages}
      />

      {/* 页面元数据 */}
      <DynamicMetadata
        locale={locale}
        title={title}
        description={description}
        keywords={keywords}
        ogTitle={ogTitle}
        ogDescription={ogDescription}
        ogImage={ogImage}
        twitterTitle={twitterTitle}
        twitterDescription={twitterDescription}
        twitterImage={twitterImage}
        noindex={noindex}
      />

      {/* 结构化数据 */}
      {structuredDataType && (
        <StructuredData
          type={structuredDataType}
          data={structuredData}
          locale={locale}
        />
      )}

      {/* 网站和组织结构化数据 */}
      <StructuredData type="website" />
      <StructuredData type="organization" />
    </>
  );
}

// 辅助函数：将语言代码转换为Open Graph locale格式
function getOpenGraphLocale(locale: Locale): string {
  const localeMap: Record<Locale, string> = {
    'en': 'en_US',
    'zh': 'zh_CN',
    'tw': 'zh_TW',
    'ko': 'ko_KR',
    'ja': 'ja_JP',
    'pt': 'pt_PT',
    'es': 'es_ES',
    'de': 'de_DE',
    'fr': 'fr_FR',
    'vi': 'vi_VN',
  };
  return localeMap[locale] || 'en_US';
}
