import { Tool, Category, Article } from '../types';
import { isStaticExport } from '../utils/environment';
import {
  getStaticTools as loadStaticTools,
  getStaticToolById as loadStaticToolById,
  getStaticToolBySlug as loadStaticToolBySlug,
  getStaticCategories as loadStaticCategories,
  getStaticCategoryById as loadStaticCategoryById,
  getStaticArticles as loadStaticArticles,
  getStaticArticleById as loadStaticArticleById,
  getStaticArticleBySlug as loadStaticArticleBySlug
} from '../utils/static-data-loader';

// 静态数据模块接口
interface StaticDataModule {
  getStaticTools: (language?: string) => Tool[];
  getStaticToolById: (id: string, language?: string) => Tool | undefined;
  getStaticToolBySlug: (slug: string, language?: string) => Tool | undefined;
  getStaticCategories: (language?: string) => Category[];
  getStaticCategoryById: (id: string, language?: string) => Category | undefined;
  getStaticCategoryBySlug: (slug: string, language?: string) => Category | undefined;
  getStaticArticles: (language?: string) => Article[];
  getStaticArticleById: (id: string, language?: string) => Article | undefined;
  getStaticArticleBySlug: (slug: string, language?: string) => Article | undefined;
}

// 创建静态数据模块
const staticDataModule: StaticDataModule = {
  // 工具相关函数
  getStaticTools: (language = 'en') => {
    if (isStaticExport()) {
      return loadStaticTools(language as any);
    }
    return [];
  },

  getStaticToolById: (id: string, language = 'en') => {
    if (isStaticExport()) {
      return loadStaticToolById(id, language as any);
    }
    return undefined;
  },

  getStaticToolBySlug: (slug: string, language = 'en') => {
    if (isStaticExport()) {
      return loadStaticToolBySlug(slug, language as any);
    }
    return undefined;
  },

  // 类别相关函数
  getStaticCategories: (language = 'en') => {
    if (isStaticExport()) {
      return loadStaticCategories(language as any);
    }
    return [];
  },

  getStaticCategoryById: (id: string, language = 'en') => {
    if (isStaticExport()) {
      return loadStaticCategoryById(id, language as any);
    }
    return undefined;
  },

  getStaticCategoryBySlug: (slug: string, language = 'en') => {
    if (isStaticExport()) {
      // 由于 static-data-loader 中没有 getStaticCategoryBySlug，我们需要通过 getStaticCategories 来查找
      const categories = loadStaticCategories(language as any);
      return categories.find(category => category.slug === slug);
    }
    return undefined;
  },

  // 文章相关函数
  getStaticArticles: (language = 'en') => {
    if (isStaticExport()) {
      return loadStaticArticles(language as any);
    }
    return [];
  },

  getStaticArticleById: (id: string, language = 'en') => {
    if (isStaticExport()) {
      return loadStaticArticleById(id, language as any);
    }
    return undefined;
  },

  getStaticArticleBySlug: (slug: string, language = 'en') => {
    if (isStaticExport()) {
      return loadStaticArticleBySlug(slug, language as any);
    }
    return undefined;
  }
};

/**
 * 获取静态工具数据
 * @param language 语言
 * @returns 工具列表
 */
export function getStaticTools(language: string = 'en'): Tool[] {
  if (!staticDataModule || typeof staticDataModule.getStaticTools !== 'function') {
    throw new Error('Static data module not loaded. Please run "node scripts/fetch-static-data.js" before building.');
  }

  return staticDataModule.getStaticTools(language);
}

/**
 * 获取静态类别数据
 * @param language 语言
 * @returns 类别列表
 */
export function getStaticCategories(language: string = 'en'): Category[] {
  if (!staticDataModule || typeof staticDataModule.getStaticCategories !== 'function') {
    throw new Error('Static data module not loaded. Please run "node scripts/fetch-static-data.js" before building.');
  }

  return staticDataModule.getStaticCategories(language);
}

/**
 * 获取静态文章数据
 * @param language 语言
 * @returns 文章列表
 */
export function getStaticArticles(language: string = 'en'): Article[] {
  if (!staticDataModule || typeof staticDataModule.getStaticArticles !== 'function') {
    throw new Error('Static data module not loaded. Please run "node scripts/fetch-static-data.js" before building.');
  }

  return staticDataModule.getStaticArticles(language);
}

/**
 * 根据ID获取静态工具
 * @param id 工具ID
 * @param language 语言
 * @returns 工具对象，如果未找到则返回undefined
 */
export function getStaticToolById(id: string, language: string = 'en'): Tool | undefined {
  if (!staticDataModule || typeof staticDataModule.getStaticToolById !== 'function') {
    throw new Error('Static data module not loaded. Please run "node scripts/fetch-static-data.js" before building.');
  }

  return staticDataModule.getStaticToolById(id, language);
}

/**
 * 根据ID获取静态工具
 * @param slug 工具slug
 * @param language 语言
 * @returns 工具对象，如果未找到则返回undefined
 */
export function getStaticToolBySlug(slug: string, language: string = 'en'): Tool | undefined {
  if (!staticDataModule || typeof staticDataModule.getStaticToolBySlug !== 'function') {
    throw new Error('Static data module not loaded. Please run "node scripts/fetch-static-data.js" before building.');
  }

  return staticDataModule.getStaticToolBySlug(slug, language);
}

/**
 * 根据ID获取静态类别
 * @param id 类别ID
 * @param language 语言
 * @returns 类别对象，如果未找到则返回undefined
 */
export function getStaticCategoryById(id: string, language: string = 'en'): Category | undefined {
  if (!staticDataModule || typeof staticDataModule.getStaticCategoryById !== 'function') {
    throw new Error('Static data module not loaded. Please run "node scripts/fetch-static-data.js" before building.');
  }

  return staticDataModule.getStaticCategoryById(id, language);
}

/**
 * 根据Slug获取静态类别
 * @param slug 类别Slug
 * @param language 语言
 * @returns 类别对象，如果未找到则返回undefined
 */
export function getStaticCategoryBySlug(slug: string, language: string = 'en'): Category | undefined {
  if (!staticDataModule || typeof staticDataModule.getStaticCategoryBySlug !== 'function') {
    throw new Error('Static data module not loaded. Please run "node scripts/fetch-static-data.js" before building.');
  }

  return staticDataModule.getStaticCategoryBySlug(slug, language);
}

/**
 * 根据ID获取静态文章
 * @param id 文章ID
 * @param language 语言
 * @returns 文章对象，如果未找到则返回undefined
 */
export function getStaticArticleById(id: string, language: string = 'en'): Article | undefined {
  if (!staticDataModule || typeof staticDataModule.getStaticArticleById !== 'function') {
    throw new Error('Static data module not loaded. Please run "node scripts/fetch-static-data.js" before building.');
  }

  return staticDataModule.getStaticArticleById(id, language);
}

/**
 * 根据Slug获取静态文章
 * @param slug 文章Slug
 * @param language 语言
 * @returns 文章对象，如果未找到则返回undefined
 */
export function getStaticArticleBySlug(slug: string, language: string = 'en'): Article | undefined {
  if (!staticDataModule || typeof staticDataModule.getStaticArticleBySlug !== 'function') {
    throw new Error('Static data module not loaded. Please run "node scripts/fetch-static-data.js" before building.');
  }

  return staticDataModule.getStaticArticleBySlug(slug, language);
}

/**
 * 为了向后兼容，保留旧的函数名
 * @param id 类别ID
 * @param language 语言
 * @returns 类别对象，如果未找到则返回undefined
 */
export function getStaticCategorieById(id: string, language: string = 'en'): Category | undefined {
  return getStaticCategoryById(id, language);
}
