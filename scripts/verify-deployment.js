#!/usr/bin/env node

/**
 * 部署验证脚本
 * 检查网站是否正确部署，没有重定向循环问题
 */

const https = require('https');
const http = require('http');

async function checkUrl(url, expectedStatus = 200, maxRedirects = 5) {
  return new Promise((resolve, reject) => {
    const client = url.startsWith('https:') ? https : http;
    let redirectCount = 0;
    
    function makeRequest(currentUrl) {
      const request = client.get(currentUrl, (response) => {
        const { statusCode, headers } = response;
        
        console.log(`${currentUrl} -> ${statusCode}`);
        
        if (statusCode >= 300 && statusCode < 400 && headers.location) {
          redirectCount++;
          if (redirectCount > maxRedirects) {
            reject(new Error(`Too many redirects (${redirectCount}) for ${url}`));
            return;
          }
          
          const redirectUrl = new URL(headers.location, currentUrl).toString();
          console.log(`  Redirecting to: ${redirectUrl}`);
          
          // 检查是否重定向到 .txt 文件
          if (redirectUrl.includes('.txt')) {
            reject(new Error(`Redirecting to .txt file: ${redirectUrl}`));
            return;
          }
          
          makeRequest(redirectUrl);
        } else {
          resolve({
            url: currentUrl,
            status: statusCode,
            redirects: redirectCount,
            headers: headers
          });
        }
      });
      
      request.on('error', reject);
      request.setTimeout(10000, () => {
        request.destroy();
        reject(new Error(`Timeout for ${currentUrl}`));
      });
    }
    
    makeRequest(url);
  });
}

async function verifyDeployment(baseUrl) {
  console.log(`Verifying deployment at: ${baseUrl}`);
  console.log('='.repeat(50));
  
  const testUrls = [
    { url: baseUrl, description: 'Root path' },
    { url: `${baseUrl}/en/`, description: 'English homepage' },
    { url: `${baseUrl}/zh/`, description: 'Chinese homepage' },
    { url: `${baseUrl}/en/tools/`, description: 'Tools page' },
    { url: `${baseUrl}/en/categories/`, description: 'Categories page' },
    { url: `${baseUrl}/en.txt`, description: 'Root English RSC data stream', expectedStatus: 200 },
    { url: `${baseUrl}/zh.txt`, description: 'Root Chinese RSC data stream', expectedStatus: 200 },
    { url: `${baseUrl}/en/category/coding-development.txt`, description: 'Category RSC data stream', expectedStatus: 200 },
    { url: `${baseUrl}/en/tool/force-com.txt`, description: 'Tool RSC data stream', expectedStatus: 200 },
    { url: `${baseUrl}/en/tools.txt`, description: 'Tools listing RSC data stream', expectedStatus: 200 },
  ];
  
  const results = [];
  
  for (const test of testUrls) {
    try {
      console.log(`\nTesting: ${test.description}`);
      const result = await checkUrl(test.url);
      results.push({ ...test, success: true, result });
      console.log(`✅ Success: ${result.status} (${result.redirects} redirects)`);
    } catch (error) {
      results.push({ ...test, success: false, error: error.message });
      console.log(`❌ Failed: ${error.message}`);
    }
  }
  
  console.log('\n' + '='.repeat(50));
  console.log('SUMMARY:');
  
  const successful = results.filter(r => r.success).length;
  const total = results.length;
  
  console.log(`${successful}/${total} tests passed`);
  
  if (successful === total) {
    console.log('🎉 All tests passed! Deployment looks good.');
    process.exit(0);
  } else {
    console.log('⚠️  Some tests failed. Please check the issues above.');
    process.exit(1);
  }
}

// 从命令行参数获取 URL，或使用默认值
const baseUrl = process.argv[2] || 'https://x114.org';
verifyDeployment(baseUrl).catch(console.error);