/**
 * Cloudflare Pages 智能路由处理
 * 处理特殊情况并添加调试信息
 */

export async function onRequest(context) {
  const { request } = context;
  const url = new URL(request.url);
  const { pathname } = url;

  // 只处理根路径重定向，其他交给静态规则
  if (pathname === '/') {
    return Response.redirect(new URL('/en/', request.url), 302);
  }

  // 获取原始响应
  const response = await context.next();
  
  if (response) {
    // 创建新响应，添加调试头部
    const newResponse = new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers: response.headers
    });
    
    // 添加调试信息
    newResponse.headers.set('X-Route-Debug', `path:${pathname}`);
    newResponse.headers.set('X-Route-Status', response.status.toString());
    
    // 如果是 404，添加更多调试信息
    if (response.status === 404) {
      newResponse.headers.set('X-Route-404-Path', pathname);
      newResponse.headers.set('X-Route-404-Time', new Date().toISOString());
    }
    
    return newResponse;
  }
  
  return response;
}