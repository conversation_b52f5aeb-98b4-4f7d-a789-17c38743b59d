'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';
import { ExternalLink, Check, X, Clock } from 'lucide-react';

interface Submission {
  id: string;
  name: string;
  description: string;
  website: string;
  category: string;
  email: string;
  additional_info?: string;
  locale: string;
  status: 'pending' | 'approved' | 'rejected';
  submitted_at: string;
  created_at: string;
}

interface SubmissionListProps {
  apiKey: string;
  apiBaseUrl?: string;
}

interface ApiResponse {
  success: boolean;
  data: Submission[];
  pagination?: {
    limit: number;
    offset: number;
    hasMore: boolean;
  };
}

export default function SubmissionList({
  apiKey,
  apiBaseUrl = '/api'
}: SubmissionListProps) {
  const [submissions, setSubmissions] = useState<Submission[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'pending' | 'approved' | 'rejected'>('pending');
  const [reviewNotes, setReviewNotes] = useState<Record<string, string>>({});

  useEffect(() => {
    fetchSubmissions();
  }, [filter]);

  const fetchSubmissions = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (filter !== 'all') {
        params.set('status', filter);
      }

      const response = await fetch(`${apiBaseUrl}/admin/submissions?${params}`, {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch submissions');
      }

      const data = await response.json() as ApiResponse;
      setSubmissions(data.data || []);
    } catch (error) {
      console.error('Error fetching submissions:', error);
      toast.error('Failed to load submissions');
    } finally {
      setLoading(false);
    }
  };

  const updateSubmissionStatus = async (
    id: string,
    status: 'approved' | 'rejected'
  ) => {
    try {
      const response = await fetch(`${apiBaseUrl}/admin/submissions`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id,
          status,
          reviewedBy: 'admin', // 在实际应用中，这应该是当前用户的ID
          reviewNotes: reviewNotes[id] || '',
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update submission');
      }

      toast.success(`Submission ${status} successfully`);
      fetchSubmissions(); // 重新加载列表
    } catch (error) {
      console.error('Error updating submission:', error);
      toast.error('Failed to update submission');
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="text-yellow-600"><Clock className="w-3 h-3 mr-1" />Pending</Badge>;
      case 'approved':
        return <Badge variant="outline" className="text-green-600"><Check className="w-3 h-3 mr-1" />Approved</Badge>;
      case 'rejected':
        return <Badge variant="outline" className="text-red-600"><X className="w-3 h-3 mr-1" />Rejected</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading submissions...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 过滤器 */}
      <div className="flex gap-2">
        {(['all', 'pending', 'approved', 'rejected'] as const).map((status) => (
          <Button
            key={status}
            variant={filter === status ? 'default' : 'outline'}
            onClick={() => setFilter(status)}
            className="capitalize"
          >
            {status}
          </Button>
        ))}
      </div>

      {/* 提交列表 */}
      {submissions.length === 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <p className="text-muted-foreground">No submissions found</p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {submissions.map((submission) => (
            <Card key={submission.id}>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      {submission.name}
                      <a
                        href={submission.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-primary hover:text-primary/80"
                      >
                        <ExternalLink className="w-4 h-4" />
                      </a>
                    </CardTitle>
                    <p className="text-sm text-muted-foreground">
                      {submission.email} • {submission.category} • {submission.locale}
                    </p>
                  </div>
                  {getStatusBadge(submission.status)}
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">Description</h4>
                  <p className="text-sm text-muted-foreground">{submission.description}</p>
                </div>

                {submission.additional_info && (
                  <div>
                    <h4 className="font-medium mb-2">Additional Information</h4>
                    <p className="text-sm text-muted-foreground">{submission.additional_info}</p>
                  </div>
                )}

                <div className="text-xs text-muted-foreground">
                  Submitted: {new Date(submission.submitted_at).toLocaleString()}
                </div>

                {submission.status === 'pending' && (
                  <div className="space-y-3 pt-4 border-t">
                    <div>
                      <label className="text-sm font-medium mb-2 block">Review Notes (Optional)</label>
                      <Textarea
                        placeholder="Add notes about this submission..."
                        value={reviewNotes[submission.id] || ''}
                        onChange={(e) => setReviewNotes(prev => ({
                          ...prev,
                          [submission.id]: e.target.value
                        }))}
                        className="h-20"
                      />
                    </div>
                    <div className="flex gap-2">
                      <Button
                        onClick={() => updateSubmissionStatus(submission.id, 'approved')}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        <Check className="w-4 h-4 mr-2" />
                        Approve
                      </Button>
                      <Button
                        onClick={() => updateSubmissionStatus(submission.id, 'rejected')}
                        variant="destructive"
                      >
                        <X className="w-4 h-4 mr-2" />
                        Reject
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
