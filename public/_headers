# Cloudflare Pages Headers 配置
# 优化缓存策略和内容类型

# ==========================================
# 静态资源缓存优化
# ==========================================
/_next/static/*
  Cache-Control: public, max-age=31536000, immutable
  Vary: Accept-Encoding

# 图片资源缓存
/images/*
  Cache-Control: public, max-age=86400
  Vary: Accept-Encoding

/photos/*
  Cache-Control: public, max-age=86400
  Vary: Accept-Encoding

# 图标和清单文件
/*.ico
  Cache-Control: public, max-age=86400

/*.png
  Cache-Control: public, max-age=86400

/manifest.json
  Cache-Control: public, max-age=3600
  Content-Type: application/manifest+json

# ==========================================
# RSC 数据流优化
# ==========================================
/*.txt
  Content-Type: text/x-component
  Cache-Control: public, max-age=0, must-revalidate
  Vary: Accept, RSC
  X-Robots-Tag: noindex, nofollow

# ==========================================
# HTML 页面缓存
# ==========================================
/*.html
  Cache-Control: public, max-age=300, must-revalidate
  Vary: Accept-Language, Accept-Encoding

# ==========================================
# 全局安全头
# ==========================================
/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=()
  X-Route-Debug: enabled