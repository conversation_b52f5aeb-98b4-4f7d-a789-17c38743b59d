/**
 * 广告工具函数
 * 提供广告相关的工具函数和帮助方法
 */

import { AdSize, AdDimensions, AD_SIZES, AdPosition } from './ad-types';

/**
 * 获取广告尺寸配置
 * @param size 广告尺寸类型
 * @returns 广告尺寸配置
 */
export function getAdDimensions(size: AdSize): AdDimensions {
  return AD_SIZES[size] || AD_SIZES.responsive;
}

/**
 * 生成广告样式
 * @param size 广告尺寸类型
 * @param customStyle 自定义样式
 * @returns 合并后的样式对象
 */
export function generateAdStyle(
  size: AdSize,
  customStyle?: React.CSSProperties
): React.CSSProperties {
  const dimensions = getAdDimensions(size);
  
  const baseStyle: React.CSSProperties = {
    display: 'block',
    width: dimensions.width,
    height: dimensions.height,
    maxWidth: '100%',
  };

  // 响应式广告的特殊处理
  if (size === 'responsive') {
    baseStyle.minHeight = '90px';
  }

  return { ...baseStyle, ...customStyle };
}

/**
 * 获取广告容器的CSS类名
 * @param position 广告位置
 * @param spacing 间距
 * @param centered 是否居中
 * @returns CSS类名字符串
 */
export function getAdContainerClasses(
  position?: AdPosition,
  spacing: 'none' | 'sm' | 'md' | 'lg' | 'xl' = 'md',
  centered: boolean = true
): string {
  const classes = ['ad-container'];

  // 位置相关的类名
  if (position) {
    classes.push(`ad-position-${position}`);
  }

  // 间距类名
  const spacingClasses = {
    none: '',
    sm: 'my-2',
    md: 'my-4',
    lg: 'my-6',
    xl: 'my-8'
  };
  
  if (spacingClasses[spacing]) {
    classes.push(spacingClasses[spacing]);
  }

  // 居中对齐
  if (centered) {
    classes.push('flex justify-center items-center');
  }

  return classes.filter(Boolean).join(' ');
}

/**
 * 检查是否应该显示广告
 * @param conditions 显示条件
 * @returns 是否显示广告
 */
export function shouldShowAd(conditions?: {
  minWidth?: number;
  maxWidth?: number;
  pageType?: string[];
  excludePages?: string[];
}): boolean {
  if (!conditions) return true;

  // 检查屏幕宽度条件
  if (typeof window !== 'undefined') {
    const width = window.innerWidth;
    
    if (conditions.minWidth && width < conditions.minWidth) {
      return false;
    }
    
    if (conditions.maxWidth && width > conditions.maxWidth) {
      return false;
    }
  }

  // 检查页面类型条件（这里可以根据实际需求扩展）
  // 例如：检查当前路径是否匹配条件

  return true;
}

/**
 * 生成广告占位符内容
 * @param size 广告尺寸
 * @param slot 广告位ID
 * @returns 占位符内容
 */
export function generateAdPlaceholder(size: AdSize, slot: string): {
  text: string;
  dimensions: AdDimensions;
} {
  const dimensions = getAdDimensions(size);
  
  return {
    text: `Google Ad Placeholder (Development Mode)\nSize: ${size}\nSlot: ${slot}\nDimensions: ${dimensions.width}x${dimensions.height}`,
    dimensions
  };
}

/**
 * 延迟加载广告的工具函数
 * @param element 广告元素
 * @param callback 加载回调
 * @param threshold 触发阈值
 */
export function setupLazyLoading(
  element: HTMLElement,
  callback: () => void,
  threshold: number = 0.1
): IntersectionObserver | null {
  if (typeof window === 'undefined' || !('IntersectionObserver' in window)) {
    // 如果不支持 IntersectionObserver，直接加载
    callback();
    return null;
  }

  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          callback();
          observer.unobserve(element);
        }
      });
    },
    { threshold }
  );

  observer.observe(element);
  return observer;
}

/**
 * 获取响应式广告尺寸
 * @param baseSize 基础尺寸
 * @param screenWidth 屏幕宽度
 * @returns 适合的广告尺寸
 */
export function getResponsiveAdSize(
  baseSize: AdSize,
  screenWidth?: number
): AdSize {
  const width = screenWidth || (typeof window !== 'undefined' ? window.innerWidth : 1024);

  // 移动端
  if (width < 768) {
    if (baseSize === 'banner' || baseSize === 'leaderboard') {
      return 'mobile-banner';
    }
    if (baseSize === 'rectangle') {
      return 'rectangle'; // 保持方形广告
    }
    if (baseSize === 'skyscraper') {
      return 'rectangle'; // 在移动端将摩天楼广告改为方形
    }
  }

  // 平板端
  if (width < 1024) {
    if (baseSize === 'skyscraper') {
      return 'rectangle'; // 在平板端将摩天楼广告改为方形
    }
  }

  return baseSize;
}
