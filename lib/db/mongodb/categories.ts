import { ObjectId } from 'mongodb';
import { Category } from '../../types';
import { connectToDatabase, COLLECTIONS } from '../mongodb';
import {
  convertCategoryDocument,
  convertDocumentArray,
  findDocumentById,
  findDocumentBySlug
} from './utils';

// 获取所有类别
export async function getAllCategoriesFromMongoDB(language?: string): Promise<Category[]> {
  try {
    const db = await connectToDatabase();
    const collection = db.collection(COLLECTIONS.CATEGORIES);

    // 创建索引以提高查询性能
    await collection.createIndex({ language: 1 });
    await collection.createIndex({ id: 1 });

    // 构建查询条件，如果指定了语言，则按语言筛选
    const query = language ? { language } : {};

    const categories = await collection.find(query).toArray();

    // 使用通用转换函数处理文档
    return convertDocumentArray(categories, convertCategoryDocument);
  } catch (error) {
    console.error('Error fetching categories from MongoDB:', error);
    throw error;
  }
}

// 根据 ID 获取类别
export async function getCategoryByIdFromMongoDB(id: string, language?: string): Promise<Category | null> {
  try {
    const db = await connectToDatabase();
    const collection = db.collection(COLLECTIONS.CATEGORIES);

    // 使用通用函数查找文档
    return await findDocumentById(collection, id, convertCategoryDocument, language);
  } catch (error) {
    console.error(`Error fetching category with id ${id} from MongoDB:`, error);
    throw error;
  }
}

// 根据 Slug 获取类别
export async function getCategoryBySlugFromMongoDB(slug: string, language?: string): Promise<Category | null> {
  try {
    const db = await connectToDatabase();
    const collection = db.collection(COLLECTIONS.CATEGORIES);

    // 使用通用函数查找文档
    return await findDocumentBySlug(collection, slug, convertCategoryDocument, language);
  } catch (error) {
    console.error(`Error fetching category with slug ${slug} from MongoDB:`, error);
    throw error;
  }
}

// 创建新类别
export async function createCategoryInMongoDB(category: Omit<Category, '_id'>): Promise<Category> {
  try {
    const db = await connectToDatabase();
    const collection = db.collection(COLLECTIONS.CATEGORIES);

    const result = await collection.insertOne(category);

    // 使用通用转换函数处理文档
    return convertCategoryDocument({
      ...category,
      _id: result.insertedId
    });
  } catch (error) {
    console.error('Error creating category in MongoDB:', error);
    throw error;
  }
}

// 更新类别
export async function updateCategoryInMongoDB(id: string, category: Partial<Category>): Promise<Category | null> {
  try {
    const db = await connectToDatabase();
    const collection = db.collection(COLLECTIONS.CATEGORIES);

    // 尝试使用 id 字段更新
    let query: any = { id };

    // 如果没有找到，尝试使用 _id 更新（如果 id 是有效的 ObjectId）
    if ((await collection.countDocuments(query)) === 0 && ObjectId.isValid(id)) {
      query = { _id: new ObjectId(id) };
    }

    const result = await collection.findOneAndUpdate(
      query,
      { $set: category },
      { returnDocument: 'after' }
    );

    // 使用通用转换函数处理文档
    return convertCategoryDocument(result);
  } catch (error) {
    console.error(`Error updating category with id ${id} in MongoDB:`, error);
    throw error;
  }
}

// 删除类别
export async function deleteCategoryFromMongoDB(id: string): Promise<boolean> {
  try {
    const db = await connectToDatabase();
    const collection = db.collection(COLLECTIONS.CATEGORIES);

    // 尝试使用 id 字段删除
    let result = await collection.deleteOne({ id });

    // 如果没有删除任何文档，尝试使用 _id 删除（如果 id 是有效的 ObjectId）
    if (result.deletedCount === 0 && ObjectId.isValid(id)) {
      result = await collection.deleteOne({ _id: new ObjectId(id) });
    }

    return result.deletedCount > 0;
  } catch (error) {
    console.error(`Error deleting category with id ${id} from MongoDB:`, error);
    throw error;
  }
}
