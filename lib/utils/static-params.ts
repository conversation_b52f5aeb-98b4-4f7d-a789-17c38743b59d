import { Locale, locales } from '@/lib/i18n/config';
import { getTools, getToolById } from '@/lib/db/tools';
import { getAllCategories, getCategoryById } from '@/lib/db/categories';
import { getArticles, getArticleBySlug } from '@/lib/db/articles';

/**
 * 生成所有支持的语言路径
 * @returns 语言路径参数
 */
export function generateLocaleParams() {
  return locales.map((locale) => ({ locale }));
}

/**
 * 生成所有工具的路径参数
 * @returns 工具路径参数
 */
export async function generateToolParams() {
  const params = [];

  // 为每种语言获取工具
  for (const locale of locales) {
    const tools = await getTools({}, locale);

    // 为每个工具生成路径参数
    for (const tool of tools) {
      params.push({
        locale,
        id: tool.id
      });
    }
  }

  return params;
}

/**
 * 生成所有类别的路径参数
 * @returns 类别路径参数
 */
export async function generateCategoryParams() {
  const params = [];

  // 为每种语言获取类别
  for (const locale of locales) {
    const categories = await getAllCategories(locale);

    // 为每个类别生成路径参数
    for (const category of categories) {
      params.push({
        locale,
        id: category.id
      });
    }
  }

  return params;
}

/**
 * 生成所有文章的路径参数
 * @returns 文章路径参数
 */
export async function generateArticleParams() {
  const params = [];

  // 为每种语言获取文章
  for (const locale of locales) {
    const articles = await getArticles({}, locale);

    // 为每个文章生成路径参数
    for (const article of articles) {
      params.push({
        locale,
        slug: article.slug
      });
    }
  }

  return params;
}

/**
 * 生成工具的元数据
 * @param locale 语言
 * @param id 工具ID
 * @returns 元数据
 */
export async function generateToolMetadata(locale: Locale, id: string) {
  const tool = await getToolById(id, locale);

  if (!tool) {
    return {
      title: 'Tool Not Found',
      description: 'The requested tool could not be found.'
    };
  }

  return {
    title: `${tool.name} | X114 AI Tool Hub`,
    description: tool.description || '',
    keywords: [...(tool.tags || []), 'AI tool', tool.name, 'X114'],
    openGraph: {
      title: `${tool.name} | X114 AI Tool Hub`,
      description: tool.description || '',
      images: [
        {
          url: tool.image || '/images/placeholder.jpg',
          width: 1200,
          height: 630,
          alt: tool.name
        }
      ]
    }
  };
}

/**
 * 生成类别的元数据
 * @param locale 语言
 * @param id 类别ID
 * @returns 元数据
 */
export async function generateCategoryMetadata(locale: Locale, id: string) {
  const category = await getCategoryById(id, locale);

  if (!category) {
    return {
      title: 'Category Not Found',
      description: 'The requested category could not be found.'
    };
  }

  return {
    title: `${category.name} | X114 AI Tool Hub`,
    description: category.description || `Explore AI tools in the ${category.name} category.`,
    keywords: [category.name, 'AI tools', 'AI category', 'X114'],
    openGraph: {
      title: `${category.name} | X114 AI Tool Hub`,
      description: category.description || `Explore AI tools in the ${category.name} category.`
    }
  };
}

/**
 * 生成文章的元数据
 * @param locale 语言
 * @param slug 文章别名
 * @returns 元数据
 */
export async function generateArticleMetadata(locale: Locale, slug: string) {
  const article = await getArticleBySlug(slug, locale);

  if (!article) {
    return {
      title: 'Article Not Found',
      description: 'The requested article could not be found.'
    };
  }

  return {
    title: article.title,
    description: article.summary || '', // 使用 summary 而不是 description
    keywords: [...(article.tags || []), 'article', 'X114'],
    openGraph: {
      title: article.title,
      description: article.summary || '', // 使用 summary 而不是 description
      images: [
        {
          url: article.coverImage || '/images/placeholder.jpg', // 使用 coverImage 而不是 image
          width: 1200,
          height: 630,
          alt: article.title
        }
      ],
      type: 'article',
      publishedTime: article.publishedAt ? (typeof article.publishedAt === 'string' ? article.publishedAt : article.publishedAt.toISOString()) : new Date().toISOString(),
      modifiedTime: article.updatedAt ? (typeof article.updatedAt === 'string' ? article.updatedAt : article.updatedAt.toISOString()) : new Date().toISOString()
    }
  };
}
