'use client';

import React, { Component, ErrorInfo as ReactErrorInfo } from 'react';
import { ErrorInfo } from '@/lib/utils/monitoring';
import { isDevelopment } from '@/lib/utils/environment';

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  onError?: (errorInfo: ErrorInfo) => void;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

/**
 * 错误边界组件
 * 用于捕获 React 组件中的错误
 */
export default class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null
    };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    // 更新状态，以便下一次渲染显示回退 UI
    return {
      hasError: true,
      error
    };
  }

  componentDidCatch(error: Error, errorInfo: ReactErrorInfo): void {
    // 捕获错误信息
    const info: ErrorInfo = {
      message: error.message,
      error,
      componentStack: errorInfo.componentStack,
      timestamp: Date.now(),
      url: typeof window !== 'undefined' ? window.location.href : ''
    };

    // 如果提供了错误处理回调，则调用它
    if (this.props.onError) {
      this.props.onError(info);
    }

    // 在开发环境中打印错误
    if (isDevelopment()) {
      console.error('Error caught by ErrorBoundary:', error);
      console.error('Component stack:', errorInfo.componentStack);
    }
  }

  render(): React.ReactNode {
    if (this.state.hasError) {
      // 如果提供了回退 UI，则显示它
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // 否则，显示默认的错误 UI
      return (
        <div className="p-6 bg-red-50 dark:bg-red-900/20 rounded-lg">
          <h2 className="text-xl font-semibold text-red-700 dark:text-red-400 mb-2">
            Something went wrong
          </h2>
          {isDevelopment() && this.state.error && (
            <div className="mt-4">
              <p className="text-sm text-red-600 dark:text-red-300 font-mono">
                {this.state.error.message}
              </p>
              <details className="mt-2">
                <summary className="text-sm text-red-600 dark:text-red-300 cursor-pointer">
                  Stack trace
                </summary>
                <pre className="mt-2 p-2 bg-red-100 dark:bg-red-900/40 rounded text-xs overflow-auto max-h-40 text-red-600 dark:text-red-300">
                  {this.state.error.stack}
                </pre>
              </details>
            </div>
          )}
          <button
            className="mt-4 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded"
            onClick={() => window.location.reload()}
          >
            Reload page
          </button>
        </div>
      );
    }

    // 如果没有错误，则渲染子组件
    return this.props.children;
  }
}
