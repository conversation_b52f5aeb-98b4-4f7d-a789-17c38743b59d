import { Metadata } from 'next';
import { getDictionary } from '@/lib/i18n/get-dictionary';
import { Locale, i18n } from '@/lib/i18n/config';
import { getAllCategories } from '@/lib/db/categories';
import ToolSubmissionForm from '@/components/tools/tool-submission-form';
import { TopBannerAd, ContentRectangleAd } from '@/components/ads/client-ad-wrapper';

// 为静态导出配置
export const dynamic = 'force-static';
export const revalidate = 3600; // 每小时重新验证一次

// 为静态导出生成所有支持的语言路径
export function generateStaticParams() {
  return i18n.locales.map((locale) => ({ locale }));
}

export const metadata: Metadata = {
  title: 'Submit a Tool | X114',
  description: 'Submit your AI tool to our directory for others to discover',
};

export default async function SubmitToolPage({
  params
}: {
  params: Promise<{ locale: Locale }>
}) {
  // 在 Next.js 15+ 中，params 是异步的，需要使用 await
  const locale = (await params).locale;
  const dictionary = await getDictionary(locale);
  const categories = await getAllCategories(locale);

  return (
    <div className="container mx-auto px-4 py-24">
      <div className="max-w-3xl mx-auto">
        <div className="text-center mb-10">
          <h1 className="text-4xl font-bold mb-4">{dictionary.submit?.title || 'Submit Your AI Tool'}</h1>
          <p className="text-lg text-muted-foreground">
            {dictionary.submit?.description || 'Share your AI tool with our community and reach thousands of potential users.'}
          </p>
        </div>

        {/* 顶部横幅广告 */}
        <TopBannerAd adSlot="1620640133" />

        <ToolSubmissionForm categories={categories} locale={locale} dictionary={dictionary} />

        {/* 底部内容广告 */}
        <ContentRectangleAd adSlot="4626949563" className="mt-12" />
      </div>
    </div>
  );
}