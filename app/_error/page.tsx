/**
 * 自定义错误页面
 * 
 * 在静态导出模式下，Next.js需要一个自定义的错误页面
 */

import { Metadata } from 'next';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { AlertTriangle, Home, ArrowLeft } from 'lucide-react';

export const metadata: Metadata = {
  title: 'Error - X114 AI Tools Directory',
  description: 'An error occurred while loading the page.',
  robots: 'noindex,nofollow',
};

export default function ErrorPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-background">
      <div className="max-w-md w-full mx-auto text-center px-6">
        <div className="mb-8">
          <AlertTriangle className="h-16 w-16 text-destructive mx-auto mb-4" />
          <h1 className="text-4xl font-bold text-foreground mb-2">Oops!</h1>
          <h2 className="text-xl text-muted-foreground mb-4">Something went wrong</h2>
          <p className="text-muted-foreground">
            We encountered an error while loading this page. Please try again or return to the homepage.
          </p>
        </div>

        <div className="space-y-4">
          <Button 
            onClick={() => window.location.reload()} 
            className="w-full"
            variant="default"
          >
            Try Again
          </Button>
          
          <div className="flex gap-2">
            <Button 
              onClick={() => window.history.back()} 
              variant="outline" 
              className="flex-1"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Go Back
            </Button>
            
            <Link href="/" className="flex-1">
              <Button variant="outline" className="w-full">
                <Home className="h-4 w-4 mr-2" />
                Home
              </Button>
            </Link>
          </div>
        </div>

        <div className="mt-8 text-sm text-muted-foreground">
          <p>Error Code: STATIC_EXPORT_ERROR</p>
        </div>
      </div>
    </div>
  );
}
