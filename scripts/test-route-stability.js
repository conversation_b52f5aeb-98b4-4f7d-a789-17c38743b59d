#!/usr/bin/env node

/**
 * 路由稳定性测试工具
 * 重复测试同一 URL 多次，检测间歇性失败
 */

const https = require('https');
const http = require('http');

async function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const client = url.startsWith('https:') ? https : http;
    const startTime = Date.now();
    
    const request = client.get(url, options, (response) => {
      let data = '';
      
      response.on('data', (chunk) => {
        data += chunk;
      });
      
      response.on('end', () => {
        const endTime = Date.now();
        resolve({
          status: response.statusCode,
          headers: response.headers,
          contentType: response.headers['content-type'],
          contentLength: data.length,
          responseTime: endTime - startTime,
          timestamp: new Date().toISOString(),
          debugInfo: {
            routeDebug: response.headers['x-route-debug'],
            routeStatus: response.headers['x-route-status'],
            route404Path: response.headers['x-route-404-path']
          }
        });
      });
    });
    
    request.on('error', reject);
    request.setTimeout(10000, () => {
      request.destroy();
      reject(new Error(`Timeout for ${url}`));
    });
  });
}

async function testRouteStability(url, iterations = 10, delay = 100) {
  console.log(`Testing route stability: ${url}`);
  console.log(`Iterations: ${iterations}, Delay: ${delay}ms`);
  console.log('-'.repeat(60));
  
  const results = [];
  const statusCounts = {};
  const responseTimes = [];
  
  for (let i = 0; i < iterations; i++) {
    try {
      const result = await makeRequest(url);
      results.push(result);
      
      // 统计状态码
      statusCounts[result.status] = (statusCounts[result.status] || 0) + 1;
      responseTimes.push(result.responseTime);
      
      console.log(`${i + 1}/${iterations}: ${result.status} (${result.responseTime}ms) ${result.contentType || 'unknown'}`);
      
      if (result.debugInfo.routeDebug) {
        console.log(`  Debug: ${result.debugInfo.routeDebug}`);
      }
      
      if (result.status === 404 && result.debugInfo.route404Path) {
        console.log(`  404 Path: ${result.debugInfo.route404Path}`);
      }
      
      if (delay > 0 && i < iterations - 1) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    } catch (error) {
      console.log(`${i + 1}/${iterations}: ERROR - ${error.message}`);
      results.push({ error: error.message, timestamp: new Date().toISOString() });
    }
  }
  
  // 分析结果
  console.log('\n' + '='.repeat(60));
  console.log('STABILITY ANALYSIS:');
  console.log('='.repeat(60));
  
  // 状态码分布
  console.log('\nStatus Code Distribution:');
  Object.entries(statusCounts).forEach(([status, count]) => {
    const percentage = ((count / iterations) * 100).toFixed(1);
    console.log(`  ${status}: ${count}/${iterations} (${percentage}%)`);
  });
  
  // 响应时间统计
  if (responseTimes.length > 0) {
    const avgTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
    const minTime = Math.min(...responseTimes);
    const maxTime = Math.max(...responseTimes);
    
    console.log('\nResponse Time Statistics:');
    console.log(`  Average: ${avgTime.toFixed(1)}ms`);
    console.log(`  Min: ${minTime}ms`);
    console.log(`  Max: ${maxTime}ms`);
  }
  
  // 稳定性评估
  const uniqueStatuses = Object.keys(statusCounts).length;
  const isStable = uniqueStatuses === 1;
  const successRate = ((statusCounts['200'] || 0) / iterations * 100).toFixed(1);
  
  console.log('\nStability Assessment:');
  console.log(`  Stable: ${isStable ? '✅ YES' : '❌ NO'}`);
  console.log(`  Success Rate: ${successRate}%`);
  console.log(`  Unique Status Codes: ${uniqueStatuses}`);
  
  if (!isStable) {
    console.log('\n⚠️  INSTABILITY DETECTED!');
    console.log('This route shows inconsistent behavior.');
    console.log('Check Cloudflare Pages configuration and caching settings.');
  }
  
  return {
    url,
    iterations,
    results,
    statusCounts,
    responseTimes,
    isStable,
    successRate: parseFloat(successRate)
  };
}

async function testConcurrentRequests(url, concurrency = 5) {
  console.log(`\nTesting concurrent requests: ${url}`);
  console.log(`Concurrency: ${concurrency}`);
  console.log('-'.repeat(60));
  
  const promises = [];
  for (let i = 0; i < concurrency; i++) {
    promises.push(makeRequest(url));
  }
  
  try {
    const results = await Promise.all(promises);
    const statuses = results.map(r => r.status);
    const uniqueStatuses = [...new Set(statuses)];
    
    console.log('Concurrent Request Results:');
    results.forEach((result, index) => {
      console.log(`  Request ${index + 1}: ${result.status} (${result.responseTime}ms)`);
    });
    
    console.log(`\nConcurrency Test: ${uniqueStatuses.length === 1 ? '✅ PASSED' : '❌ FAILED'}`);
    console.log(`Unique Status Codes: ${uniqueStatuses.join(', ')}`);
    
    return uniqueStatuses.length === 1;
  } catch (error) {
    console.log(`❌ Concurrent test failed: ${error.message}`);
    return false;
  }
}

async function main() {
  const baseUrl = process.argv[2] || 'https://x114.org';
  const iterations = parseInt(process.argv[3]) || 10;
  
  console.log('Route Stability Testing Tool');
  console.log('='.repeat(60));
  console.log(`Base URL: ${baseUrl}`);
  console.log(`Test Iterations: ${iterations}`);
  console.log('');
  
  // 测试关键路径
  const testUrls = [
    `${baseUrl}/zh/category/image-generation-editing`,
    `${baseUrl}/zh/category/image-generation-editing/`,
    `${baseUrl}/en/category/coding-development`,
    `${baseUrl}/en/tool/force-com`,
    `${baseUrl}/zh/tools`,
    `${baseUrl}/en.txt`,
    `${baseUrl}/zh/category/image-generation-editing.txt`
  ];
  
  const results = [];
  
  for (const url of testUrls) {
    const result = await testRouteStability(url, iterations);
    results.push(result);
    
    // 测试并发请求
    await testConcurrentRequests(url, 3);
    
    console.log('\n' + '='.repeat(80) + '\n');
  }
  
  // 总结报告
  console.log('FINAL SUMMARY:');
  console.log('='.repeat(60));
  
  const stableRoutes = results.filter(r => r.isStable).length;
  const totalRoutes = results.length;
  const overallStability = ((stableRoutes / totalRoutes) * 100).toFixed(1);
  
  console.log(`Stable Routes: ${stableRoutes}/${totalRoutes} (${overallStability}%)`);
  
  const unstableRoutes = results.filter(r => !r.isStable);
  if (unstableRoutes.length > 0) {
    console.log('\nUnstable Routes:');
    unstableRoutes.forEach(route => {
      console.log(`  ❌ ${route.url} (${route.successRate}% success rate)`);
    });
  } else {
    console.log('\n✅ All routes are stable!');
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testRouteStability, testConcurrentRequests };